import * as React from "@rbxts/react";
import { UserInputService, RunService } from "@rbxts/services";
import { ActionBar, AbilitySlotData, ToastService, safeCall, GlobalErrorHandler } from "../../core";
import { ResponsiveManager } from "../../core/gui/layout/ResponsiveManager";
import { ABILITIES } from "../../shared/abilities/AbilityTypes";
import { RoomAbility } from "../abilities/RoomAbility";
import { QuakeAbility } from "../abilities/whitebeard/QuakeAbility";
import { HakiDominanceAbility } from "../abilities/HakiDominanceAbility";
import { IceAgeAbility } from "../abilities/IceAgeAbility";
import { FireFistAbility } from "../abilities/FireFistAbility";
import { ThreeSwordStyleAbility } from "../abilities/ThreeSwordStyleAbility";

interface ActionBarDemoProps {
	layoutOrder?: number;
}

export function ActionBarDemo(props: ActionBarDemoProps): React.ReactElement {
	// Debug: Log when ActionBarDemo renders
	print("🔥 ActionBarDemo is rendering!");

	// State to track cooldowns for each ability
	const [cooldowns, setCooldowns] = React.useState<Map<string, number>>(new Map());

	// State to track if abilities are initialized
	const [abilitiesInitialized, setAbilitiesInitialized] = React.useState(false);

	// Force re-render to update cooldown states
	const [, forceUpdate] = React.useReducer((x: number) => x + 1, 0);

	// State for QuakeAbility punch type selection
	const [selectedPunchType, setSelectedPunchType] = React.useState<"single" | "double">("single");

	// Initialize abilities as singletons to avoid duplication
	const roomAbility = React.useRef<RoomAbility>();
	const quakeAbility = React.useRef<QuakeAbility>();
	const hakiDominanceAbility = React.useRef<HakiDominanceAbility>();
	const iceAgeAbility = React.useRef<IceAgeAbility>();
	const fireFistAbility = React.useRef<FireFistAbility>();
	const threeSwordStyleAbility = React.useRef<ThreeSwordStyleAbility>();

	// Initialize abilities only once with error handling
	React.useEffect(() => {
		const initializeAbilities = () => {
			let allInitialized = true;

			if (!roomAbility.current) {
				const instance = safeCall(
					() => new RoomAbility(),
					"ActionBarDemo",
					"initialize_RoomAbility",
					undefined
				);
				
				if (instance) {
					roomAbility.current = instance;
					print("✅ RoomAbility initialized successfully");
				} else {
					warn("❌ Failed to initialize RoomAbility");
					allInitialized = false;
				}
			}

			if (!quakeAbility.current) {
				const instance = safeCall(
					() => new QuakeAbility(),
					"ActionBarDemo",
					"initialize_QuakeAbility",
					undefined
				);
				
				if (instance) {
					quakeAbility.current = instance;
					print("✅ QuakeAbility initialized successfully");
				} else {
					warn("❌ Failed to initialize QuakeAbility");
					allInitialized = false;
				}
			}

			if (!hakiDominanceAbility.current) {
				const instance = safeCall(
					() => new HakiDominanceAbility(),
					"ActionBarDemo",
					"initialize_HakiDominanceAbility",
					undefined
				);
				
				if (instance) {
					hakiDominanceAbility.current = instance;
					print("✅ HakiDominanceAbility initialized successfully");
				} else {
					warn("❌ Failed to initialize HakiDominanceAbility");
					allInitialized = false;
				}
			}

			if (!iceAgeAbility.current) {
				const instance = safeCall(
					() => new IceAgeAbility(),
					"ActionBarDemo",
					"initialize_IceAgeAbility",
					undefined
				);
				
				if (instance) {
					iceAgeAbility.current = instance;
					print("✅ IceAgeAbility initialized successfully");
				} else {
					warn("❌ Failed to initialize IceAgeAbility");
					allInitialized = false;
				}
			}

			if (!fireFistAbility.current) {
				const instance = safeCall(
					() => new FireFistAbility(),
					"ActionBarDemo",
					"initialize_FireFistAbility",
					undefined
				);
				
				if (instance) {
					fireFistAbility.current = instance;
					print("✅ FireFistAbility initialized successfully");
				} else {
					warn("❌ Failed to initialize FireFistAbility");
					allInitialized = false;
				}
			}

			if (!threeSwordStyleAbility.current) {
				const instance = safeCall(
					() => new ThreeSwordStyleAbility(),
					"ActionBarDemo",
					"initialize_ThreeSwordStyleAbility",
					undefined
				);
				
				if (instance) {
					threeSwordStyleAbility.current = instance;
					print("✅ ThreeSwordStyleAbility initialized successfully");
				} else {
					warn("❌ Failed to initialize ThreeSwordStyleAbility");
					allInitialized = false;
				}
			}

			return allInitialized;
		};

		const success = initializeAbilities();
		setAbilitiesInitialized(success);
		
		if (success) {
			print("✅ All abilities initialized successfully");
		} else {
			warn("⚠️ Some abilities failed to initialize");
		}
	}, []);

	// Clean up expired cooldowns and force re-render
	React.useEffect(() => {
		// Only run the effect if there are active cooldowns
		if (cooldowns.size() === 0) return;

		// Check every 100ms instead of every frame for better performance
		let lastCheckTime = tick();
		const checkInterval = 0.1; // 100ms

		const connection = RunService.Heartbeat.Connect(() => {
			const currentTime = tick();

			// Only check every 100ms
			if (currentTime - lastCheckTime < checkInterval) return;
			lastCheckTime = currentTime;

			let hasExpiredCooldowns = false;

			setCooldowns((prev) => {
				// If no cooldowns, nothing to update
				if (prev.size() === 0) return prev;

				const newMap = new Map<string, number>();
				prev.forEach((endTime, abilityId) => {
					if (currentTime < endTime) {
						// Cooldown still active
						newMap.set(abilityId, endTime);
					} else {
						// Cooldown expired
						hasExpiredCooldowns = true;
						print(`✅ Cooldown for ${abilityId} has expired!`);
					}
				});
				return newMap;
			});

			if (hasExpiredCooldowns) {
				forceUpdate();
			}
		});

		return () => connection.Disconnect();
	}, [cooldowns, forceUpdate]);

	// Get current punch type display info
	const getPunchTypeInfo = (punchType: "single" | "double") => {
		return punchType === "single" ? { text: "Single Punch", icon: "👊" } : { text: "Cross Punch", icon: "🥊" };
	};

	const currentPunchInfo = getPunchTypeInfo(selectedPunchType);

	// Sample abilities for the action bar (5 slots)
	const abilitySlots: AbilitySlotData[] = [
		{
			id: "ROOM",
			name: ABILITIES.ROOM.name,
			icon: "🔵", // Blue circle for Room
			cooldownEndTime: cooldowns.get("ROOM"),
			isOnCooldown: cooldowns.has("ROOM") && tick() < (cooldowns.get("ROOM") ?? 0),
			totalCooldownDuration: ABILITIES.ROOM.cooldown,
		},
		{
			id: "QUAKE_PUNCH",
			name: `${ABILITIES.QUAKE_PUNCH.name} (${currentPunchInfo.text})`,
			icon: currentPunchInfo.icon, // Dynamic icon based on selection
			cooldownEndTime: cooldowns.get("QUAKE_PUNCH"),
			isOnCooldown: cooldowns.has("QUAKE_PUNCH") && tick() < (cooldowns.get("QUAKE_PUNCH") ?? 0),
			totalCooldownDuration: ABILITIES.QUAKE_PUNCH.cooldown,
		},
		{
			id: "ICE_AGE",
			name: ABILITIES.ICE_AGE.name,
			icon: "❄️", // Ice snowflake icon
			cooldownEndTime: cooldowns.get("ICE_AGE"),
			isOnCooldown: cooldowns.has("ICE_AGE") && tick() < (cooldowns.get("ICE_AGE") ?? 0),
			totalCooldownDuration: ABILITIES.ICE_AGE.cooldown,
		},
		{
			id: "FIRE_FIST",
			name: ABILITIES.FIRE_FIST.name,
			icon: "🔥", // Fire icon
			cooldownEndTime: cooldowns.get("FIRE_FIST"),
			isOnCooldown: cooldowns.has("FIRE_FIST") && tick() < (cooldowns.get("FIRE_FIST") ?? 0),
			totalCooldownDuration: ABILITIES.FIRE_FIST.cooldown,
		},
		{
			id: "THREE_SWORD_STYLE",
			name: ABILITIES.THREE_SWORD_STYLE.name,
			icon: "⚔️", // Crossed swords for Zoro's technique
			cooldownEndTime: cooldowns.get("THREE_SWORD_STYLE"),
			isOnCooldown: cooldowns.has("THREE_SWORD_STYLE") && tick() < (cooldowns.get("THREE_SWORD_STYLE") ?? 0),
			totalCooldownDuration: ABILITIES.THREE_SWORD_STYLE.cooldown,
		},
	];

	const handleSlotClick = (slotIndex: number, abilityId: string) => {
		const ability = ABILITIES[abilityId];
		if (!ability) {
			print(`Unknown ability: ${abilityId}`);
			return;
		}

		// Check if ability is on cooldown
		const currentTime = tick();
		const cooldownEndTime = cooldowns.get(abilityId);
		if (cooldownEndTime !== undefined && cooldownEndTime > 0 && currentTime < cooldownEndTime) {
			const remainingTime = math.ceil(cooldownEndTime - currentTime);
			print(`${ability.name} is on cooldown! ${remainingTime} seconds remaining.`);
			ToastService.showWarning("Ability on Cooldown", `${ability.name} - ${remainingTime}s remaining`);
			return;
		}

		// Special handling for specific abilities with safety checks
		try {
			if (abilityId === "ROOM") {
				if (roomAbility.current) {
					roomAbility.current.activate();
					print(`🔵 Casting ${ability.name} from slot ${slotIndex + 1}!`);
					ToastService.showSuccess("Room Activated", "Spatial manipulation field created");
				} else {
					warn("❌ RoomAbility not initialized");
					ToastService.showError("Error", "Room ability not ready");
					return;
				}
			} else if (abilityId === "QUAKE_PUNCH") {
				if (!quakeAbility.current) {
					warn("❌ QuakeAbility not initialized");
					ToastService.showError("Error", "Quake ability not ready");
					return;
				}

				// Check if shift is held to cycle punch type
				const isShiftHeld =
					UserInputService.IsKeyDown(Enum.KeyCode.LeftShift) ||
					UserInputService.IsKeyDown(Enum.KeyCode.RightShift);

				if (isShiftHeld) {
					// Cycle punch type instead of activating
					const newType = selectedPunchType === "single" ? "double" : "single";
					setSelectedPunchType(newType);
					const newInfo = getPunchTypeInfo(newType);
					print(`🔄 Switched to ${newInfo.text} ${newInfo.icon}`);
					ToastService.showInfo("Punch Type Changed", `Now using: ${newInfo.text}`);
					return; // Don't set cooldown when just cycling
				} else {
					// Activate with current punch type
					quakeAbility.current.activate(selectedPunchType);
					print(
						`${currentPunchInfo.icon} Casting ${ability.name} (${currentPunchInfo.text}) from slot ${slotIndex + 1}!`,
					);
					ToastService.showSuccess(
						`${currentPunchInfo.icon} Quake Punch`,
						`${currentPunchInfo.text} unleashed!`,
					);
				}
			} else if (abilityId === "THREE_SWORD_STYLE") {
				if (threeSwordStyleAbility.current) {
					threeSwordStyleAbility.current.activate();
					print(`⚔️ Casting ${ability.name} - Zoro's legendary sword technique from slot ${slotIndex + 1}!`);
					ToastService.showSuccess("⚔️ Three Sword Style", "Santoryu technique activated!");
				} else {
					warn("❌ ThreeSwordStyleAbility not initialized");
					ToastService.showError("Error", "Three Sword Style ability not ready");
					return;
				}
			} else if (abilityId === "ICE_AGE") {
				if (iceAgeAbility.current) {
					iceAgeAbility.current.activate();
					print(`❄️ Casting ${ability.name} - Aokiji's ultimate ice technique from slot ${slotIndex + 1}!`);
					ToastService.showSuccess("❄️ Ice Age", "Freezing the battlefield...");
				} else {
					warn("❌ IceAgeAbility not initialized");
					ToastService.showError("Error", "Ice Age ability not ready");
					return;
				}
			} else if (abilityId === "FIRE_FIST") {
				if (fireFistAbility.current) {
					fireFistAbility.current.activate();
					print(`🔥 Casting ${ability.name} - Ace's signature fire attack from slot ${slotIndex + 1}!`);
					ToastService.showSuccess("🔥 Fire Fist", "Ace's burning attack unleashed!");
				} else {
					warn("❌ FireFistAbility not initialized");
					ToastService.showError("Error", "Fire Fist ability not ready");
					return;
				}
			} else if (abilityId === "HAKI_DOMINANCE") {
				if (hakiDominanceAbility.current) {
					hakiDominanceAbility.current.activate();
					print(`🔴 Casting ${ability.name} - Rayleigh's overwhelming Haki from slot ${slotIndex + 1}!`);
					ToastService.showSuccess("🔴 Haki Dominance", "Overwhelming presence activated!");
				} else {
					warn("❌ HakiDominanceAbility not initialized");
					ToastService.showError("Error", "Haki Dominance ability not ready");
					return;
				}
			} else {
				// Simulate ability cast for other abilities
				print(`🔥 Casting ${ability.name} from slot ${slotIndex + 1}!`);
				ToastService.showSuccess("Ability Activated", ability.name);
			}

			// Set cooldown
			const newCooldownEndTime = currentTime + ability.cooldown;
			setCooldowns((prev) => {
				const newMap = new Map<string, number>();
				// Copy existing entries
				prev.forEach((value, key) => {
					newMap.set(key, value);
				});
				// Set new cooldown
				newMap.set(abilityId, newCooldownEndTime);
				return newMap;
			});
		} catch (error) {
			warn(`❌ Error activating ability ${abilityId}: ${error}`);
			ToastService.showError("Ability Error", `Failed to activate ${ability.name}`);
		}
	};

	// Handle keyboard input for ability slots (1, 2, 3, 4, 5)
	React.useEffect(() => {
		const connection = UserInputService.InputBegan.Connect((input, gameProcessed) => {
			if (gameProcessed) return;

			if (input.UserInputType === Enum.UserInputType.Keyboard) {
				const keyCode = input.KeyCode;
				let slotIndex = -1;

				// Map number keys to slot indices
				if (keyCode === Enum.KeyCode.One) slotIndex = 0;
				else if (keyCode === Enum.KeyCode.Two) slotIndex = 1;
				else if (keyCode === Enum.KeyCode.Three) slotIndex = 2;
				else if (keyCode === Enum.KeyCode.Four) slotIndex = 3;
				else if (keyCode === Enum.KeyCode.Five) slotIndex = 4;

				// Trigger ability if valid slot
				if (slotIndex >= 0 && slotIndex < abilitySlots.size()) {
					const slot = abilitySlots[slotIndex];
					handleSlotClick(slotIndex, slot.id);
				}
			}
		});

		return () => connection.Disconnect();
	}, [abilitySlots, handleSlotClick, selectedPunchType]);

	// Get responsive manager for dynamic sizing
	const responsiveManager = ResponsiveManager.getInstance();
	const safeAreaInsets = responsiveManager.getSafeAreaInsets();

	// Calculate responsive container size and position
	const containerWidth = responsiveManager.isMobile() ? 320 : 400;
	const containerHeight = responsiveManager.isMobile() ? 70 : 80;

	// Reduce the bottom margin - the original was 120px from bottom, let's use a smaller base margin
	const baseBottomMargin = responsiveManager.isMobile() ? 80 : 100; // Reduced from 40 + safe area
	const bottomMargin = baseBottomMargin + (responsiveManager.isMobile() ? safeAreaInsets.bottom * 0.5 : 0);

	// Don't render the action bar until abilities are initialized
	if (!abilitiesInitialized) {
		return (
			<frame
				BackgroundTransparency={1}
				Size={new UDim2(1, 0, 1, 0)}
				Position={new UDim2(0, 0, 0, 0)}
				BorderSizePixel={0}
			>
				<textlabel
					Text="Initializing abilities..."
					Size={new UDim2(0, 200, 0, 30)}
					Position={new UDim2(0.5, 0, 1, -50)}
					AnchorPoint={new Vector2(0.5, 0)}
					BackgroundTransparency={1}
					TextColor3={Color3.fromRGB(255, 255, 255)}
					TextSize={14}
					Font={Enum.Font.SourceSans}
					TextXAlignment={Enum.TextXAlignment.Center}
				/>
			</frame>
		);
	}

	return (
		<frame
			BackgroundTransparency={1}
			Size={new UDim2(1, 0, 1, 0)} // Full screen
			Position={new UDim2(0, 0, 0, 0)}
			BorderSizePixel={0}
		>
			{/* ActionBar Container - Responsive bottom center */}
			<frame
				BackgroundTransparency={1}
				Size={new UDim2(0, containerWidth, 0, containerHeight)}
				Position={new UDim2(0.5, 0, 1, -bottomMargin)}
				AnchorPoint={new Vector2(0.5, 0)}
				BorderSizePixel={0}
			>
				{/* Add size constraints for responsive behavior */}
				<uisizeconstraint MinSize={new Vector2(280, 60)} MaxSize={new Vector2(500, 100)} />

				<ActionBar
					slots={abilitySlots}
					onSlotClick={handleSlotClick}
					layoutOrder={props.layoutOrder}
					position={new UDim2(0.5, 0, 0.5, 0)} // Center within container
					anchorPoint={new Vector2(0.5, 0.5)}
					zIndex={100} // Ensure action bar is always visible
				/>
			</frame>
		</frame>
	);
}
