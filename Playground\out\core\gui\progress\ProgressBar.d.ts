import * as React from "@rbxts/react";
interface ProgressBarProps {
    progress: number;
    barColor?: Color3;
    backgroundColor?: Color3;
    size?: UDim2;
    cornerRadius?: UDim;
    borderSize?: number;
    borderColor?: Color3;
    layoutOrder?: number;
    position?: UDim2;
    anchorPoint?: Vector2;
}
export declare function ProgressBar({ progress, barColor, backgroundColor, size, cornerRadius, borderSize, borderColor, layoutOrder, position, anchorPoint, }: ProgressBarProps): React.ReactElement;
export {};
