-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local exports = {}
local _QuestManager = TS.import(script, game:GetService("ReplicatedStorage"), "core", "quests", "QuestManager")
exports.Quest = _QuestManager.Quest
exports.QuestManager = _QuestManager.QuestManager
exports.QuestSystem = _QuestManager.QuestSystem
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "quests", "types") or {} do
	exports[_k] = _v
end
return exports
