import { BaseService } from "../foundation/BaseService";
import { Result } from "../foundation/types/Result";
import { Error } from "../foundation/types/BrandedTypes";
import { QuestDefinition, QuestProgress, QuestEvent, QuestConfiguration, QuestObjective, QuestType } from "./types";
/**
 * Individual quest instance with progress tracking
 */
export declare class Quest {
    private definition;
    private progress;
    private eventCallbacks;
    constructor(definition: QuestDefinition);
    /**
     * Start the quest
     */
    start(): Result<boolean, string>;
    /**
     * Complete the quest
     */
    complete(force?: boolean): Result<boolean, string>;
    /**
     * Fail the quest
     */
    fail(reason?: string): Result<boolean, string>;
    /**
     * Abandon the quest
     */
    abandon(): Result<boolean, string>;
    /**
     * Update objective progress
     */
    updateObjective(objectiveId: string, increment?: number): Result<boolean, string>;
    /**
     * Set objective progress directly
     */
    setObjectiveProgress(objectiveId: string, count: number): Result<boolean, string>;
    /**
     * Check if quest has expired
     */
    checkExpiration(): boolean;
    /**
     * Check if quest can be repeated
     */
    canRepeat(): boolean;
    /**
     * Reset quest for repetition
     */
    reset(): Result<boolean, string>;
    /**
     * Get quest definition
     */
    getDefinition(): QuestDefinition;
    /**
     * Get quest progress
     */
    getProgress(): QuestProgress;
    private cloneObjectivesMap;
    private objectivesMapToArray;
    /**
     * Get completion percentage
     */
    getCompletionPercentage(): number;
    /**
     * Get visible objectives (not hidden)
     */
    getVisibleObjectives(): QuestObjective[];
    /**
     * Get completed objectives
     */
    getCompletedObjectives(): QuestObjective[];
    /**
     * Get remaining time in seconds
     */
    getRemainingTime(): number | undefined;
    /**
     * Add event listener
     */
    onEvent(callback: (event: QuestEvent) => void): () => void;
    private areRequiredObjectivesCompleted;
    private fireEvent;
}
/**
 * Quest Manager service for managing all quests
 */
export declare class QuestManager extends BaseService {
    private static instance;
    private questDefinitions;
    private activeQuests;
    private completedQuests;
    private config;
    private eventCallbacks;
    constructor();
    static getInstance(): QuestManager;
    protected onInitialize(): Promise<Result<void, Error>>;
    protected onShutdown(): Promise<Result<void, Error>>;
    /**
     * Register a quest definition
     */
    registerQuest(definition: QuestDefinition): Result<void, string>;
    /**
     * Start a quest
     */
    startQuest(questId: string): Result<Quest, string>;
    /**
     * Complete a quest
     */
    completeQuest(questId: string): Result<boolean, string>;
    /**
     * Fail a quest
     */
    failQuest(questId: string, reason?: string): Result<boolean, string>;
    /**
     * Abandon a quest
     */
    abandonQuest(questId: string): Result<boolean, string>;
    /**
     * Update quest objective progress
     */
    updateQuestObjective(questId: string, objectiveId: string, increment?: number): Result<boolean, string>;
    /**
     * Process a game event to update quest progress
     */
    processGameEvent(eventType: string, data: Record<string, unknown>): void;
    /**
     * Get an active quest
     */
    getQuest(questId: string): Quest | undefined;
    private activeQuestsToArray;
    /**
     * Get all active quests
     */
    getActiveQuests(): Quest[];
    /**
     * Get quests by type
     */
    getQuestsByType(questType: QuestType): Quest[];
    /**
     * Get available quests (not started but prerequisites met)
     */
    getAvailableQuests(): QuestDefinition[];
    /**
     * Check for expired quests
     */
    checkExpiredQuests(): string[];
    /**
     * Update configuration
     */
    updateConfig(newConfig: Partial<QuestConfiguration>): void;
    /**
     * Add global event listener
     */
    onEvent(callback: (event: QuestEvent) => void): () => void;
    /**
     * Generate a comprehensive report
     */
    getReport(): string;
    private getObjectKeysCount;
    private handleQuestEvent;
    private checkAutoStartQuests;
    private doesEventMatchObjective;
    private loadDefaultQuests;
}
export declare const QuestSystem: {
    getInstance: () => QuestManager;
    startQuest: (questId: string) => Result<Quest, string>;
    getQuest: (questId: string) => Quest | undefined;
    processGameEvent: (eventType: string, data: Record<string, unknown>) => void;
    getReport: () => string;
};
