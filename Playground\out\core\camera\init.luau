-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local exports = {}
local _CameraManager = TS.import(script, game:GetService("ReplicatedStorage"), "core", "camera", "CameraManager")
exports.CameraManager = _CameraManager.CameraManager
exports.Camera = _CameraManager.Camera
exports.CameraMode = TS.import(script, game:GetService("ReplicatedStorage"), "core", "camera", "CameraManager").CameraMode
return exports
