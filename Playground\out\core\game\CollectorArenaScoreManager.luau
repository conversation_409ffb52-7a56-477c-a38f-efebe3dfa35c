-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local Players = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "services").Players
local CollectorArenaScoreManager
do
	CollectorArenaScoreManager = setmetatable({}, {
		__tostring = function()
			return "CollectorArenaScoreManager"
		end,
	})
	CollectorArenaScoreManager.__index = CollectorArenaScoreManager
	function CollectorArenaScoreManager.new(...)
		local self = setmetatable({}, CollectorArenaScoreManager)
		return self:constructor(...) or self
	end
	function CollectorArenaScoreManager:constructor()
		-- Simple in-memory storage for now
		-- TODO: Integrate with proper DataStore when needed
		self.localScores = {}
		-- Simple in-memory storage for now
		-- TODO: Integrate with proper DataStore when needed
	end
	function CollectorArenaScoreManager:getInstance()
		if not CollectorArenaScoreManager.instance then
			CollectorArenaScoreManager.instance = CollectorArenaScoreManager.new()
		end
		return CollectorArenaScoreManager.instance
	end
	CollectorArenaScoreManager.saveScore = TS.async(function(self, gameScore)
		local _exitType, _returns = TS.try(function()
			local player = Players.LocalPlayer
			if not player then
				return TS.TRY_RETURN, { false }
			end
			local _object = table.clone(gameScore)
			setmetatable(_object, nil)
			_object.timestamp = tick()
			local scoreData = _object
			-- Save to local storage (in-memory for now)
			local _exp = self.localScores
			table.insert(_exp, 1, scoreData)
			-- Keep only last 10 games
			if #self.localScores > 10 then
				-- Remove oldest entries
				while #self.localScores > 10 do
					local _exp_1 = self.localScores
					_exp_1[#_exp_1] = nil
				end
			end
			-- Update personal best
			if not self.personalBest or self.personalBest.score < gameScore.score then
				self.personalBest = scoreData
				print(`🏆 New personal best: {gameScore.score} points!`)
			end
			print(`💾 Score saved locally: {gameScore.score} points`)
			return TS.TRY_RETURN, { true }
		end, function(error)
			warn(`Failed to save score: {error}`)
			return TS.TRY_RETURN, { false }
		end)
		if _exitType then
			return unpack(_returns)
		end
	end)
	CollectorArenaScoreManager.getPersonalBest = TS.async(function(self)
		return self.personalBest
	end)
	CollectorArenaScoreManager.getRecentGames = TS.async(function(self)
		-- Convert to regular JavaScript array for easier handling
		local games = {}
		for _, score in self.localScores do
			table.insert(games, score)
		end
		return games
	end)
end
return {
	CollectorArenaScoreManager = CollectorArenaScoreManager,
}
