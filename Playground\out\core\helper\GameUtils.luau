-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
-- Core utility exports for game development
local MathUtils = TS.import(script, game:GetService("ReplicatedStorage"), "core", "helper", "MathUtils").MathUtils
local StringUtils = TS.import(script, game:GetService("ReplicatedStorage"), "core", "helper", "StringUtils").StringUtils
local TimeUtils = TS.import(script, game:GetService("ReplicatedStorage"), "core", "helper", "TimeUtils").TimeUtils
local TableUtils = TS.import(script, game:GetService("ReplicatedStorage"), "core", "helper", "TableUtils").TableUtils
local PositionHelper = TS.import(script, game:GetService("ReplicatedStorage"), "core", "helper", "PositionHelper").PositionHelper
--[[
	*
	 * Convenience re-exports for common operations
	 
]]
local Utils = {
	Math = MathUtils,
	String = StringUtils,
	Time = TimeUtils,
	Table = TableUtils,
	Position = PositionHelper,
}
--[[
	*
	 * Quick utility functions for common tasks
	 
]]
local QuickUtils = {
	formatNumber = function(num)
		return StringUtils:formatNumber(num)
	end,
	formatTime = function(seconds)
		return StringUtils:formatTime(seconds)
	end,
	random = function(min, max)
		return math.random() * (max - min) + min
	end,
	randomInt = function(min, max)
		return math.floor(math.random() * (max - min + 1)) + min
	end,
	clamp = function(value, min, max)
		return MathUtils:clamp(value, min, max)
	end,
	lerp = function(a, b, t)
		return MathUtils:lerp(a, b, t)
	end,
	inRange = function(pos1, pos2, range)
		return MathUtils:distance(pos1, pos2) <= range
	end,
	distance = function(pos1, pos2)
		return MathUtils:distance(pos1, pos2)
	end,
	round = function(value, decimals)
		if decimals == nil then
			decimals = 0
		end
		return MathUtils:round(value, decimals)
	end,
	copy = function(obj)
		return TableUtils:deepCopy(obj)
	end,
	waitFor = function(condition, timeout)
		if timeout == nil then
			timeout = 10
		end
		return TimeUtils:waitFor(condition, timeout)
	end,
	generateId = function()
		return StringUtils:generateId()
	end,
	isEmpty = function(str)
		return (string.gsub(str, "%s", "")) == ""
	end,
	capitalize = function(str)
		return string.upper(string.sub(str, 1, 1)) .. string.lower(string.sub(str, 2))
	end,
	gameTime = function()
		return TimeUtils:getGameTime()
	end,
	playerName = function(player)
		return StringUtils:formatPlayerName(player)
	end,
	safeColor = function(hex, fallback)
		if fallback == nil then
			fallback = "#FFFFFF"
		end
		local _exitType, _returns = TS.try(function()
			return TS.TRY_RETURN, { Color3.fromHex(hex) }
		end, function()
			return TS.TRY_RETURN, { Color3.fromHex(fallback) }
		end)
		if _exitType then
			return unpack(_returns)
		end
	end,
	exists = function(value)
		return value ~= nil
	end,
	default = function(value, defaultValue)
		return if value ~= nil then value else defaultValue
	end,
}
print("🔧 Core utilities loaded: Math, String, Time, Table, Position helpers")
print("💡 Access via Utils.Math, Utils.String, etc. or QuickUtils for common functions")
return {
	MathUtils = MathUtils,
	StringUtils = StringUtils,
	TimeUtils = TimeUtils,
	TableUtils = TableUtils,
	PositionHelper = PositionHelper,
	Utils = Utils,
	QuickUtils = QuickUtils,
}
