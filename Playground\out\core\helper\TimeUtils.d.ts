/**
 * Time and timing utilities for game development
 */
export declare class TimeUtils {
    private static timers;
    private static frameCallbacks;
    private static gameStartTime;
    /**
     * Get current game time in seconds since game start
     */
    static getGameTime(): number;
    /**
     * Get current Unix timestamp
     */
    static getUnixTime(): number;
    /**
     * Get formatted current date/time
     */
    static getCurrentDateTime(): string;
    /**
     * Get formatted current date
     */
    static getCurrentDate(): string;
    /**
     * Get formatted current time
     */
    static getCurrentTime(): string;
    /**
     * Convert seconds to human readable duration
     */
    static formatDuration(seconds: number): string;
    /**
     * Format duration for display (e.g., "2 hours 30 minutes")
     */
    static formatDurationLong(seconds: number): string;
    /**
     * Check if enough time has passed since last check
     */
    static hasElapsed(key: string, requiredInterval: number): boolean;
    /**
     * Simple interval timer (manual update required)
     */
    static setInterval(key: string, callback: () => void, interval: number): void;
    /**
     * Clear interval timer
     */
    static clearInterval(key: string): void;
    /**
     * Update all interval timers (call this in your main loop)
     */
    static updateTimers(): void;
    /**
     * Register a callback to run every frame
     */
    static onFrame(callback: () => void): void;
    /**
     * Remove frame callback
     */
    static offFrame(callback: () => void): void;
    /**
     * Call all frame callbacks (call this in RunService.Heartbeat)
     */
    static updateFrame(): void;
    /**
     * Create a countdown timer
     */
    static createCountdown(duration: number, onTick?: (remaining: number) => void, onComplete?: () => void): {
        getRemaining: () => number;
        isComplete: () => boolean;
        cancel: () => void;
    };
    /**
     * Create a stopwatch
     */
    static createStopwatch(): {
        start: () => void;
        stop: () => void;
        reset: () => void;
        getElapsed: () => number;
        isRunning: () => boolean;
    };
    /**
     * Calculate FPS based on frame time
     */
    static calculateFPS(deltaTime: number): number;
    /**
     * Create an FPS counter
     */
    static createFPSCounter(): {
        update: (deltaTime: number) => void;
        getFPS: () => number;
        getAverageFPS: () => number;
    };
    /**
     * Wait for a condition to be true (with timeout)
     */
    static waitFor(condition: () => boolean, timeout?: number, checkInterval?: number): Promise<boolean>;
    /**
     * Throttle function calls (limit frequency)
     */
    static throttle<T extends unknown[]>(key: string, func: (...args: T) => void, limit: number): (...args: T) => void;
    /**
     * Debounce function calls (delay until no calls for specified time)
     */
    static debounce<T extends unknown[]>(key: string, func: (...args: T) => void, delay: number): (...args: T) => void;
    /**
     * Get time until next hour/day/etc
     */
    static getTimeUntilNext(unit: "minute" | "hour" | "day"): number;
    /**
     * Clean up all timers and callbacks
     */
    static cleanup(): void;
}
