import { AIBehavior } from "../interfaces/AIBehavior";
import { AIContext } from "../interfaces/AIContext";
import { AIBehaviorResult } from "../interfaces/AIBehaviorResult";
import { PositionHelper } from "../../helper/PositionHelper";

export class FollowBehavior implements AIBehavior {
	name = "Follow";
	priority = 5;

	canExecute(context: AIContext): boolean {
		if (!context.target || !context.targetPosition) return false;

		const distance = context.position.sub(context.targetPosition).Magnitude;
		const maxFollowRange = context.blackboard.followRange as number || 30;
		const minFollowDistance = context.blackboard.minFollowDistance as number || 5;
		
		return distance <= maxFollowRange && distance > minFollowDistance;
	}

	execute(context: AIContext): AIBehaviorResult {
		if (!context.target || !context.targetPosition) {
			return { success: false, completed: true };
		}

		const distance = context.position.sub(context.targetPosition).Magnitude;
		const minFollowDistance = context.blackboard.minFollowDistance as number || 5;
		const optimalDistance = context.blackboard.optimalFollowDistance as number || 8;

		// Enhanced follow behavior with intelligent positioning
		if (distance > optimalDistance) {
			// Move closer if too far
			this.moveTowards(context, context.targetPosition);
		} else if (distance < minFollowDistance) {
			// Move away if too close (avoid crowding)
			const retreatDirection = context.position.sub(context.targetPosition).Unit;
			const retreatPosition = context.position.add(retreatDirection.mul(3));
			this.moveTowards(context, retreatPosition);
		} else {
			// At optimal distance - match target movement
			this.matchTargetMovement(context);
		}

		// Always face the target for better interaction
		PositionHelper.lookAt(context.entity, context.targetPosition);

		// Update follow persistence
		const followTime = (context.blackboard.followTime as number || 0) + context.deltaTime;
		context.blackboard.followTime = followTime;

		return { 
			success: true, 
			completed: distance <= minFollowDistance,
			nextBehavior: distance > 50 ? "Wander" : undefined // Give up following if target too far
		};
	}

	onEnter(context: AIContext): void {
		print(`🏃 ${context.entityId} is following target at distance ${math.floor(context.position.sub(context.targetPosition!).Magnitude)}`);
		context.blackboard.followTime = 0;
		context.blackboard.lastTargetPosition = context.targetPosition;
	}

	onExit(context: AIContext): void {
		print(`🏃 ${context.entityId} stopped following`);
	}

	private moveTowards(context: AIContext, targetPosition: Vector3): void {
		// Enhanced pathfinding with obstacle avoidance
		const clearTarget = PositionHelper.findClearPath(context.position, targetPosition, [context.entity]);
		const actualTarget = this.avoidOtherEntities(context, clearTarget);

		if (context.entity.IsA("Model") && context.entity.PrimaryPart) {
			const humanoid = context.entity.FindFirstChild("Humanoid") as Humanoid;
			if (humanoid) {
				// Enhanced humanoid movement with speed adjustment
				const distance = context.position.sub(actualTarget).Magnitude;
				const moveSpeed = context.blackboard.moveSpeed as number || 16;
				
				// Adjust speed based on distance (slow down when close, speed up when far)
				if (distance > 20) {
					humanoid.WalkSpeed = moveSpeed * 1.2; // Speed up when far
				} else if (distance < 10) {
					humanoid.WalkSpeed = moveSpeed * 0.7; // Slow down when close
				} else {
					humanoid.WalkSpeed = moveSpeed;
				}
				
				humanoid.MoveTo(actualTarget);
			} else {
				this.smoothMoveTo(context.entity.PrimaryPart, actualTarget, context.deltaTime);
			}
		} else if (context.entity.IsA("BasePart")) {
			this.smoothMoveTo(context.entity as BasePart, actualTarget, context.deltaTime);
		}
	}

	private matchTargetMovement(context: AIContext): void {
		// Try to predict where target is going and position accordingly
		const lastTargetPos = context.blackboard.lastTargetPosition as Vector3;
		const currentTargetPos = context.targetPosition!;
		
		if (lastTargetPos) {
			const targetVelocity = currentTargetPos.sub(lastTargetPos);
			const targetSpeed = targetVelocity.Magnitude;
			
			if (targetSpeed > 2) { // Target is moving
				// Position slightly behind and to the side
				const targetDirection = targetVelocity.Unit;
				const sideOffset = new Vector3(-targetDirection.Z, 0, targetDirection.X).mul(3);
				const behindOffset = targetDirection.mul(-6);
				const optimalPosition = currentTargetPos.add(behindOffset).add(sideOffset);
				
				this.moveTowards(context, optimalPosition);
			}
		}
		
		context.blackboard.lastTargetPosition = currentTargetPos;
	}

	private avoidOtherEntities(context: AIContext, targetPosition: Vector3): Vector3 {
		// Simple entity avoidance - check for other AI entities nearby
		const nearbyEntities = this.findNearbyEntities(context.position, 8);
		
		if (nearbyEntities.size() === 0) {
			return targetPosition;
		}

		// Calculate avoidance vector
		let avoidanceVector = new Vector3(0, 0, 0);
		nearbyEntities.forEach((entity) => {
			const entityPos = PositionHelper.getPosition(entity);
			const avoidDirection = context.position.sub(entityPos);
			const distance = avoidDirection.Magnitude;
			
			if (distance > 0 && distance < 8) {
				// Stronger avoidance for closer entities
				const strength = (8 - distance) / 8;
				avoidanceVector = avoidanceVector.add(avoidDirection.Unit.mul(strength * 5));
			}
		});

		// Combine target direction with avoidance
		const targetDirection = targetPosition.sub(context.position);
		const adjustedTarget = context.position.add(targetDirection.add(avoidanceVector));
		
		return adjustedTarget;
	}

	private findNearbyEntities(position: Vector3, radius: number): Instance[] {
		// Simple nearby entity detection (in a real implementation, this would use the EntityManager)
		const nearbyEntities: Instance[] = [];
		// This is a simplified version - in practice you'd query the EntityManager
		return nearbyEntities;
	}

	private smoothMoveTo(part: BasePart, targetPosition: Vector3, deltaTime: number): void {
		const currentPosition = part.Position;
		const direction = targetPosition.sub(currentPosition);
		const distance = direction.Magnitude;

		if (distance > 0.1) {
			const moveSpeed = 16;
			const maxMove = moveSpeed * deltaTime;
			const moveDistance = math.min(maxMove, distance);
			const newPosition = currentPosition.add(direction.Unit.mul(moveDistance));

			// Smooth rotation towards movement direction
			const lookDirection = direction.Unit;
			const currentLookDirection = part.CFrame.LookVector;
			const rotationSpeed = 5; // radians per second
			const maxRotation = rotationSpeed * deltaTime;
			
			// Interpolate between current and target direction
			const angle = math.acos(math.clamp(currentLookDirection.Dot(lookDirection), -1, 1));
			const rotationAmount = math.min(maxRotation, angle);
			
			let finalDirection: Vector3;
			if (angle > 0.01) {
				const axis = currentLookDirection.Cross(lookDirection).Unit;
				const rotationCFrame = CFrame.fromAxisAngle(axis, rotationAmount);
				finalDirection = rotationCFrame.mul(currentLookDirection);
			} else {
				finalDirection = lookDirection;
			}

			const newCFrame = CFrame.lookAt(newPosition, newPosition.add(finalDirection));
			part.CFrame = newCFrame;
		}
	}
}
