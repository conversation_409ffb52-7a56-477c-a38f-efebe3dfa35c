-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local PositionHelper = TS.import(script, game:GetService("ReplicatedStorage"), "core", "helper", "PositionHelper").PositionHelper
local FollowBehavior
do
	FollowBehavior = setmetatable({}, {
		__tostring = function()
			return "FollowBehavior"
		end,
	})
	FollowBehavior.__index = FollowBehavior
	function FollowBehavior.new(...)
		local self = setmetatable({}, FollowBehavior)
		return self:constructor(...) or self
	end
	function FollowBehavior:constructor()
		self.name = "Follow"
		self.priority = 5
	end
	function FollowBehavior:canExecute(context)
		if not context.target or not context.targetPosition then
			return false
		end
		local _position = context.position
		local _targetPosition = context.targetPosition
		local distance = (_position - _targetPosition).Magnitude
		local _condition = context.blackboard.followRange
		if not (_condition ~= 0 and _condition == _condition and _condition) then
			_condition = 30
		end
		local maxFollowRange = _condition
		local _condition_1 = context.blackboard.minFollowDistance
		if not (_condition_1 ~= 0 and _condition_1 == _condition_1 and _condition_1) then
			_condition_1 = 5
		end
		local minFollowDistance = _condition_1
		return distance <= maxFollowRange and distance > minFollowDistance
	end
	function FollowBehavior:execute(context)
		if not context.target or not context.targetPosition then
			return {
				success = false,
				completed = true,
			}
		end
		local _position = context.position
		local _targetPosition = context.targetPosition
		local distance = (_position - _targetPosition).Magnitude
		local _condition = context.blackboard.minFollowDistance
		if not (_condition ~= 0 and _condition == _condition and _condition) then
			_condition = 5
		end
		local minFollowDistance = _condition
		local _condition_1 = context.blackboard.optimalFollowDistance
		if not (_condition_1 ~= 0 and _condition_1 == _condition_1 and _condition_1) then
			_condition_1 = 8
		end
		local optimalDistance = _condition_1
		-- Enhanced follow behavior with intelligent positioning
		if distance > optimalDistance then
			-- Move closer if too far
			self:moveTowards(context, context.targetPosition)
		elseif distance < minFollowDistance then
			-- Move away if too close (avoid crowding)
			local _position_1 = context.position
			local _targetPosition_1 = context.targetPosition
			local retreatDirection = (_position_1 - _targetPosition_1).Unit
			local _position_2 = context.position
			local _arg0 = retreatDirection * 3
			local retreatPosition = _position_2 + _arg0
			self:moveTowards(context, retreatPosition)
		else
			-- At optimal distance - match target movement
			self:matchTargetMovement(context)
		end
		-- Always face the target for better interaction
		PositionHelper:lookAt(context.entity, context.targetPosition)
		-- Update follow persistence
		local _condition_2 = context.blackboard.followTime
		if not (_condition_2 ~= 0 and _condition_2 == _condition_2 and _condition_2) then
			_condition_2 = 0
		end
		local followTime = _condition_2 + context.deltaTime
		context.blackboard.followTime = followTime
		return {
			success = true,
			completed = distance <= minFollowDistance,
			nextBehavior = if distance > 50 then "Wander" else nil,
		}
	end
	function FollowBehavior:onEnter(context)
		local _exp = context.entityId
		local _position = context.position
		local _targetPosition = context.targetPosition
		print(`🏃 {_exp} is following target at distance {math.floor((_position - _targetPosition).Magnitude)}`)
		context.blackboard.followTime = 0
		context.blackboard.lastTargetPosition = context.targetPosition
	end
	function FollowBehavior:onExit(context)
		print(`🏃 {context.entityId} stopped following`)
	end
	function FollowBehavior:moveTowards(context, targetPosition)
		-- Enhanced pathfinding with obstacle avoidance
		local clearTarget = PositionHelper:findClearPath(context.position, targetPosition, { context.entity })
		local actualTarget = self:avoidOtherEntities(context, clearTarget)
		if context.entity:IsA("Model") and context.entity.PrimaryPart then
			local humanoid = context.entity:FindFirstChild("Humanoid")
			if humanoid then
				-- Enhanced humanoid movement with speed adjustment
				local distance = (context.position - actualTarget).Magnitude
				local _condition = context.blackboard.moveSpeed
				if not (_condition ~= 0 and _condition == _condition and _condition) then
					_condition = 16
				end
				local moveSpeed = _condition
				-- Adjust speed based on distance (slow down when close, speed up when far)
				if distance > 20 then
					humanoid.WalkSpeed = moveSpeed * 1.2
				elseif distance < 10 then
					humanoid.WalkSpeed = moveSpeed * 0.7
				else
					humanoid.WalkSpeed = moveSpeed
				end
				humanoid:MoveTo(actualTarget)
			else
				self:smoothMoveTo(context.entity.PrimaryPart, actualTarget, context.deltaTime)
			end
		elseif context.entity:IsA("BasePart") then
			self:smoothMoveTo(context.entity, actualTarget, context.deltaTime)
		end
	end
	function FollowBehavior:matchTargetMovement(context)
		-- Try to predict where target is going and position accordingly
		local lastTargetPos = context.blackboard.lastTargetPosition
		local currentTargetPos = context.targetPosition
		if lastTargetPos then
			local targetVelocity = currentTargetPos - lastTargetPos
			local targetSpeed = targetVelocity.Magnitude
			if targetSpeed > 2 then
				-- Position slightly behind and to the side
				local targetDirection = targetVelocity.Unit
				local sideOffset = Vector3.new(-targetDirection.Z, 0, targetDirection.X) * 3
				local behindOffset = targetDirection * (-6)
				local optimalPosition = currentTargetPos + behindOffset + sideOffset
				self:moveTowards(context, optimalPosition)
			end
		end
		context.blackboard.lastTargetPosition = currentTargetPos
	end
	function FollowBehavior:avoidOtherEntities(context, targetPosition)
		-- Simple entity avoidance - check for other AI entities nearby
		local nearbyEntities = self:findNearbyEntities(context.position, 8)
		if #nearbyEntities == 0 then
			return targetPosition
		end
		-- Calculate avoidance vector
		local avoidanceVector = Vector3.new(0, 0, 0)
		-- ▼ ReadonlyArray.forEach ▼
		local _callback = function(entity)
			local entityPos = PositionHelper:getPosition(entity)
			local avoidDirection = context.position - entityPos
			local distance = avoidDirection.Magnitude
			if distance > 0 and distance < 8 then
				-- Stronger avoidance for closer entities
				local strength = (8 - distance) / 8
				local _avoidanceVector = avoidanceVector
				local _unit = avoidDirection.Unit
				local _arg0 = strength * 5
				avoidanceVector = _avoidanceVector + (_unit * _arg0)
			end
		end
		for _k, _v in nearbyEntities do
			_callback(_v, _k - 1, nearbyEntities)
		end
		-- ▲ ReadonlyArray.forEach ▲
		-- Combine target direction with avoidance
		local _targetPosition = targetPosition
		local _position = context.position
		local targetDirection = _targetPosition - _position
		local _position_1 = context.position
		local _avoidanceVector = avoidanceVector
		local _arg0 = targetDirection + _avoidanceVector
		local adjustedTarget = _position_1 + _arg0
		return adjustedTarget
	end
	function FollowBehavior:findNearbyEntities(position, radius)
		-- Simple nearby entity detection (in a real implementation, this would use the EntityManager)
		local nearbyEntities = {}
		-- This is a simplified version - in practice you'd query the EntityManager
		return nearbyEntities
	end
	function FollowBehavior:smoothMoveTo(part, targetPosition, deltaTime)
		local currentPosition = part.Position
		local direction = targetPosition - currentPosition
		local distance = direction.Magnitude
		if distance > 0.1 then
			local moveSpeed = 16
			local maxMove = moveSpeed * deltaTime
			local moveDistance = math.min(maxMove, distance)
			local _arg0 = direction.Unit * moveDistance
			local newPosition = currentPosition + _arg0
			-- Smooth rotation towards movement direction
			local lookDirection = direction.Unit
			local currentLookDirection = part.CFrame.LookVector
			local rotationSpeed = 5
			local maxRotation = rotationSpeed * deltaTime
			-- Interpolate between current and target direction
			local angle = math.acos(math.clamp(currentLookDirection:Dot(lookDirection), -1, 1))
			local rotationAmount = math.min(maxRotation, angle)
			local finalDirection
			if angle > 0.01 then
				local axis = currentLookDirection:Cross(lookDirection).Unit
				local rotationCFrame = CFrame.fromAxisAngle(axis, rotationAmount)
				finalDirection = rotationCFrame * currentLookDirection
			else
				finalDirection = lookDirection
			end
			local _finalDirection = finalDirection
			local newCFrame = CFrame.lookAt(newPosition, newPosition + _finalDirection)
			part.CFrame = newCFrame
		end
	end
end
return {
	FollowBehavior = FollowBehavior,
}
