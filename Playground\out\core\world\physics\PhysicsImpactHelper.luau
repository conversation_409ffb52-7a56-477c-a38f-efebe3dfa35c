-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local _services = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "services")
local Workspace = _services.Workspace
local RunService = _services.RunService
local Players = _services.Players
local PhysicsImpactHelper
do
	PhysicsImpactHelper = setmetatable({}, {
		__tostring = function()
			return "PhysicsImpactHelper"
		end,
	})
	PhysicsImpactHelper.__index = PhysicsImpactHelper
	function PhysicsImpactHelper.new(...)
		local self = setmetatable({}, PhysicsImpactHelper)
		return self:constructor(...) or self
	end
	function PhysicsImpactHelper:constructor()
	end
	function PhysicsImpactHelper:initialize()
		if self.updateConnection then
			return nil
		end
		self.updateConnection = RunService.Heartbeat:Connect(function()
			self:updatePhysicsZones()
		end)
		print("🌍 PhysicsImpactHelper initialized")
	end
	function PhysicsImpactHelper:createGravityZone(options)
		self.zoneCounter = self.zoneCounter + 1
		local zoneId = `gravity_{self.zoneCounter}_{tick()}`
		local _object = {
			id = zoneId,
			options = options,
			createdAt = tick(),
		}
		local _left = "expiresAt"
		local _value = options.duration
		_object[_left] = if _value ~= 0 and _value == _value and _value then tick() + options.duration else nil
		_object.affectedObjects = {}
		_object.zoneVisual = if options.visualEffect then self:createZoneVisual(options) else nil
		local zone = _object
		self.activeZones[zoneId] = zone
		-- Set up cleanup if temporary
		local _value_1 = options.duration
		if _value_1 ~= 0 and _value_1 == _value_1 and _value_1 then
			task.delay(options.duration, function()
				self:removePhysicsZone(zoneId)
			end)
		end
		print(`🌍 Created gravity zone: {zoneId} with {options.gravityMultiplier}x gravity`)
		return zoneId
	end
	function PhysicsImpactHelper:createForceZone(options)
		self.zoneCounter = self.zoneCounter + 1
		local zoneId = `force_{self.zoneCounter}_{tick()}`
		local _object = {
			id = zoneId,
			options = options,
			createdAt = tick(),
		}
		local _left = "expiresAt"
		local _value = options.duration
		_object[_left] = if _value ~= 0 and _value == _value and _value then tick() + options.duration else nil
		_object.affectedObjects = {}
		_object.zoneVisual = if options.visualEffect then self:createZoneVisual(options) else nil
		local zone = _object
		self.activeZones[zoneId] = zone
		local _value_1 = options.duration
		if _value_1 ~= 0 and _value_1 == _value_1 and _value_1 then
			task.delay(options.duration, function()
				self:removePhysicsZone(zoneId)
			end)
		end
		print(`🌍 Created force zone: {zoneId} with {options.forceType} force`)
		return zoneId
	end
	function PhysicsImpactHelper:createBarrierZone(options)
		self.zoneCounter = self.zoneCounter + 1
		local zoneId = `barrier_{self.zoneCounter}_{tick()}`
		local _object = {
			id = zoneId,
			options = options,
			createdAt = tick(),
		}
		local _left = "expiresAt"
		local _value = options.duration
		_object[_left] = if _value ~= 0 and _value == _value and _value then tick() + options.duration else nil
		_object.affectedObjects = {}
		_object.zoneVisual = if options.visualEffect then self:createBarrierVisual(options) else nil
		local zone = _object
		self.activeZones[zoneId] = zone
		local _value_1 = options.duration
		if _value_1 ~= 0 and _value_1 == _value_1 and _value_1 then
			task.delay(options.duration, function()
				self:removePhysicsZone(zoneId)
			end)
		end
		print(`🌍 Created barrier zone: {zoneId} of type {options.barrierType}`)
		return zoneId
	end
	function PhysicsImpactHelper:updatePhysicsZones()
		for zoneId, zone in self.activeZones do
			self:updateZone(zone)
		end
		-- Clean up expired zones
		self:cleanupExpiredZones()
	end
	function PhysicsImpactHelper:updateZone(zone)
		local objectsInZone = self:findObjectsInZone(zone)
		-- Remove objects that left the zone
		for obj in zone.affectedObjects do
			if not (objectsInZone[obj] ~= nil) then
				self:removeObjectFromZone(obj, zone)
				zone.affectedObjects[obj] = nil
			end
		end
		-- Add new objects that entered the zone
		for obj in objectsInZone do
			if not (zone.affectedObjects[obj] ~= nil) then
				self:addObjectToZone(obj, zone)
				zone.affectedObjects[obj] = true
			end
		end
		-- Apply continuous effects to all objects in zone
		for obj in zone.affectedObjects do
			self:applyZoneEffect(obj, zone)
		end
	end
	function PhysicsImpactHelper:findObjectsInZone(zone)
		local objectsInZone = {}
		local options = zone.options
		for _, objectType in options.affectedObjects do
			local objects = self:getObjectsByType(objectType)
			for _1, obj in objects do
				if self:isObjectInZone(obj, zone) then
					objectsInZone[obj] = true
				end
			end
		end
		return objectsInZone
	end
	function PhysicsImpactHelper:getObjectsByType(objectType)
		local objects = {}
		repeat
			if objectType == "players" then
				for _, player in Players:GetPlayers() do
					if player.Character then
						local _character = player.Character
						table.insert(objects, _character)
					end
				end
				break
			end
			if objectType == "parts" then
				for _, descendant in Workspace:GetDescendants() do
					if descendant:IsA("Part") and not descendant.Anchored then
						table.insert(objects, descendant)
					end
				end
				break
			end
			if objectType == "debris" then
				for _, descendant in Workspace:GetDescendants() do
					local _value = descendant:IsA("Part") and (string.find(descendant.Name, "debris"))
					if _value ~= 0 and _value == _value and _value then
						table.insert(objects, descendant)
					end
				end
				break
			end
		until true
		return objects
	end
	function PhysicsImpactHelper:isObjectInZone(obj, zone)
		local position
		if obj:IsA("Model") then
			local primaryPart = obj.PrimaryPart or obj:FindFirstChild("HumanoidRootPart")
			if not primaryPart or not primaryPart:IsA("BasePart") then
				return false
			end
			position = primaryPart.Position
		elseif obj:IsA("Part") then
			position = obj.Position
		else
			return false
		end
		local _center = zone.options.center
		local _position = position
		local distance = (_center - _position).Magnitude
		return distance <= zone.options.radius
	end
	function PhysicsImpactHelper:addObjectToZone(obj, zone)
		print(`🌍 Object {obj.Name} entered {zone.options.zoneType} zone {zone.id}`)
		-- Initialize zone-specific effects
		local _exp = zone.options.zoneType
		repeat
			if _exp == "gravity" then
				self:initializeGravityEffect(obj, zone.options)
				break
			end
			if _exp == "force" then
				self:initializeForceEffect(obj, zone.options)
				break
			end
			if _exp == "barrier" then
				-- Barriers work on collision, not continuous effect
				break
			end
		until true
	end
	function PhysicsImpactHelper:removeObjectFromZone(obj, zone)
		print(`🌍 Object {obj.Name} left {zone.options.zoneType} zone {zone.id}`)
		-- Clean up zone-specific effects
		self:cleanupObjectEffects(obj)
	end
	function PhysicsImpactHelper:applyZoneEffect(obj, zone)
		local _exp = zone.options.zoneType
		repeat
			if _exp == "gravity" then
				self:applyGravityEffect(obj, zone.options)
				break
			end
			if _exp == "force" then
				self:applyForceEffect(obj, zone.options)
				break
			end
		until true
	end
	function PhysicsImpactHelper:initializeGravityEffect(obj, options)
		local part = self:getPartFromObject(obj)
		if not part or part.Anchored then
			return nil
		end
		-- Create custom gravity using BodyVelocity
		local bodyVelocity = Instance.new("BodyVelocity")
		bodyVelocity.MaxForce = Vector3.new(0, math.huge, 0)
		bodyVelocity.Velocity = Vector3.new(0, 0, 0)
		bodyVelocity.Parent = part
		local forceApp = {
			target = obj,
			forceType = "BodyVelocity",
			forceObject = bodyVelocity,
		}
		local _activeForces = self.activeForces
		local _obj = obj
		if not (_activeForces[_obj] ~= nil) then
			local _activeForces_1 = self.activeForces
			local _obj_1 = obj
			_activeForces_1[_obj_1] = {}
		end
		local _activeForces_1 = self.activeForces
		local _obj_1 = obj
		local _exp = _activeForces_1[_obj_1]
		table.insert(_exp, forceApp)
	end
	function PhysicsImpactHelper:applyGravityEffect(obj, options)
		local part = self:getPartFromObject(obj)
		if not part then
			return nil
		end
		local _activeForces = self.activeForces
		local _obj = obj
		local forces = _activeForces[_obj]
		if not forces then
			return nil
		end
		-- ▼ ReadonlyArray.find ▼
		local _callback = function(f)
			return f.forceType == "BodyVelocity"
		end
		local _result
		for _i, _v in forces do
			if _callback(_v, _i - 1, forces) == true then
				_result = _v
				break
			end
		end
		-- ▲ ReadonlyArray.find ▲
		local gravityForce = _result
		if not gravityForce or not gravityForce.forceObject.Parent then
			return nil
		end
		local bodyVelocity = gravityForce.forceObject
		local gravityDirection = options.gravityDirection or Vector3.new(0, -1, 0)
		local gravityStrength = 196.2 * options.gravityMultiplier
		-- Apply custom gravity
		local currentVelocity = bodyVelocity.Velocity
		local _arg0 = gravityStrength * (1 / 60)
		local _arg0_1 = gravityDirection * _arg0
		local newVelocity = currentVelocity + _arg0_1
		bodyVelocity.Velocity = newVelocity
	end
	function PhysicsImpactHelper:initializeForceEffect(obj, options)
		local part = self:getPartFromObject(obj)
		if not part or part.Anchored then
			return nil
		end
		if options.forceType == "impulse" then
			-- Apply one-time impulse
			local bodyVelocity = Instance.new("BodyVelocity")
			bodyVelocity.MaxForce = Vector3.new(4000, 4000, 4000)
			local _forceDirection = options.forceDirection
			local _forceStrength = options.forceStrength
			bodyVelocity.Velocity = _forceDirection * _forceStrength
			bodyVelocity.Parent = part
			-- Remove after short time
			task.delay(0.5, function()
				if bodyVelocity.Parent then
					bodyVelocity:Destroy()
				end
			end)
		end
	end
	function PhysicsImpactHelper:applyForceEffect(obj, options)
		if options.forceType == "constant" then
			local part = self:getPartFromObject(obj)
			if not part then
				return nil
			end
			-- Apply constant force
			local bodyVelocity = part:FindFirstChild("BodyVelocity")
			if bodyVelocity then
				local _forceDirection = options.forceDirection
				local _forceStrength = options.forceStrength
				bodyVelocity.Velocity = _forceDirection * _forceStrength
			end
		end
	end
	function PhysicsImpactHelper:getPartFromObject(obj)
		if obj:IsA("Part") then
			return obj
		elseif obj:IsA("Model") then
			local part = obj.PrimaryPart or obj:FindFirstChild("HumanoidRootPart")
			if part and part:IsA("Part") then
				return part
			end
		end
		return nil
	end
	function PhysicsImpactHelper:cleanupObjectEffects(obj)
		local _activeForces = self.activeForces
		local _obj = obj
		local forces = _activeForces[_obj]
		if not forces then
			return nil
		end
		for _, force in forces do
			if force.forceObject.Parent then
				force.forceObject:Destroy()
			end
		end
		local _activeForces_1 = self.activeForces
		local _obj_1 = obj
		_activeForces_1[_obj_1] = nil
	end
	function PhysicsImpactHelper:createZoneVisual(options)
		local visual = Instance.new("Part")
		visual.Name = `{options.zoneType}_zone_visual`
		visual.Size = Vector3.new(options.radius * 2, options.radius * 2, options.radius * 2)
		visual.Position = options.center
		visual.Shape = Enum.PartType.Ball
		visual.Material = Enum.Material.ForceField
		visual.Transparency = 0.7
		visual.CanCollide = false
		visual.Anchored = true
		-- Color based on zone type
		local _exp = options.zoneType
		repeat
			if _exp == "gravity" then
				visual.Color = Color3.new(0.5, 0, 1)
				break
			end
			if _exp == "force" then
				visual.Color = Color3.new(1, 0.5, 0)
				break
			end
			if _exp == "barrier" then
				visual.Color = Color3.new(0, 0.5, 1)
				break
			end
		until true
		visual.Parent = Workspace
		return visual
	end
	function PhysicsImpactHelper:createBarrierVisual(options)
		local visual = self:createZoneVisual(options)
		if options.barrierType == "dome" then
			visual.Shape = Enum.PartType.Ball
		elseif options.barrierType == "cylinder" then
			visual.Shape = Enum.PartType.Cylinder
		end
		return visual
	end
	function PhysicsImpactHelper:removePhysicsZone(zoneId)
		local _activeZones = self.activeZones
		local _zoneId = zoneId
		local zone = _activeZones[_zoneId]
		if not zone then
			return nil
		end
		-- Clean up all affected objects
		for obj in zone.affectedObjects do
			self:cleanupObjectEffects(obj)
		end
		-- Remove visual
		if zone.zoneVisual and zone.zoneVisual.Parent then
			zone.zoneVisual:Destroy()
		end
		local _activeZones_1 = self.activeZones
		local _zoneId_1 = zoneId
		_activeZones_1[_zoneId_1] = nil
		print(`🌍 Removed physics zone: {zoneId}`)
	end
	function PhysicsImpactHelper:cleanupExpiredZones()
		local currentTime = tick()
		for zoneId, zone in self.activeZones do
			local _condition = zone.expiresAt
			if _condition ~= 0 and _condition == _condition and _condition then
				_condition = currentTime >= zone.expiresAt
			end
			if _condition ~= 0 and _condition == _condition and _condition then
				self:removePhysicsZone(zoneId)
			end
		end
	end
	function PhysicsImpactHelper:getActiveZones()
		return self.activeZones
	end
	function PhysicsImpactHelper:shutdown()
		if self.updateConnection then
			self.updateConnection:Disconnect()
			self.updateConnection = nil
		end
		-- Clean up all zones
		local zoneIds = {}
		for zoneId in self.activeZones do
			table.insert(zoneIds, zoneId)
		end
		for _, zoneId in zoneIds do
			self:removePhysicsZone(zoneId)
		end
		print("🌍 PhysicsImpactHelper shutdown")
	end
	PhysicsImpactHelper.activeZones = {}
	PhysicsImpactHelper.zoneCounter = 0
	PhysicsImpactHelper.activeForces = {}
end
return {
	PhysicsImpactHelper = PhysicsImpactHelper,
}
