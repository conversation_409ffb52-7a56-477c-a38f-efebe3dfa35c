-- Compiled with roblox-ts v3.0.0
--[[
	*
	 * Table and data structure utilities for game development
	 
]]
local TableUtils
do
	TableUtils = setmetatable({}, {
		__tostring = function()
			return "TableUtils"
		end,
	})
	TableUtils.__index = TableUtils
	function TableUtils.new(...)
		local self = setmetatable({}, TableUtils)
		return self:constructor(...) or self
	end
	function TableUtils:constructor()
	end
	function TableUtils:deepCopy(obj)
		local _obj = obj
		if typeof(_obj) ~= "table" then
			return obj
		end
		local copy = {}
		for key, value in pairs(obj) do
			copy[key] = self:deepCopy(value)
		end
		return copy
	end
	function TableUtils:shallowCopy(obj)
		local _obj = obj
		if typeof(_obj) ~= "table" then
			return obj
		end
		local copy = {}
		for key, value in pairs(obj) do
			copy[key] = value
		end
		return copy
	end
	function TableUtils:merge(...)
		local objects = { ... }
		local result = {}
		for _, obj in objects do
			if typeof(obj) == "table" then
				for key, value in pairs(obj) do
					result[key] = value
				end
			end
		end
		return result
	end
	function TableUtils:deepMerge(...)
		local objects = { ... }
		local result = {}
		for _, obj in objects do
			if typeof(obj) == "table" then
				for key, value in pairs(obj) do
					local currentValue = result[key]
					if typeof(currentValue) == "table" and typeof(value) == "table" then
						result[key] = self:deepMerge(currentValue, value)
					else
						result[key] = value
					end
				end
			end
		end
		return result
	end
	function TableUtils:get(obj, path)
		local keys = string.split(path, ".")
		local current = obj
		for _, key in keys do
			local _current = current
			local _condition = typeof(_current) ~= "table"
			if not _condition then
				_condition = current[key] == nil
			end
			if _condition then
				return nil
			end
			current = current[key]
		end
		return current
	end
	function TableUtils:set(obj, path, value)
		local keys = string.split(path, ".")
		local current = obj
		for i = 0, #keys - 2 do
			local key = keys[i + 1]
			local _arg0 = current[key]
			if typeof(_arg0) ~= "table" then
				current[key] = {}
			end
			current = current[key]
		end
		current[keys[#keys]] = value
	end
	function TableUtils:has(obj, path)
		return self:get(obj, path) ~= nil
	end
	function TableUtils:remove(obj, path)
		local keys = string.split(path, ".")
		local current = obj
		-- Navigate to parent object
		for i = 0, #keys - 2 do
			local key = keys[i + 1]
			local _arg0 = current[key]
			if typeof(_arg0) ~= "table" then
				return false
			end
			current = current[key]
		end
		local lastKey = keys[#keys]
		if current[lastKey] ~= nil then
			current[lastKey] = nil
			return true
		end
		return false
	end
	function TableUtils:keys(obj)
		local keys = {}
		for key in pairs(obj) do
			table.insert(keys, key)
		end
		return keys
	end
	function TableUtils:values(obj)
		local values = {}
		for _, value in pairs(obj) do
			table.insert(values, value)
		end
		return values
	end
	function TableUtils:entries(obj)
		local entries = {}
		for key, value in pairs(obj) do
			local _arg0 = { key, value }
			table.insert(entries, _arg0)
		end
		return entries
	end
	function TableUtils:size(obj)
		local count = 0
		for key in pairs(obj) do
			count += 1
		end
		return count
	end
	function TableUtils:isEmpty(obj)
		for key in pairs(obj) do
			return false
		end
		return true
	end
	function TableUtils:filter(obj, predicate)
		local result = {}
		for key, value in pairs(obj) do
			if predicate(value, key) then
				result[key] = value
			end
		end
		return result
	end
	function TableUtils:map(obj, mapper)
		local result = {}
		for key, value in pairs(obj) do
			result[key] = mapper(value, key)
		end
		return result
	end
	function TableUtils:reduce(obj, reducer, initialValue)
		local accumulator = initialValue
		for key, value in pairs(obj) do
			accumulator = reducer(accumulator, value, key)
		end
		return accumulator
	end
	function TableUtils:find(obj, predicate)
		for key, value in pairs(obj) do
			if predicate(value, key) then
				return value
			end
		end
		return nil
	end
	function TableUtils:findKey(obj, predicate)
		for key, value in pairs(obj) do
			if predicate(value, key) then
				return key
			end
		end
		return nil
	end
	function TableUtils:some(obj, predicate)
		for key, value in pairs(obj) do
			if predicate(value, key) then
				return true
			end
		end
		return false
	end
	function TableUtils:every(obj, predicate)
		for key, value in pairs(obj) do
			if not predicate(value, key) then
				return false
			end
		end
		return true
	end
	function TableUtils:groupBy(array, keySelector)
		local groups = {}
		for _, item in array do
			local key = keySelector(item)
			if not groups[key] then
				groups[key] = {}
			end
			local _exp = groups[key]
			table.insert(_exp, item)
		end
		return groups
	end
	function TableUtils:fromEntries(entries)
		local result = {}
		for _, _binding in entries do
			local key = _binding[1]
			local value = _binding[2]
			result[key] = value
		end
		return result
	end
	function TableUtils:pick(obj, keys)
		local result = {}
		for _, key in keys do
			if obj[key] ~= nil then
				result[key] = obj[key]
			end
		end
		return result
	end
	function TableUtils:omit(obj, keys)
		local result = self:shallowCopy(obj)
		for _, key in keys do
			result[key] = nil
		end
		return result
	end
	function TableUtils:invert(obj)
		local result = {}
		for key, value in pairs(obj) do
			result[tostring(value)] = key
		end
		return result
	end
	function TableUtils:flatten(obj, prefix)
		if prefix == nil then
			prefix = ""
		end
		local result = {}
		for key, value in pairs(obj) do
			local newKey = if prefix ~= "" and prefix then `{prefix}.{key}` else key
			if typeof(value) == "table" and value ~= nil then
				local flattened = self:flatten(value, newKey)
				for flatKey, flatValue in pairs(flattened) do
					result[flatKey] = flatValue
				end
			else
				result[newKey] = value
			end
		end
		return result
	end
	function TableUtils:unflatten(obj)
		local result = {}
		for key, value in pairs(obj) do
			self:set(result, key, value)
		end
		return result
	end
	function TableUtils:deepEqual(a, b)
		if a == b then
			return true
		end
		local _a = a
		local _exp = typeof(_a)
		local _b = b
		if _exp ~= typeof(_b) then
			return false
		end
		local _a_1 = a
		if typeof(_a_1) ~= "table" then
			return false
		end
		local objA = a
		local objB = b
		local keysA = self:keys(objA)
		local keysB = self:keys(objB)
		if #keysA ~= #keysB then
			return false
		end
		for _, key in keysA do
			if not self:deepEqual(objA[key], objB[key]) then
				return false
			end
		end
		return true
	end
	function TableUtils:serialize(obj, indent)
		if indent == nil then
			indent = 0
		end
		local _indent = indent
		local indentStr = string.rep(" ", _indent)
		local _obj = obj
		if typeof(_obj) == "string" then
			return `"{obj}"`
		else
			local _obj_1 = obj
			local _condition = typeof(_obj_1) == "number"
			if not _condition then
				local _obj_2 = obj
				_condition = typeof(_obj_2) == "boolean"
			end
			if _condition then
				return tostring(obj)
			elseif obj == nil then
				return "null"
			else
				local _obj_2 = obj
				if typeof(_obj_2) == "table" then
					local objTable = obj
					local entries = {}
					for key, value in pairs(objTable) do
						local serializedValue = self:serialize(value, indent + 2)
						local _arg0 = `{indentStr}  "{key}": {serializedValue}`
						table.insert(entries, _arg0)
					end
					if #entries == 0 then
						return "{}"
					end
					return `\{\n{table.concat(entries, ",\n")}\n{indentStr}\}`
				else
					return `"{tostring(obj)}"`
				end
			end
		end
	end
end
return {
	TableUtils = TableUtils,
}
