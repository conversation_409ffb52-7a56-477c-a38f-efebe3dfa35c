import * as React from "@rbxts/react";
import { COLORS } from "../../design";

interface ProgressBarProps {
	progress: number; // 0 to 1
	barColor?: Color3;
	backgroundColor?: Color3;
	size?: UDim2;
	cornerRadius?: UDim;
	borderSize?: number;
	borderColor?: Color3;
	layoutOrder?: number;
	position?: UDim2;
	anchorPoint?: Vector2;
}

export function ProgressBar({
	progress,
	barColor = Color3.fromHex(COLORS.primary),
	backgroundColor = Color3.fromHex(COLORS.bg.secondary),
	size = new UDim2(1, 0, 0, 20),
	cornerRadius = new UDim(0, 4),
	borderSize = 0,
	borderColor = Color3.fromHex(COLORS.border.base),
	layoutOrder,
	position,
	anchorPoint,
}: ProgressBarProps): React.ReactElement {
	// Clamp progress between 0 and 1
	const clampedProgress = math.max(0, math.min(1, progress));

	return (
		<frame
			Size={size}
			Position={position}
			AnchorPoint={anchorPoint}
			BackgroundColor3={backgroundColor}
			BorderSizePixel={borderSize}
			BorderColor3={borderColor}
			LayoutOrder={layoutOrder}
		>
			{/* Corner radius for background */}
			<uicorner CornerRadius={cornerRadius} />

			{/* Progress fill */}
			<frame
				Size={new UDim2(clampedProgress, 0, 1, 0)}
				Position={new UDim2(0, 0, 0, 0)}
				BackgroundColor3={barColor}
				BorderSizePixel={0}
			>
				{/* Corner radius for progress fill */}
				<uicorner CornerRadius={cornerRadius} />
			</frame>
		</frame>
	);
}