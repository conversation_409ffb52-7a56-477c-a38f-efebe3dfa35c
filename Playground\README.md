# 🎮 RoboxGames Playground

An advanced Roblox game development playground built with TypeScript and the RoboxGames Core Framework. This repository serves as a comprehensive testing environment and showcase for modern game development patterns in Roblox.

## 🚀 Features

### 🌍 World Testing Laboratory
- **Gravity System Testing**: Low, high, zero, and normal gravity zones
- **Weather System**: Realistic weather effects including rain, snow, storms, and more
- **Day/Night Cycle**: Dynamic time-of-day controls
- **Entity Management**: Spawn and manage NPCs, projectiles, effects, and pickups
- **AI System**: Advanced AI behaviors for NPCs with patrolling, following, and fleeing

### 🎨 GUI Component Showcase
- **Interactive Component Browser**: Test all available UI components
- **Design System**: Consistent color palette and typography
- **Responsive Layout**: Adaptive UI that works across different screen sizes
- **Modal System**: Advanced overlay dialogs
- **Form Components**: Input fields, sliders, buttons, and more

### ✨ Effects & Animation System
- **Particle Effects**: Explosions, storms, and impact flashes
- **Sound Integration**: Layered audio effects with proper falloff
- **Animation Framework**: Programmatic character animations without assets
- **Visual Effects**: Glowing spheres, expanding rings, and complex combinations

### 🔧 Core Framework Testing
- **Physics Engine**: Gravity zones, force fields, and barriers
- **Network Validation**: Client-server communication with proper validation
- **Data Persistence**: DataStore integration with player data management
- **State Management**: Centralized state with reactive updates
- **Error Handling**: Comprehensive error handling with Result patterns

## 🛠️ Setup & Installation

### Prerequisites
- Node.js (v14 or higher)
- TypeScript knowledge
- Roblox Studio
- Basic understanding of React concepts

### Installation Steps

1. **Clone the repository**
   ```bash
   git clone https://github.com/RoboxGames/Playground.git
   cd Playground
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Build the project**
   ```bash
   npm run build
   ```

4. **Open in Roblox Studio**
   - Open Roblox Studio
   - Create a new place or open an existing one
   - Use Rojo or manually sync the built files

### Development Workflow

- **Watch mode**: `npm run watch` - Automatically rebuilds on file changes
- **Build once**: `npm run build` - Single build for testing
- **Linting**: Uses ESLint with TypeScript support

## 🎯 Usage Guide

### World Testing Panel

The World Testing Panel is the main interface for testing framework features:

1. **Open the panel**: Look for the testing panel button in the bottom-left corner
2. **Test systems**: Use the organized sections to test different features
3. **Monitor console**: Watch the console output for detailed feedback
4. **Interactive testing**: Many features provide visual feedback and console logs

### Key Testing Features

#### 🌍 Environmental Controls
- **Gravity Testing**: Experience different gravity levels
- **Weather Systems**: Test realistic weather with particle effects
- **Lighting Controls**: Dynamic day/night transitions

#### 🤖 Entity & AI Systems
- **NPC Spawning**: Create intelligent NPCs with various behaviors
- **Entity Management**: Track and manage game objects
- **AI Behaviors**: Test pathfinding, detection, and interactions

#### 🎨 UI Development
- **Component Browser**: Interactive showcase of all UI components
- **Design System**: Consistent styling and theming
- **Responsive Testing**: Test UI adaptability

#### ✨ Effects Testing
- **Particle Systems**: Explosions, weather effects, and more
- **Sound Effects**: 3D positional audio with proper falloff
- **Animation Testing**: Character and object animations

## 🏗️ Architecture Overview

### Core Framework Structure
```
src/
├── client/           # Client-side code
│   ├── gui/         # UI components and panels
│   ├── abilities/   # Client ability systems
│   └── movement/    # Movement and input handling
├── server/          # Server-side code
│   ├── world/       # World management
│   ├── abilities/   # Server ability validation
│   └── data/        # Data persistence
├── core/            # Shared framework code
│   ├── gui/         # React UI components
│   ├── effects/     # Visual and audio effects
│   ├── world/       # World systems (physics, weather)
│   ├── entities/    # Entity management
│   └── animations/  # Animation systems
└── shared/          # Shared types and utilities
```

### Technology Stack

- **TypeScript**: Type-safe development with modern JavaScript features
- **React**: Component-based UI with hooks and state management
- **roblox-ts**: TypeScript to Lua transpilation for Roblox
- **ESLint**: Code quality and consistency
- **Prettier**: Code formatting

## 🧪 Testing Features

### Comprehensive Test Suite
The playground includes a comprehensive testing system accessible through the World Testing Panel:

1. **Server Status**: Verify server initialization and connectivity
2. **RemoteEvent Status**: Check client-server communication channels
3. **Core Framework**: Test framework initialization and services
4. **Error Handling**: Validate error handling and recovery
5. **Networking**: Test client-server data validation
6. **Effects System**: Verify visual and audio effects
7. **Ability System**: Test ability validation and cooldowns
8. **UI Components**: Verify component functionality
9. **Performance**: Monitor system performance metrics

### Usage Examples

#### Testing Weather Effects
```typescript
// Example: Test realistic rain weather
const weatherController = WeatherController.getInstance();
weatherController.setWeather({
    type: "rain",
    intensity: 0.7,
    transitionDuration: 2
});
```

#### Spawning AI NPCs
```typescript
// Example: Spawn an intelligent NPC
const entityManager = EntityManager.getInstance();
const entity = entityManager.spawnEntity({
    type: EntityType.NPC,
    position: new Vector3(10, 0, 0),
    data: { name: "Guard NPC", aiEnabled: true }
});
```

## 🤝 Contributing

This playground serves as a testing ground for the RoboxGames Core Framework. When contributing:

1. **Follow TypeScript best practices**
2. **Use the established component patterns**
3. **Add tests for new features**
4. **Update documentation**
5. **Follow the existing code style**

### Development Guidelines

- **Component Structure**: Follow React component patterns with proper TypeScript typing
- **State Management**: Use the Core framework's state management system
- **Error Handling**: Implement proper error handling with Result patterns
- **Testing**: Add interactive tests to the World Testing Panel
- **Documentation**: Document new features and APIs

## 📝 API Documentation

### Core Services

#### Entity Management
```typescript
const entityManager = EntityManager.getInstance();
const entity = entityManager.spawnEntity({
    type: EntityType.NPC,
    position: new Vector3(0, 10, 0),
    data: { health: 100 }
});
```

#### Weather Control
```typescript
const weatherController = WeatherController.getInstance();
weatherController.setWeather({
    type: "snow",
    intensity: 0.8
});
```

#### AI System
```typescript
const aiController = AIController.getInstance();
const aiAgent = aiController.registerAI(entityId, {
    detectionRange: 30,
    moveSpeed: 12
});
```

## 🔧 Configuration

The framework uses a centralized configuration system:

- **Design System**: Colors, typography, and spacing in `src/core/design/`
- **Physics**: Gravity and force settings in `src/core/world/physics/`
- **Network**: RemoteEvent configurations in server scripts
- **Effects**: Particle and sound effect parameters

## 🐛 Troubleshooting

### Common Issues

1. **Build Failures**: Ensure all dependencies are installed with `npm install`
2. **RemoteEvent Errors**: Check server initialization in Roblox Studio
3. **Physics Issues**: Verify workspace settings and part properties
4. **UI Not Appearing**: Check ZIndex values and ScreenGui display orders

### Debug Tools

- **Console Logging**: Comprehensive logging throughout the system
- **Debug Overlay**: Visual debugging tools (press F3 in-game)
- **Performance Monitor**: Built-in performance tracking
- **Error Reporting**: Detailed error messages with stack traces

## 📊 Performance

The playground is optimized for:
- **Smooth 60 FPS**: Efficient rendering and physics
- **Low Memory Usage**: Proper cleanup and garbage collection
- **Fast Loading**: Optimized asset loading and initialization
- **Scalability**: Supports multiple concurrent users

## 🔗 Links

- [RoboxGames Core Framework Documentation](link-to-docs)
- [TypeScript for Roblox](https://roblox-ts.com/)
- [React Documentation](https://reactjs.org/)
- [Roblox Developer Hub](https://developer.roblox.com/)

## 📄 License

This project is part of the RoboxGames ecosystem. See LICENSE file for details.

---

**Built with ❤️ by the RoboxGames Team**

*Showcasing the future of Roblox game development with modern tools and frameworks.*