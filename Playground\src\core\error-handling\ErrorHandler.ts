import * as React from "@rbxts/react";
import { ErrorContext as CoreErrorContext } from "./types/ErrorTypes";

/**
 * Centralized error handling utilities for the Playground application
 * Provides consistent error logging, recovery strategies, and user feedback
 */

export interface ErrorHandlerContext extends CoreErrorContext {
	timestamp?: number;
}

export interface ErrorRecoveryOptions {
	showToast?: boolean;
	fallbackValue?: unknown;
	retryCallback?: () => void;
	maxRetries?: number;
}

export class ErrorHandler {
	private static instance: ErrorHandler;
	private errorCounts = new Map<string, number>();
	private readonly maxErrorCount = 5;

	public static getInstance(): ErrorHandler {
		if (!ErrorHandler.instance) {
			ErrorHandler.instance = new ErrorHandler();
		}
		return ErrorHandler.instance;
	}

	/**
	 * Handle and log an error with context
	 */
	public handleError(
		err: unknown,
		context: ErrorHandlerContext = {},
		options: ErrorRecoveryOptions = {}
	): unknown {
		const errorMessage = this.formatError(err);
		const errorKey = `${context.component || 'unknown'}_${context.operation || 'unknown'}`;
		
		// Track error frequency
		const currentCount = this.errorCounts.get(errorKey) || 0;
		this.errorCounts.set(errorKey, currentCount + 1);

		// Create detailed error log
		const logMessage = this.createErrorLog(errorMessage, context);
		
		// Log based on severity (frequency-based)
		if (currentCount < this.maxErrorCount) {
			warn(logMessage);
		} else if (currentCount === this.maxErrorCount) {
			warn(`${logMessage} (This error will be suppressed from now on)`);
		}

		// Show user-friendly toast if requested
		if (options.showToast && currentCount < 3) {
			this.showErrorToast(context.component || 'Application', errorMessage);
		}

		// Return fallback value if provided
		if (options.fallbackValue !== undefined) {
			return options.fallbackValue;
		}

		return undefined;
	}

	/**
	 * Safely execute a function with error handling
	 */
	public safeExecute<T>(
		fn: () => T,
		context: ErrorHandlerContext = {},
		options: ErrorRecoveryOptions = {}
	): T | undefined {
		try {
			return fn();
		} catch (err) {
			return this.handleError(err, context, options) as T | undefined;
		}
	}

	/**
	 * Safely execute an async function with error handling
	 */
	public async safeExecuteAsync<T>(
		fn: () => Promise<T>,
		context: ErrorHandlerContext = {},
		options: ErrorRecoveryOptions = {}
	): Promise<T | undefined> {
		try {
			return (await fn()) as T;
		} catch (err) {
			this.handleError(err, context, options);
			if (options.fallbackValue !== undefined) {
				return options.fallbackValue as T;
			}
			return undefined;
		}
	}

	/**
	 * Create a safe wrapper for React components
	 */
	public wrapComponent<T extends Record<string, unknown>>(
		Component: React.FC<T>,
		componentName: string
	): React.FC<T> {
		return (props: T) => {
			try {
				return Component(props);
			} catch (err) {
				this.handleError(err, { 
					component: componentName, 
					operation: 'render' 
				}, { 
					showToast: true 
				});
				
				// Return a simple error UI using React.createElement
				return React.createElement("frame", {
					Size: new UDim2(1, 0, 1, 0),
					BackgroundColor3: Color3.fromRGB(40, 40, 40),
					BackgroundTransparency: 0.5,
				}, React.createElement("textlabel", {
					Text: `Error in ${componentName}`,
					Size: new UDim2(1, 0, 1, 0),
					BackgroundTransparency: 1,
					TextColor3: Color3.fromRGB(255, 100, 100),
					TextSize: 14,
					Font: Enum.Font.SourceSans,
					TextXAlignment: Enum.TextXAlignment.Center,
				}));
			}
		};
	}

	/**
	 * Clear error counts (useful for testing or after fixes)
	 */
	public clearErrorCounts(): void {
		this.errorCounts.clear();
	}

	/**
	 * Get error statistics
	 */
	public getErrorStats(): Array<[string, number]> {
		const entries: Array<[string, number]> = [];
		this.errorCounts.forEach((count, key) => {
			entries.push([key, count]);
		});
		return entries;
	}

	private formatError(err: unknown): string {
		if (typeIs(err, "string")) {
			return err;
		} else if (typeIs(err, "table") && "message" in err) {
			return tostring(err.message);
		} else {
			return tostring(err);
		}
	}

	private createErrorLog(errorMessage: string, context: ErrorHandlerContext): string {
		const timestamp = context.timestamp || tick();
		const component = context.component || 'Unknown';
		const operation = context.operation || 'Unknown';
		
		let logMessage = `❌ [${component}] Error in ${operation}: ${errorMessage}`;
		
		if (context.userId) {
			logMessage += ` (User: ${context.userId})`;
		}
		
		logMessage += ` (Time: ${math.floor(timestamp)})`;
		
		if (context.additionalData) {
			const dataEntries: string[] = [];
			for (const [key, value] of pairs(context.additionalData)) {
				dataEntries.push(`${key}=${tostring(value)}`);
			}
			logMessage += ` (Data: ${dataEntries.join(', ')})`;
		}
		
		return logMessage;
	}

	private showErrorToast(component: string, message: string): void {
		// Disable toast for now to avoid require complications
		warn(`[ErrorHandler] Toast disabled: ${component} - ${message}`);
	}
}

// Export singleton instance
export const GlobalErrorHandler = ErrorHandler.getInstance();

// Utility functions for common patterns
export function safeCall<T>(
	fn: () => T,
	component: string,
	operation: string,
	fallback?: T
): T | undefined {
	return GlobalErrorHandler.safeExecute(
		fn,
		{ component, operation },
		{ showToast: false, fallbackValue: fallback }
	);
}

export function safeCallAsync<T>(
	fn: () => Promise<T>,
	component: string,
	operation: string,
	fallback?: T
): Promise<T | undefined> {
	return GlobalErrorHandler.safeExecuteAsync(
		fn,
		{ component, operation },
		{ showToast: false, fallbackValue: fallback }
	);
}

export function safeRender<T extends Record<string, unknown>>(
	Component: React.FC<T>,
	componentName: string
): React.FC<T> {
	return GlobalErrorHandler.wrapComponent(Component, componentName);
}