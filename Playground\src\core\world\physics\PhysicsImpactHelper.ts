import { Workspace, RunService, TweenService, Players } from "@rbxts/services";
import {
	PhysicsZoneOptions,
	GravityZoneOptions,
	ForceZoneOptions,
	BarrierZoneOptions,
	PhysicsZone,
	ForceApplication,
	ObjectType,
} from "./interfaces/PhysicsZoneOptions";

export class PhysicsImpactHelper {
	private static activeZones: Map<string, PhysicsZone> = new Map();
	private static zoneCounter: number = 0;
	private static activeForces: Map<Instance, ForceApplication[]> = new Map();
	private static updateConnection?: RBXScriptConnection;

	/**
	 * Initialize the physics system
	 */
	public static initialize(): void {
		if (this.updateConnection) return;

		this.updateConnection = RunService.Heartbeat.Connect(() => {
			this.updatePhysicsZones();
		});

		print("🌍 PhysicsImpactHelper initialized");
	}

	/**
	 * Create a gravity manipulation zone (perfect for Room ability)
	 */
	public static createGravityZone(options: GravityZoneOptions): string {
		this.zoneCounter = this.zoneCounter + 1;
		const zoneId = `gravity_${this.zoneCounter}_${tick()}`;

		const zone: PhysicsZone = {
			id: zoneId,
			options: options,
			createdAt: tick(),
			expiresAt: options.duration ? tick() + options.duration : undefined,
			affectedObjects: new Set(),
			zoneVisual: options.visualEffect ? this.createZoneVisual(options) : undefined,
		};

		this.activeZones.set(zoneId, zone);

		// Set up cleanup if temporary
		if (options.duration) {
			task.delay(options.duration, () => {
				this.removePhysicsZone(zoneId);
			});
		}

		print(`🌍 Created gravity zone: ${zoneId} with ${options.gravityMultiplier}x gravity`);
		return zoneId;
	}

	/**
	 * Create a force zone (for pushing/pulling effects)
	 */
	public static createForceZone(options: ForceZoneOptions): string {
		this.zoneCounter = this.zoneCounter + 1;
		const zoneId = `force_${this.zoneCounter}_${tick()}`;

		const zone: PhysicsZone = {
			id: zoneId,
			options: options,
			createdAt: tick(),
			expiresAt: options.duration ? tick() + options.duration : undefined,
			affectedObjects: new Set(),
			zoneVisual: options.visualEffect ? this.createZoneVisual(options) : undefined,
		};

		this.activeZones.set(zoneId, zone);

		if (options.duration) {
			task.delay(options.duration, () => {
				this.removePhysicsZone(zoneId);
			});
		}

		print(`🌍 Created force zone: ${zoneId} with ${options.forceType} force`);
		return zoneId;
	}

	/**
	 * Create a barrier zone (invisible walls, domes)
	 */
	public static createBarrierZone(options: BarrierZoneOptions): string {
		this.zoneCounter = this.zoneCounter + 1;
		const zoneId = `barrier_${this.zoneCounter}_${tick()}`;

		const zone: PhysicsZone = {
			id: zoneId,
			options: options,
			createdAt: tick(),
			expiresAt: options.duration ? tick() + options.duration : undefined,
			affectedObjects: new Set(),
			zoneVisual: options.visualEffect ? this.createBarrierVisual(options) : undefined,
		};

		this.activeZones.set(zoneId, zone);

		if (options.duration) {
			task.delay(options.duration, () => {
				this.removePhysicsZone(zoneId);
			});
		}

		print(`🌍 Created barrier zone: ${zoneId} of type ${options.barrierType}`);
		return zoneId;
	}

	/**
	 * Update all active physics zones
	 */
	private static updatePhysicsZones(): void {
		for (const [zoneId, zone] of this.activeZones) {
			this.updateZone(zone);
		}

		// Clean up expired zones
		this.cleanupExpiredZones();
	}

	/**
	 * Update a specific physics zone
	 */
	private static updateZone(zone: PhysicsZone): void {
		const objectsInZone = this.findObjectsInZone(zone);

		// Remove objects that left the zone
		for (const obj of zone.affectedObjects) {
			if (!objectsInZone.has(obj)) {
				this.removeObjectFromZone(obj, zone);
				zone.affectedObjects.delete(obj);
			}
		}

		// Add new objects that entered the zone
		for (const obj of objectsInZone) {
			if (!zone.affectedObjects.has(obj)) {
				this.addObjectToZone(obj, zone);
				zone.affectedObjects.add(obj);
			}
		}

		// Apply continuous effects to all objects in zone
		for (const obj of zone.affectedObjects) {
			this.applyZoneEffect(obj, zone);
		}
	}

	/**
	 * Find all objects within a physics zone
	 */
	private static findObjectsInZone(zone: PhysicsZone): Set<Instance> {
		const objectsInZone = new Set<Instance>();
		const options = zone.options;

		for (const objectType of options.affectedObjects) {
			const objects = this.getObjectsByType(objectType);

			for (const obj of objects) {
				if (this.isObjectInZone(obj, zone)) {
					objectsInZone.add(obj);
				}
			}
		}

		return objectsInZone;
	}

	/**
	 * Get objects by type
	 */
	private static getObjectsByType(objectType: ObjectType): Instance[] {
		const objects: Instance[] = [];

		switch (objectType) {
			case "players":
				for (const player of Players.GetPlayers()) {
					if (player.Character) {
						objects.push(player.Character);
					}
				}
				break;
			case "parts":
				for (const descendant of Workspace.GetDescendants()) {
					if (descendant.IsA("Part") && !descendant.Anchored) {
						objects.push(descendant);
					}
				}
				break;
			case "debris":
				for (const descendant of Workspace.GetDescendants()) {
					if (descendant.IsA("Part") && descendant.Name.find("debris")[0]) {
						objects.push(descendant);
					}
				}
				break;
			// Add more object types as needed
		}

		return objects;
	}

	/**
	 * Check if an object is within a zone
	 */
	private static isObjectInZone(obj: Instance, zone: PhysicsZone): boolean {
		let position: Vector3;

		if (obj.IsA("Model")) {
			const primaryPart = obj.PrimaryPart || obj.FindFirstChild("HumanoidRootPart");
			if (!primaryPart || !primaryPart.IsA("BasePart")) return false;
			position = primaryPart.Position;
		} else if (obj.IsA("Part")) {
			position = obj.Position;
		} else {
			return false;
		}

		const distance = zone.options.center.sub(position).Magnitude;
		return distance <= zone.options.radius;
	}

	/**
	 * Add an object to a physics zone
	 */
	private static addObjectToZone(obj: Instance, zone: PhysicsZone): void {
		print(`🌍 Object ${obj.Name} entered ${zone.options.zoneType} zone ${zone.id}`);

		// Initialize zone-specific effects
		switch (zone.options.zoneType) {
			case "gravity":
				this.initializeGravityEffect(obj, zone.options as GravityZoneOptions);
				break;
			case "force":
				this.initializeForceEffect(obj, zone.options as ForceZoneOptions);
				break;
			case "barrier":
				// Barriers work on collision, not continuous effect
				break;
		}
	}

	/**
	 * Remove an object from a physics zone
	 */
	private static removeObjectFromZone(obj: Instance, zone: PhysicsZone): void {
		print(`🌍 Object ${obj.Name} left ${zone.options.zoneType} zone ${zone.id}`);

		// Clean up zone-specific effects
		this.cleanupObjectEffects(obj);
	}

	/**
	 * Apply zone effects to an object
	 */
	private static applyZoneEffect(obj: Instance, zone: PhysicsZone): void {
		switch (zone.options.zoneType) {
			case "gravity":
				this.applyGravityEffect(obj, zone.options as GravityZoneOptions);
				break;
			case "force":
				this.applyForceEffect(obj, zone.options as ForceZoneOptions);
				break;
		}
	}

	/**
	 * Initialize gravity effect on an object
	 */
	private static initializeGravityEffect(obj: Instance, options: GravityZoneOptions): void {
		const part = this.getPartFromObject(obj);
		if (!part || part.Anchored) return;

		// Create custom gravity using BodyVelocity
		const bodyVelocity = new Instance("BodyVelocity");
		bodyVelocity.MaxForce = new Vector3(0, math.huge, 0);
		bodyVelocity.Velocity = new Vector3(0, 0, 0);
		bodyVelocity.Parent = part;

		const forceApp: ForceApplication = {
			target: obj,
			forceType: "BodyVelocity",
			forceObject: bodyVelocity,
		};

		if (!this.activeForces.has(obj)) {
			this.activeForces.set(obj, []);
		}
		this.activeForces.get(obj)!.push(forceApp);
	}

	/**
	 * Apply gravity effect continuously
	 */
	private static applyGravityEffect(obj: Instance, options: GravityZoneOptions): void {
		const part = this.getPartFromObject(obj);
		if (!part) return;

		const forces = this.activeForces.get(obj);
		if (!forces) return;

		const gravityForce = forces.find((f) => f.forceType === "BodyVelocity");
		if (!gravityForce || !gravityForce.forceObject.Parent) return;

		const bodyVelocity = gravityForce.forceObject as BodyVelocity;
		const gravityDirection = options.gravityDirection || new Vector3(0, -1, 0);
		const gravityStrength = 196.2 * options.gravityMultiplier; // Standard Roblox gravity

		// Apply custom gravity
		const currentVelocity = bodyVelocity.Velocity;
		const newVelocity = currentVelocity.add(gravityDirection.mul(gravityStrength * (1 / 60))); // 60fps
		bodyVelocity.Velocity = newVelocity;
	}

	/**
	 * Initialize force effect on an object
	 */
	private static initializeForceEffect(obj: Instance, options: ForceZoneOptions): void {
		const part = this.getPartFromObject(obj);
		if (!part || part.Anchored) return;

		if (options.forceType === "impulse") {
			// Apply one-time impulse
			const bodyVelocity = new Instance("BodyVelocity");
			bodyVelocity.MaxForce = new Vector3(4000, 4000, 4000);
			bodyVelocity.Velocity = options.forceDirection.mul(options.forceStrength);
			bodyVelocity.Parent = part;

			// Remove after short time
			task.delay(0.5, () => {
				if (bodyVelocity.Parent) {
					bodyVelocity.Destroy();
				}
			});
		}
	}

	/**
	 * Apply force effect continuously
	 */
	private static applyForceEffect(obj: Instance, options: ForceZoneOptions): void {
		if (options.forceType === "constant") {
			const part = this.getPartFromObject(obj);
			if (!part) return;

			// Apply constant force
			const bodyVelocity = part.FindFirstChild("BodyVelocity") as BodyVelocity;
			if (bodyVelocity) {
				bodyVelocity.Velocity = options.forceDirection.mul(options.forceStrength);
			}
		}
	}

	/**
	 * Get the main Part from an object (Model or Part)
	 */
	private static getPartFromObject(obj: Instance): Part | undefined {
		if (obj.IsA("Part")) {
			return obj;
		} else if (obj.IsA("Model")) {
			const part = obj.PrimaryPart || obj.FindFirstChild("HumanoidRootPart");
			if (part && part.IsA("Part")) {
				return part;
			}
		}
		return undefined;
	}

	/**
	 * Clean up all effects on an object
	 */
	private static cleanupObjectEffects(obj: Instance): void {
		const forces = this.activeForces.get(obj);
		if (!forces) return;

		for (const force of forces) {
			if (force.forceObject.Parent) {
				force.forceObject.Destroy();
			}
		}

		this.activeForces.delete(obj);
	}

	/**
	 * Create visual representation of a zone
	 */
	private static createZoneVisual(options: PhysicsZoneOptions): Part {
		const visual = new Instance("Part");
		visual.Name = `${options.zoneType}_zone_visual`;
		visual.Size = new Vector3(options.radius * 2, options.radius * 2, options.radius * 2);
		visual.Position = options.center;
		visual.Shape = Enum.PartType.Ball;
		visual.Material = Enum.Material.ForceField;
		visual.Transparency = 0.7;
		visual.CanCollide = false;
		visual.Anchored = true;

		// Color based on zone type
		switch (options.zoneType) {
			case "gravity":
				visual.Color = new Color3(0.5, 0, 1); // Purple
				break;
			case "force":
				visual.Color = new Color3(1, 0.5, 0); // Orange
				break;
			case "barrier":
				visual.Color = new Color3(0, 0.5, 1); // Blue
				break;
		}

		visual.Parent = Workspace;
		return visual;
	}

	/**
	 * Create barrier visual
	 */
	private static createBarrierVisual(options: BarrierZoneOptions): Part {
		const visual = this.createZoneVisual(options);

		if (options.barrierType === "dome") {
			visual.Shape = Enum.PartType.Ball;
		} else if (options.barrierType === "cylinder") {
			visual.Shape = Enum.PartType.Cylinder;
		}

		return visual;
	}

	/**
	 * Remove a physics zone
	 */
	public static removePhysicsZone(zoneId: string): void {
		const zone = this.activeZones.get(zoneId);
		if (!zone) return;

		// Clean up all affected objects
		for (const obj of zone.affectedObjects) {
			this.cleanupObjectEffects(obj);
		}

		// Remove visual
		if (zone.zoneVisual && zone.zoneVisual.Parent) {
			zone.zoneVisual.Destroy();
		}

		this.activeZones.delete(zoneId);
		print(`🌍 Removed physics zone: ${zoneId}`);
	}

	/**
	 * Clean up expired zones
	 */
	private static cleanupExpiredZones(): void {
		const currentTime = tick();
		for (const [zoneId, zone] of this.activeZones) {
			if (zone.expiresAt && currentTime >= zone.expiresAt) {
				this.removePhysicsZone(zoneId);
			}
		}
	}

	/**
	 * Get all active physics zones
	 */
	public static getActiveZones(): Map<string, PhysicsZone> {
		return this.activeZones;
	}

	/**
	 * Shutdown the physics system
	 */
	public static shutdown(): void {
		if (this.updateConnection) {
			this.updateConnection.Disconnect();
			this.updateConnection = undefined;
		}

		// Clean up all zones
		const zoneIds: string[] = [];
		for (const [zoneId] of this.activeZones) {
			zoneIds.push(zoneId);
		}
		for (const zoneId of zoneIds) {
			this.removePhysicsZone(zoneId);
		}

		print("🌍 PhysicsImpactHelper shutdown");
	}
}
