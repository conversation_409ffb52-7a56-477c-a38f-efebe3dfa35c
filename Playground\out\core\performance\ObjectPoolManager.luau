-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local BaseService = TS.import(script, game:GetService("ReplicatedStorage"), "core", "foundation", "BaseService").BaseService
local Result = TS.import(script, game:GetService("ReplicatedStorage"), "core", "foundation", "types", "Result").Result
local createError = TS.import(script, game:GetService("ReplicatedStorage"), "core", "foundation", "types", "BrandedTypes").createError
--[[
	*
	 * Generic object pool for optimizing creation and destruction of frequently used objects
	 
]]
local ObjectPool
do
	ObjectPool = setmetatable({}, {
		__tostring = function()
			return "ObjectPool"
		end,
	})
	ObjectPool.__index = ObjectPool
	function ObjectPool.new(...)
		local self = setmetatable({}, ObjectPool)
		return self:constructor(...) or self
	end
	function ObjectPool:constructor(factory, config)
		if config == nil then
			config = {}
		end
		self.pool = {}
		self.activeObjects = {}
		self.lastShrinkTime = 0
		self.factory = factory
		local _object = {
			initialSize = 10,
			maxSize = 100,
			expandSize = 5,
			shrinkThreshold = 0.25,
			shrinkSize = 5,
			enableAutoShrink = true,
			autoShrinkInterval = 30,
		}
		for _k, _v in config do
			_object[_k] = _v
		end
		self.config = _object
		self.stats = {
			totalCreated = 0,
			totalDestroyed = 0,
			currentActive = 0,
			currentPooled = 0,
			peakActive = 0,
			poolHits = 0,
			poolMisses = 0,
			expansions = 0,
			shrinks = 0,
		}
		self:initializePool()
	end
	function ObjectPool:acquire()
		local obj
		if #self.pool > 0 then
			local _exp = self.pool
			-- ▼ Array.pop ▼
			local _length = #_exp
			local _result = _exp[_length]
			_exp[_length] = nil
			-- ▲ Array.pop ▲
			obj = _result
			self.stats.poolHits += 1
		else
			obj = self:createNewObject()
			self.stats.poolMisses += 1
			-- Consider expanding the pool if we're getting too many misses
			if self:shouldExpand() then
				self:expandPool()
			end
		end
		local _activeObjects = self.activeObjects
		local _obj = obj
		_activeObjects[_obj] = true
		obj:setActive(true)
		-- ▼ ReadonlySet.size ▼
		local _size = 0
		for _ in self.activeObjects do
			_size += 1
		end
		-- ▲ ReadonlySet.size ▲
		self.stats.currentActive = _size
		self.stats.currentPooled = #self.pool
		if self.stats.currentActive > self.stats.peakActive then
			self.stats.peakActive = self.stats.currentActive
		end
		return obj
	end
	function ObjectPool:release(obj)
		local _activeObjects = self.activeObjects
		local _obj = obj
		if not (_activeObjects[_obj] ~= nil) then
			warn("Attempted to release object that wasn't acquired from this pool")
			return nil
		end
		local _activeObjects_1 = self.activeObjects
		local _obj_1 = obj
		_activeObjects_1[_obj_1] = nil
		obj:setActive(false)
		obj:reset()
		-- Only add back to pool if we haven't exceeded max size
		if #self.pool < self.config.maxSize then
			local _pool = self.pool
			local _obj_2 = obj
			table.insert(_pool, _obj_2)
		else
			self:destroyObject(obj)
		end
		-- ▼ ReadonlySet.size ▼
		local _size = 0
		for _ in self.activeObjects do
			_size += 1
		end
		-- ▲ ReadonlySet.size ▲
		self.stats.currentActive = _size
		self.stats.currentPooled = #self.pool
		-- Consider shrinking if enabled and it's time
		if self.config.enableAutoShrink and self:shouldShrink() then
			self:shrinkPool()
		end
	end
	function ObjectPool:releaseAll()
		local _array = {}
		local _length = #_array
		for _v in self.activeObjects do
			_length += 1
			_array[_length] = _v
		end
		local activeArray = _array
		-- ▼ ReadonlyArray.forEach ▼
		local _callback = function(obj)
			return self:release(obj)
		end
		for _k, _v in activeArray do
			_callback(_v, _k - 1, activeArray)
		end
		-- ▲ ReadonlyArray.forEach ▲
	end
	function ObjectPool:clear()
		-- Release all active objects first
		self:releaseAll()
		-- Destroy all pooled objects
		local _exp = self.pool
		-- ▼ ReadonlyArray.forEach ▼
		local _callback = function(obj)
			return self:destroyObject(obj)
		end
		for _k, _v in _exp do
			_callback(_v, _k - 1, _exp)
		end
		-- ▲ ReadonlyArray.forEach ▲
		self.pool = {}
		self.stats.currentPooled = 0
	end
	function ObjectPool:getStats()
		local _object = table.clone(self.stats)
		setmetatable(_object, nil)
		return _object
	end
	function ObjectPool:getConfig()
		local _object = table.clone(self.config)
		setmetatable(_object, nil)
		return _object
	end
	function ObjectPool:updateConfig(newConfig)
		local _object = table.clone(self.config)
		setmetatable(_object, nil)
		for _k, _v in newConfig do
			_object[_k] = _v
		end
		self.config = _object
	end
	function ObjectPool:getEfficiency()
		local totalRequests = self.stats.poolHits + self.stats.poolMisses
		local hitRate = if totalRequests > 0 then self.stats.poolHits / totalRequests else 0
		local totalCapacity = self.stats.currentActive + self.stats.currentPooled
		local utilizationRate = if totalCapacity > 0 then self.stats.currentActive / totalCapacity else 0
		return {
			hitRate = hitRate,
			utilizationRate = utilizationRate,
			averagePoolSize = (self.stats.currentActive + self.stats.currentPooled) / 2,
		}
	end
	function ObjectPool:initializePool()
		do
			local i = 0
			local _shouldIncrement = false
			while true do
				if _shouldIncrement then
					i += 1
				else
					_shouldIncrement = true
				end
				if not (i < self.config.initialSize) then
					break
				end
				local obj = self:createNewObject()
				local _exp = self.pool
				table.insert(_exp, obj)
			end
		end
		self.stats.currentPooled = #self.pool
	end
	function ObjectPool:createNewObject()
		local obj = self.factory()
		obj:setActive(false)
		self.stats.totalCreated += 1
		return obj
	end
	function ObjectPool:destroyObject(obj)
		-- In Roblox, we can't explicitly destroy objects, but we can mark them for GC
		obj:setActive(false)
		self.stats.totalDestroyed += 1
	end
	function ObjectPool:shouldExpand()
		-- Expand if we're consistently getting misses and haven't reached max size
		local totalRequests = self.stats.poolHits + self.stats.poolMisses
		local recentMissRate = if totalRequests > 10 then self.stats.poolMisses / totalRequests else 0
		return recentMissRate > 0.3 and #self.pool + self.config.expandSize <= self.config.maxSize
	end
	function ObjectPool:expandPool()
		do
			local i = 0
			local _shouldIncrement = false
			while true do
				if _shouldIncrement then
					i += 1
				else
					_shouldIncrement = true
				end
				if not (i < self.config.expandSize) then
					break
				end
				if #self.pool >= self.config.maxSize then
					break
				end
				local obj = self:createNewObject()
				local _exp = self.pool
				table.insert(_exp, obj)
			end
		end
		self.stats.expansions += 1
		self.stats.currentPooled = #self.pool
		print(`🔧 Pool expanded to {#self.pool} objects`)
	end
	function ObjectPool:shouldShrink()
		local currentTime = tick()
		-- Only shrink at intervals
		if currentTime - self.lastShrinkTime < self.config.autoShrinkInterval then
			return false
		end
		-- Shrink if utilization is low
		local totalCapacity = self.stats.currentActive + self.stats.currentPooled
		local utilization = if totalCapacity > 0 then self.stats.currentActive / totalCapacity else 0
		return utilization < self.config.shrinkThreshold and #self.pool > self.config.shrinkSize
	end
	function ObjectPool:shrinkPool()
		local shrinkAmount = math.min(self.config.shrinkSize, #self.pool - self.config.shrinkSize)
		do
			local i = 0
			local _shouldIncrement = false
			while true do
				if _shouldIncrement then
					i += 1
				else
					_shouldIncrement = true
				end
				if not (i < shrinkAmount) then
					break
				end
				local _exp = self.pool
				-- ▼ Array.pop ▼
				local _length = #_exp
				local _result = _exp[_length]
				_exp[_length] = nil
				-- ▲ Array.pop ▲
				local obj = _result
				if obj then
					self:destroyObject(obj)
				end
			end
		end
		self.stats.shrinks += 1
		self.stats.currentPooled = #self.pool
		self.lastShrinkTime = tick()
		print(`🔧 Pool shrunk to {#self.pool} objects`)
	end
end
--[[
	*
	 * Object Pool Manager service for managing multiple object pools
	 
]]
local ObjectPoolManager
do
	local super = BaseService
	ObjectPoolManager = setmetatable({}, {
		__tostring = function()
			return "ObjectPoolManager"
		end,
		__index = super,
	})
	ObjectPoolManager.__index = ObjectPoolManager
	function ObjectPoolManager.new(...)
		local self = setmetatable({}, ObjectPoolManager)
		return self:constructor(...) or self
	end
	function ObjectPoolManager:constructor()
		super.constructor(self, "ObjectPoolManager")
		self.pools = {}
	end
	function ObjectPoolManager:getInstance()
		if not ObjectPoolManager.instance then
			ObjectPoolManager.instance = ObjectPoolManager.new()
		end
		return ObjectPoolManager.instance
	end
	ObjectPoolManager.onInitialize = TS.async(function(self)
		local _exitType, _returns = TS.try(function()
			print("🏊 Object Pool Manager initialized")
			return TS.TRY_RETURN, { Result:ok(nil) }
		end, function(error)
			return TS.TRY_RETURN, { Result:err(createError(`Failed to initialize ObjectPoolManager: {error}`)) }
		end)
		if _exitType then
			return unpack(_returns)
		end
	end)
	ObjectPoolManager.onShutdown = TS.async(function(self)
		-- Clear all pools
		local _exp = self.pools
		-- ▼ ReadonlyMap.forEach ▼
		local _callback = function(pool)
			return pool:clear()
		end
		for _k, _v in _exp do
			_callback(_v, _k, _exp)
		end
		-- ▲ ReadonlyMap.forEach ▲
		table.clear(self.pools)
		print("🏊 Object Pool Manager shutdown")
		return Result:ok(nil)
	end)
	function ObjectPoolManager:registerPool(name, factory, config)
		local _pools = self.pools
		local _name = name
		if _pools[_name] ~= nil then
			warn(`Pool '{name}' already exists. Replacing existing pool.`)
			local _pools_1 = self.pools
			local _name_1 = name
			local _result = _pools_1[_name_1]
			if _result ~= nil then
				_result:clear()
			end
		end
		local pool = ObjectPool.new(factory, config)
		local _pools_1 = self.pools
		local _name_1 = name
		_pools_1[_name_1] = pool
		print(`🏊 Registered object pool: {name}`)
		return pool
	end
	function ObjectPoolManager:getPool(name)
		local _pools = self.pools
		local _name = name
		return _pools[_name]
	end
	function ObjectPoolManager:removePool(name)
		local _pools = self.pools
		local _name = name
		local pool = _pools[_name]
		if pool then
			pool:clear()
			local _pools_1 = self.pools
			local _name_1 = name
			_pools_1[_name_1] = nil
			print(`🏊 Removed object pool: {name}`)
			return true
		end
		return false
	end
	function ObjectPoolManager:getPoolNames()
		local names = {}
		local _exp = self.pools
		-- ▼ ReadonlyMap.forEach ▼
		local _callback = function(_, name)
			local _name = name
			table.insert(names, _name)
			return #names
		end
		for _k, _v in _exp do
			_callback(_v, _k, _exp)
		end
		-- ▲ ReadonlyMap.forEach ▲
		return names
	end
	function ObjectPoolManager:getAllStats()
		local stats = {}
		local _exp = self.pools
		-- ▼ ReadonlyMap.forEach ▼
		local _callback = function(pool, name)
			stats[name] = pool:getStats()
		end
		for _k, _v in _exp do
			_callback(_v, _k, _exp)
		end
		-- ▲ ReadonlyMap.forEach ▲
		return stats
	end
	function ObjectPoolManager:getAllEfficiency()
		local efficiency = {}
		local _exp = self.pools
		-- ▼ ReadonlyMap.forEach ▼
		local _callback = function(pool, name)
			efficiency[name] = pool:getEfficiency()
		end
		for _k, _v in _exp do
			_callback(_v, _k, _exp)
		end
		-- ▲ ReadonlyMap.forEach ▲
		return efficiency
	end
	function ObjectPoolManager:getReport()
		local report = "🏊 Object Pool Manager Report\n"
		-- ▼ ReadonlyMap.size ▼
		local _size = 0
		for _ in self.pools do
			_size += 1
		end
		-- ▲ ReadonlyMap.size ▲
		report ..= `Total Pools: {_size}\n\n`
		local _exp = self.pools
		-- ▼ ReadonlyMap.forEach ▼
		local _callback = function(pool, name)
			local stats = pool:getStats()
			local efficiency = pool:getEfficiency()
			report ..= `📋 Pool: {name}\n`
			report ..= `  Active: {stats.currentActive} | Pooled: {stats.currentPooled} | Peak: {stats.peakActive}\n`
			report ..= `  Hit Rate: {string.format("%.1f", efficiency.hitRate * 100)}% | Utilization: {string.format("%.1f", efficiency.utilizationRate * 100)}%\n`
			report ..= `  Total Created: {stats.totalCreated} | Expansions: {stats.expansions} | Shrinks: {stats.shrinks}\n\n`
		end
		for _k, _v in _exp do
			_callback(_v, _k, _exp)
		end
		-- ▲ ReadonlyMap.forEach ▲
		return report
	end
end
-- Utility base class for creating poolable objects
local PoolableBase
do
	PoolableBase = {}
	function PoolableBase:constructor()
		self.active = false
	end
	function PoolableBase:isActive()
		return self.active
	end
	function PoolableBase:setActive(active)
		self.active = active
		if active then
			self:onActivate()
		else
			self:onDeactivate()
		end
	end
	function PoolableBase:onActivate()
		-- Override in subclasses if needed
	end
	function PoolableBase:onDeactivate()
		-- Override in subclasses if needed
	end
end
-- Global object pooling functions for easy access
local ObjectPooling = {
	getInstance = function()
		return ObjectPoolManager:getInstance()
	end,
	registerPool = function(name, factory, config)
		return ObjectPoolManager:getInstance():registerPool(name, factory, config)
	end,
	getPool = function(name)
		return ObjectPoolManager:getInstance():getPool(name)
	end,
	getReport = function()
		return ObjectPoolManager:getInstance():getReport()
	end,
}
-- Export types for external use
return {
	ObjectPoolManager = ObjectPoolManager,
	PoolableBase = PoolableBase,
	ObjectPooling = ObjectPooling,
}
