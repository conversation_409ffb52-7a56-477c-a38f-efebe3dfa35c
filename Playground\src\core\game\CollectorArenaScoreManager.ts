import { Players } from "@rbxts/services";

export interface GameScore {
	playerName: string;
	score: number;
	coinsCollected: number;
	enemiesDefeated: number;
	gameTime: number;
	timestamp: number;
}

export class CollectorArenaScoreManager {
	private static instance: CollectorArenaScoreManager;
	private localScores: GameScore[] = [];
	private personalBest?: GameScore;

	private constructor() {
		// Simple in-memory storage for now
		// TODO: Integrate with proper DataStore when needed
	}

	public static getInstance(): CollectorArenaScoreManager {
		if (!CollectorArenaScoreManager.instance) {
			CollectorArenaScoreManager.instance = new CollectorArenaScoreManager();
		}
		return CollectorArenaScoreManager.instance;
	}

	public async saveScore(gameScore: GameScore): Promise<boolean> {
		try {
			const player = Players.LocalPlayer;
			if (!player) return false;

			const scoreData = {
				...gameScore,
				timestamp: tick(),
			};

			// Save to local storage (in-memory for now)
			this.localScores.unshift(scoreData);
			
			// Keep only last 10 games
			if (this.localScores.size() > 10) {
				// Remove oldest entries
				while (this.localScores.size() > 10) {
					this.localScores.pop();
				}
			}

			// Update personal best
			if (!this.personalBest || this.personalBest.score < gameScore.score) {
				this.personalBest = scoreData;
				print(`🏆 New personal best: ${gameScore.score} points!`);
			}
			
			print(`💾 Score saved locally: ${gameScore.score} points`);
			return true;
		} catch (error) {
			warn(`Failed to save score: ${error}`);
			return false;
		}
	}

	public async getPersonalBest(): Promise<GameScore | undefined> {
		return this.personalBest;
	}

	public async getRecentGames(): Promise<GameScore[]> {
		// Convert to regular JavaScript array for easier handling
		const games: GameScore[] = [];
		for (const score of this.localScores) {
			games.push(score);
		}
		return games;
	}
}