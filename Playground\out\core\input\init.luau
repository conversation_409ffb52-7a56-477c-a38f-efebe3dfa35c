-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local exports = {}
local _InputManager = TS.import(script, game:GetService("ReplicatedStorage"), "core", "input", "InputManager")
exports.InputManager = _InputManager.InputManager
exports.Input = _InputManager.Input
local _InputManager_1 = TS.import(script, game:GetService("ReplicatedStorage"), "core", "input", "InputManager")
exports.InputType = _InputManager_1.InputType
exports.InputContext = _InputManager_1.InputContext
return exports
