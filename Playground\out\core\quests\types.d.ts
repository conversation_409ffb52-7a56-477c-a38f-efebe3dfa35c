export declare enum QuestType {
    Main = "Main",
    Side = "Side",
    Daily = "Daily",
    Weekly = "Weekly",
    Achievement = "Achievement",
    Tutorial = "Tutorial",
    Event = "Event"
}
export declare enum QuestStatus {
    NotStarted = "NotStarted",
    Available = "Available",
    Active = "Active",
    Completed = "Completed",
    Failed = "Failed",
    Abandoned = "Abandoned",
    Expired = "Expired"
}
export declare enum ObjectiveType {
    KillTarget = "KillTarget",
    CollectItem = "CollectItem",
    ReachLocation = "ReachLocation",
    TalkToNPC = "TalkToNPC",
    UseItem = "UseItem",
    Craft = "Craft",
    Timer = "Timer",
    Custom = "Custom"
}
export interface QuestReward {
    type: "item" | "currency" | "experience" | "unlock";
    itemId?: string;
    quantity?: number;
    currencyType?: string;
    amount?: number;
    experiencePoints?: number;
    unlockId?: string;
}
export interface QuestObjective {
    id: string;
    type: ObjectiveType;
    description: string;
    targetId?: string;
    targetCount: number;
    currentCount: number;
    isOptional: boolean;
    isCompleted: boolean;
    isHidden: boolean;
    requirements?: {
        prerequisites?: string[];
        items?: Record<string, number>;
        level?: number;
    };
}
export interface QuestDefinition {
    id: string;
    name: string;
    description: string;
    type: QuestType;
    category: string;
    level: number;
    maxLevel?: number;
    isRepeatable: boolean;
    cooldownHours?: number;
    expirationHours?: number;
    prerequisites: string[];
    objectives: QuestObjective[];
    rewards: QuestReward[];
    bonusRewards?: QuestReward[];
    autoStart: boolean;
    autoComplete: boolean;
    tags: string[];
    giver?: string;
    location?: Vector3;
    iconId?: string;
    soundId?: string;
}
export interface QuestProgress {
    questId: string;
    status: QuestStatus;
    startTime?: number;
    completionTime?: number;
    expirationTime?: number;
    objectives: Map<string, QuestObjective>;
    timesCompleted: number;
    lastCompletionTime?: number;
}
export interface QuestEvent {
    type: "started" | "completed" | "failed" | "abandoned" | "objective_completed" | "expired";
    questId: string;
    objectiveId?: string;
    timestamp: number;
    playerId?: string;
}
export interface QuestConfiguration {
    enableAutoProgress: boolean;
    enableNotifications: boolean;
    maxActiveQuests: number;
    enableQuestLog: boolean;
    enableQuestTracking: boolean;
    saveProgress: boolean;
}
