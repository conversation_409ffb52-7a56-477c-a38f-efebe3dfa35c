import * as React from "@rbxts/react";
import { Button, VerticalFrame, ClientState, useUIState } from "../../core";
import { WorldTestingPanel } from "./WorldTestingPanel";
import { DebugPanel } from "./DebugPanel";
import { CollectorArenaUI } from "./CollectorArenaUI";
import { ResponsiveManager } from "../../core/gui/layout/ResponsiveManager";

interface BottomLeftGridProps {
	onTestClick: () => void;
	onHelloClick: () => void;
}

export function BottomLeftGrid(_props: BottomLeftGridProps) {
	// Use Core state management instead of local state - with safety checks
	const worldTestingState = useUIState("worldTestingPanel");
	const debugState = useUIState("debugPanel");
	const [debugPanelOpen, setDebugPanelOpen] = React.useState(false);
	const [gameUIOpen, setGameUIOpen] = React.useState(false);

	// Get responsive manager for dynamic positioning (memoized) - with safety check
	const responsiveManager = React.useMemo(() => {
		try {
			return ResponsiveManager.getInstance();
		} catch (error) {
			warn(`❌ Failed to get ResponsiveManager: ${error}`);
			return undefined;
		}
	}, []);
	
	// Memoize expensive calculations - with safety checks
	const responsiveSettings = React.useMemo(() => {
		if (!responsiveManager) {
			// Fallback values if ResponsiveManager fails
			return {
				containerWidth: 120,
				containerHeight: 250, // Increased height for the new button
				marginLeft: 16,
				marginBottom: 20,
			};
		}

		try {
			const safeAreaInsets = responsiveManager.getSafeAreaInsets();
			const containerWidth = responsiveManager.isMobile() ? 100 : 120;
			const containerHeight = responsiveManager.isMobile() ? 200 : 250; // Increased for new button
			const marginLeft = responsiveManager.getResponsiveMargin(16);
			const marginBottom = responsiveManager.getResponsiveMargin(20) + safeAreaInsets.bottom;
			
			return {
				containerWidth,
				containerHeight,
				marginLeft,
				marginBottom,
			};
		} catch (error) {
			warn(`❌ Failed to calculate responsive settings: ${error}`);
			// Fallback values
			return {
				containerWidth: 120,
				containerHeight: 250, // Increased height for the new button
				marginLeft: 16,
				marginBottom: 20,
			};
		}
	}, [responsiveManager]);

	// Memoize button handlers with error handling
	const handleWorldClick = React.useCallback(() => {
		try {
			ClientState.updateUI("worldTestingPanel", { isOpen: true });
		} catch (error) {
			warn(`❌ Failed to update world testing panel state: ${error}`);
		}
	}, []);

	const handleDebugClick = React.useCallback(() => {
		try {
			setDebugPanelOpen(true);
		} catch (error) {
			warn(`❌ Failed to open debug panel: ${error}`);
		}
	}, []);

	const handleGameClick = React.useCallback(() => {
		try {
			setGameUIOpen(true);
		} catch (error) {
			warn(`❌ Failed to open game UI: ${error}`);
		}
	}, []);

	// Memoize frame props
	const frameProps = React.useMemo(() => ({
		backgroundTransparency: 1,
		size: new UDim2(0, responsiveSettings.containerWidth, 0, responsiveSettings.containerHeight),
		position: new UDim2(0, responsiveSettings.marginLeft, 1, -responsiveSettings.marginBottom),
		anchorPoint: new Vector2(0, 1),
		spacing: 8,
		padding: 0,
		responsive: true,
		responsiveMargin: true,
	}), [responsiveSettings]);

	return (
		<>
			<VerticalFrame {...frameProps}>
				<Button
					text="🌍 World"
					variant="primary"
					onClick={handleWorldClick}
					LayoutOrder={5}
					responsive={true}
				/>

				<Button
					text="🏟️ Game"
					variant="primary"
					onClick={handleGameClick}
					LayoutOrder={6}
					responsive={true}
				/>

				<Button 
					text="🔧 Debug" 
					variant="secondary"
					onClick={handleDebugClick}
					LayoutOrder={7}
					responsive={true}
				/>
			</VerticalFrame>

			<WorldTestingPanel />

			<CollectorArenaUI isOpen={gameUIOpen} onClose={() => setGameUIOpen(false)} />

			<DebugPanel isOpen={debugPanelOpen} onClose={() => setDebugPanelOpen(false)} />
		</>
	);
}
