-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local React = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "react")
local _core = TS.import(script, game:GetService("ReplicatedStorage"), "core")
local Modal = _core.Modal
local Button = _core.Button
local Label = _core.Label
local VerticalFrame = _core.VerticalFrame
local COLORS = _core.COLORS
local SIZES = _core.SIZES
local GameModeManager = _core.GameModeManager
local GameMode = _core.GameMode
local CollectorArenaGame = _core.CollectorArenaGame
local PlaygroundController = _core.PlaygroundController
local ToastService = _core.ToastService
local function CollectorArenaUI(_param)
	local isOpen = _param.isOpen
	local onClose = _param.onClose
	local gameManager = React.useState(function()
		return GameModeManager:getInstance()
	end)
	local arenaGame = React.useState(function()
		return CollectorArenaGame.new()
	end)
	local playgroundController = React.useState(function()
		return PlaygroundController.new()
	end)
	local gameState, setGameState = React.useState(function()
		return arenaGame:getGameState()
	end)
	local currentMode, setCurrentMode = React.useState(function()
		return gameManager:getCurrentMode()
	end)
	local isGameActive, setIsGameActive = React.useState(function()
		return gameManager:isGameActiveState()
	end)
	-- Initialize game controllers
	React.useEffect(function()
		gameManager:registerGameController(GameMode.CollectorArena, arenaGame)
		gameManager:registerGameController(GameMode.Playground, playgroundController)
		print("🎮 [CollectorArenaUI] Game controllers registered")
	end, { gameManager, arenaGame, playgroundController })
	-- Poll for state changes (simple approach using game.GetService)
	React.useEffect(function()
		local heartbeat = game:GetService("RunService").Heartbeat:Connect(function()
			setGameState(arenaGame:getGameState())
			setCurrentMode(gameManager:getCurrentMode())
			setIsGameActive(gameManager:isGameActiveState())
		end)
		return function()
			return heartbeat:Disconnect()
		end
	end, { arenaGame, gameManager })
	local handleStartGame = React.useCallback(TS.async(function()
		local _exitType, _returns = TS.try(function()
			-- Switch to Collector Arena mode if not already
			if currentMode ~= GameMode.CollectorArena then
				local switchResult = TS.await(gameManager:switchToMode(GameMode.CollectorArena))
				if switchResult:isError() then
					ToastService.showError("Game Error", `Failed to switch to game mode: {switchResult:getError().message}`)
					return TS.TRY_RETURN, {}
				end
			end
			-- Start the game
			local startResult = TS.await(gameManager:startCurrentGame())
			if startResult:isError() then
				ToastService.showError("Game Error", `Failed to start game: {startResult:getError().message}`)
				return TS.TRY_RETURN, {}
			end
			ToastService.showSuccess("Game Started", "Collector Arena has begun! Collect coins and avoid enemies!")
			onClose()
		end, function(error)
			ToastService.showError("Game Error", `Unexpected error: {error}`)
		end)
		if _exitType then
			return unpack(_returns)
		end
	end), { gameManager, currentMode, onClose })
	local handleStopGame = React.useCallback(TS.async(function()
		local _exitType, _returns = TS.try(function()
			local stopResult = TS.await(gameManager:stopCurrentGame())
			if stopResult:isError() then
				ToastService.showError("Game Error", `Failed to stop game: {stopResult:getError().message}`)
				return TS.TRY_RETURN, {}
			end
			ToastService.showInfo("Game Stopped", `Final Score: {gameState.score} points!`)
		end, function(error)
			ToastService.showError("Game Error", `Unexpected error: {error}`)
		end)
		if _exitType then
			return unpack(_returns)
		end
	end), { gameManager, gameState.score })
	local handleSwitchToPlayground = React.useCallback(TS.async(function()
		local _exitType, _returns = TS.try(function()
			local switchResult = TS.await(gameManager:switchToMode(GameMode.Playground))
			if switchResult:isError() then
				ToastService.showError("Mode Error", `Failed to switch to playground: {switchResult:getError().message}`)
				return TS.TRY_RETURN, {}
			end
			ToastService.showInfo("Mode Changed", "Switched back to Playground mode")
			onClose()
		end, function(error)
			ToastService.showError("Mode Error", `Unexpected error: {error}`)
		end)
		if _exitType then
			return unpack(_returns)
		end
	end), { gameManager, onClose })
	local formatTime = function(seconds)
		local mins = math.floor(seconds / 60)
		local secs = math.floor(seconds % 60)
		return `{mins}:{if secs < 10 then "0" else ""}{secs}`
	end
	return React.createElement(Modal, {
		isOpen = isOpen,
		onClose = onClose,
		title = "🏟️ Collector Arena",
	}, React.createElement(VerticalFrame, {
		backgroundTransparency = 1,
		size = UDim2.new(1, 0, 1, 0),
		spacing = SIZES.margin,
		padding = SIZES.padding,
	}, React.createElement(Label, {
		text = `Current Mode: {currentMode}`,
		textColor = if currentMode == GameMode.CollectorArena then COLORS.success else COLORS.primary,
		layoutOrder = 1,
	}), currentMode == GameMode.CollectorArena and (React.createElement(VerticalFrame, {
		backgroundColor = COLORS.bg.surface,
		size = UDim2.new(1, 0, 0, 150),
		spacing = SIZES.margin,
		padding = SIZES.padding,
		layoutOrder = 2,
	}, React.createElement(Label, {
		text = "Game Statistics",
		textColor = COLORS.text.main,
	}), React.createElement(Label, {
		text = `Score: {gameState.score} | Time: {formatTime(gameState.timeRemaining)}`,
		textColor = COLORS.text.main,
	}), React.createElement(Label, {
		text = `Coins: {gameState.coinsCollected} | Enemies: {gameState.enemiesDefeated}`,
		textColor = COLORS.text.main,
	}), React.createElement(Label, {
		text = `Difficulty Level: {gameState.difficulty}`,
		textColor = COLORS.info,
	}))), React.createElement(VerticalFrame, {
		backgroundColor = COLORS.bg.surface,
		size = UDim2.new(1, 0, 0, 120),
		spacing = SIZES.margin,
		padding = SIZES.padding,
		layoutOrder = 3,
	}, React.createElement(Label, {
		text = "How to Play",
		textColor = COLORS.text.main,
	}), React.createElement(Label, {
		text = "• Move around to collect yellow coins (+10 points)",
		textColor = COLORS.text.main,
	}), React.createElement(Label, {
		text = "• Avoid red enemies (they get faster over time)",
		textColor = COLORS.text.main,
	}), React.createElement(Label, {
		text = "• Use abilities to defeat enemies (+25 points)",
		textColor = COLORS.text.main,
	}), React.createElement(Label, {
		text = "• Survive for 2 minutes to win!",
		textColor = COLORS.text.main,
	})), React.createElement(VerticalFrame, {
		size = UDim2.new(1, 0, 0, 120),
		spacing = SIZES.margin,
		layoutOrder = 4,
	}, if currentMode == GameMode.CollectorArena then (if gameState.isGameActive then (React.createElement(Button, {
		text = "🛑 Stop Game",
		variant = "danger",
		onClick = handleStopGame,
		size = UDim2.new(1, 0, 0, 40),
	})) else (React.createElement(Button, {
		text = "🎮 Start Game",
		variant = "primary",
		onClick = handleStartGame,
		size = UDim2.new(1, 0, 0, 40),
	}))) else (React.createElement(Button, {
		text = "🏟️ Switch to Collector Arena",
		variant = "primary",
		onClick = handleStartGame,
		size = UDim2.new(1, 0, 0, 40),
	})), currentMode == GameMode.CollectorArena and not gameState.isGameActive and (React.createElement(Button, {
		text = "🛝 Back to Playground",
		variant = "secondary",
		onClick = handleSwitchToPlayground,
		size = UDim2.new(1, 0, 0, 40),
		disabled = not gameManager:canSwitchModeState(),
	})))))
end
return {
	CollectorArenaUI = CollectorArenaUI,
}
