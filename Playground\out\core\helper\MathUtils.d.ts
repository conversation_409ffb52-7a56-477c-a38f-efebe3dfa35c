/**
 * Advanced mathematical utilities for game development
 */
export declare class MathUtils {
    /**
     * Linear interpolation between two values
     */
    static lerp(a: number, b: number, t: number): number;
    /**
     * Vector3 linear interpolation
     */
    static lerpVector3(a: Vector3, b: Vector3, t: number): Vector3;
    /**
     * Clamp a value between min and max
     */
    static clamp(value: number, min: number, max: number): number;
    /**
     * Smooth step interpolation (eased cubic curve)
     */
    static smoothStep(t: number): number;
    /**
     * Smoother step interpolation (eased quintic curve)
     */
    static smootherStep(t: number): number;
    /**
     * Map a value from one range to another
     */
    static map(value: number, inMin: number, inMax: number, outMin: number, outMax: number): number;
    /**
     * Calculate distance between two Vector3 points
     */
    static distance(a: Vector3, b: Vector3): number;
    /**
     * Calculate squared distance (faster for comparisons)
     */
    static distanceSquared(a: Vector3, b: Vector3): number;
    /**
     * Check if a point is within a sphere
     */
    static isInSphere(point: Vector3, center: Vector3, radius: number): boolean;
    /**
     * Check if a point is within a box
     */
    static isInBox(point: Vector3, boxCenter: Vector3, boxSize: Vector3): boolean;
    /**
     * Generate random point within a sphere
     */
    static randomPointInSphere(center: Vector3, radius: number): Vector3;
    /**
     * Generate random point on sphere surface
     */
    static randomPointOnSphere(center: Vector3, radius: number): Vector3;
    /**
     * Convert degrees to radians
     */
    static deg2rad(degrees: number): number;
    /**
     * Convert radians to degrees
     */
    static rad2deg(radians: number): number;
    /**
     * Calculate angle between two vectors (in radians)
     */
    static angleBetween(a: Vector3, b: Vector3): number;
    /**
     * Rotate a Vector3 around Y axis
     */
    static rotateAroundY(vector: Vector3, angleRadians: number): Vector3;
    /**
     * Round number to specified decimal places
     */
    static round(value: number, decimals?: number): number;
    /**
     * Check if number is approximately equal (within epsilon)
     */
    static approximately(a: number, b: number, epsilon?: number): boolean;
    /**
     * Wrap angle to -π to π range
     */
    static wrapAngle(angle: number): number;
    /**
     * Calculate shortest angular distance between two angles
     */
    static angularDistance(from: number, to: number): number;
}
