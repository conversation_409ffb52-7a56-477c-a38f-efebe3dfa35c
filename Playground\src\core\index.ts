// Core Framework - Enterprise-grade architecture
export { CoreFramework, initializeCoreFramework, shutdownCoreFramework, Core } from "./CoreFramework";

// Foundation - Base types and services
export { Result } from "./foundation/types/Result";
export { BrandedTypes } from "./foundation/types/BrandedTypes";
export type {
	PlayerId,
	EntityId,
	ServiceName,
	EventName,
	ComponentId,
	AssetId,
	ConfigKey,
	Error,
} from "./foundation/types/BrandedTypes";
export { createError } from "./foundation/types/BrandedTypes";
export { BaseService } from "./foundation/BaseService";
export { ServiceContainer } from "./foundation/ServiceContainer";
export { ServiceLifecycle } from "./foundation/enums/ServiceLifecycle";
export type { IService } from "./foundation/interfaces/IService";

// Configuration Management - Centralized configuration
export * from "./config";

// Error Handling - Centralized error management
export * from "./error-handling";

// Networking - Modern networking with validation
export { NetworkService } from "./networking/NetworkService";
export { NetworkValidationService } from "./networking/NetworkValidationService";
export { NetworkError } from "./networking/errors/NetworkError";

// State Management - Centralized state with middleware support
export { StateManager } from "./state/StateManager";
export { StateError } from "./state/errors/StateError";
export type { StateAction } from "./state/interfaces/StateAction";
export type { StateMiddleware } from "./state/interfaces/StateMiddleware";
export type { StateSubscription } from "./state/interfaces/StateSubscription";

// Design System - Centralized styling
export { COLORS, SIZES, TYPOGRAPHY, BORDER_RADIUS } from "./design";

// GUI Components - Modern React components
export * from "./gui/button";
export * from "./gui/grid/Grid";
export * from "./gui/input/Input";
export * from "./gui/modal/Modal";
export * from "./gui/list/ListView";
export * from "./gui/label/Label";
export * from "./gui/image/Image";
export * from "./gui/overlay/Overlay";
export * from "./gui/frame";
export * from "./gui/layout/AutoDockFrame";
export * from "./gui/layout/ZIndexManager";
export * from "./gui/layout/useZIndex";
export * from "./gui/layout/ResponsiveManager";
export * from "./gui/actionbar";
export * from "./gui/slider";
export * from "./gui/splash";
export * from "./gui/toast";
export * from "./gui/loading";
export * from "./gui/form";
export * from "./gui/showcase";
export * from "./gui/error-boundary";
export * from "./gui/progress";

// Effects System - Visual and audio effects
export * from "./effects/EffectPartBuilder";
export * from "./effects/EffectTweenBuilder";
export * from "./effects/FrameAnimationHelper";
export * from "./effects/ParticleHelper";
export * from "./effects/SoundHelper";
export * from "./effects/VisualEffectUtils";
export * from "./effects/TrailHelper";

// Animation System - Character and object animations
export * from "./animations/AnimationBuilder";
export * from "./animations/CharacterJointManager";
export * from "./animations/LimbAnimator";

// Utilities
export * from "./helper/PositionHelper";

// Character System
export * from "./character/CharacterBuilder";

// Data Persistence - Modern data management
export * from "./data/DataStoreHelper";
export * from "./data/PlayerDataManager";
export * from "./data/interfaces/DataStore";
export * from "./data/interfaces/RemoteEventTypes";
export * from "./data/interfaces/GlobalData";

// World Systems - Environmental interactions
export * from "./world";

// Entity Management - Game object management
export * from "./entities/EntityManager";
export * from "./entities/interfaces/Entity";
export * from "./entities/enums/EntityType";

// AI System - Behavior-based AI
export { AIController } from "./ai/AIController";
export type { AIBehavior } from "./ai/interfaces/AIBehavior";
export type { AIContext } from "./ai/interfaces/AIContext";
export type { AIBehaviorResult } from "./ai/interfaces/AIBehaviorResult";
export type { AIConfig } from "./ai/interfaces/AIConfig";
export { AIState } from "./ai/enums/AIState";
export * from "./ai/behaviors";

// Debug System - Development tools
export * from "./debug";

// Performance System - Monitoring and optimization
export { PerformanceMonitor, Performance } from "./performance/PerformanceMonitor";
export { ObjectPoolManager, ObjectPooling, PoolableBase } from "./performance/ObjectPoolManager";
export type { PoolableObject, PoolConfiguration, PoolStats } from "./performance/ObjectPoolManager";

// Input System - Comprehensive input management
export { InputManager, Input } from "./input/InputManager";
export { InputType, InputContext } from "./input/InputManager";
export type { InputBinding, InputConfiguration, InputState, InputStats } from "./input/InputManager";

// Inventory System - Item and inventory management
export * from "./inventory";

// Quest System - Mission and achievement management
export * from "./quests";

// Camera System - Advanced camera controls
export * from "./camera";

// Client-side Core Framework
export * from "./client";

// Game System - Game mode management and controllers
export * from "./game";
