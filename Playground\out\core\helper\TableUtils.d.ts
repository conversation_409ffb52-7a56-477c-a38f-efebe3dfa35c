/**
 * Table and data structure utilities for game development
 */
export declare class TableUtils {
    /**
     * Deep copy a table/object
     */
    static deepCopy<T>(obj: T): T;
    /**
     * Shallow copy a table/object
     */
    static shallowCopy<T>(obj: T): T;
    /**
     * Merge two or more objects
     */
    static merge<T>(...objects: Partial<T>[]): T;
    /**
     * Deep merge objects (recursive)
     */
    static deepMerge<T>(...objects: Partial<T>[]): T;
    /**
     * Get value from nested object using dot notation
     */
    static get(obj: Record<string, unknown>, path: string): unknown;
    /**
     * Set value in nested object using dot notation
     */
    static set(obj: Record<string, unknown>, path: string, value: unknown): void;
    /**
     * Check if object has a property (including nested)
     */
    static has(obj: Record<string, unknown>, path: string): boolean;
    /**
     * Remove property from object using dot notation
     */
    static remove(obj: Record<string, unknown>, path: string): boolean;
    /**
     * Get all keys from an object
     */
    static keys(obj: Record<string, unknown>): string[];
    /**
     * Get all values from an object
     */
    static values<T extends defined>(obj: Record<string, T>): T[];
    /**
     * Get all key-value pairs from an object
     */
    static entries<T>(obj: Record<string, T>): Array<[string, T]>;
    /**
     * Count number of properties in object
     */
    static size(obj: Record<string, unknown>): number;
    /**
     * Check if object is empty
     */
    static isEmpty(obj: Record<string, unknown>): boolean;
    /**
     * Filter object properties
     */
    static filter<T>(obj: Record<string, T>, predicate: (value: T, key: string) => boolean): Record<string, T>;
    /**
     * Map object values
     */
    static map<T, U>(obj: Record<string, T>, mapper: (value: T, key: string) => U): Record<string, U>;
    /**
     * Reduce object to a single value
     */
    static reduce<T, U>(obj: Record<string, T>, reducer: (accumulator: U, value: T, key: string) => U, initialValue: U): U;
    /**
     * Find first value that matches predicate
     */
    static find<T>(obj: Record<string, T>, predicate: (value: T, key: string) => boolean): T | undefined;
    /**
     * Find key of first value that matches predicate
     */
    static findKey<T>(obj: Record<string, T>, predicate: (value: T, key: string) => boolean): string | undefined;
    /**
     * Check if any value matches predicate
     */
    static some<T>(obj: Record<string, T>, predicate: (value: T, key: string) => boolean): boolean;
    /**
     * Check if all values match predicate
     */
    static every<T>(obj: Record<string, T>, predicate: (value: T, key: string) => boolean): boolean;
    /**
     * Group array of objects by a property
     */
    static groupBy<T extends defined>(array: T[], keySelector: (item: T) => string): Record<string, T[]>;
    /**
     * Create object from array of key-value pairs
     */
    static fromEntries<T>(entries: Array<[string, T]>): Record<string, T>;
    /**
     * Pick specific properties from object
     */
    static pick<T, K extends keyof T>(obj: T, keys: K[]): Pick<T, K>;
    /**
     * Omit specific properties from object
     */
    static omit<T, K extends keyof T>(obj: T, keys: K[]): Omit<T, K>;
    /**
     * Invert object (swap keys and values)
     */
    static invert(obj: Record<string, string | number>): Record<string, string>;
    /**
     * Flatten nested object with dot notation keys
     */
    static flatten(obj: Record<string, unknown>, prefix?: string): Record<string, unknown>;
    /**
     * Unflatten object with dot notation keys
     */
    static unflatten(obj: Record<string, unknown>): Record<string, unknown>;
    /**
     * Deep equal comparison
     */
    static deepEqual(a: unknown, b: unknown): boolean;
    /**
     * Serialize object to JSON-like string (for debugging)
     */
    static serialize(obj: unknown, indent?: number): string;
}
