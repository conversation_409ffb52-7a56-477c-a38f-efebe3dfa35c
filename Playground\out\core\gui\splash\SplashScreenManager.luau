-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local SplashScreenManager
do
	SplashScreenManager = setmetatable({}, {
		__tostring = function()
			return "SplashScreenManager"
		end,
	})
	SplashScreenManager.__index = SplashScreenManager
	function SplashScreenManager.new(...)
		local self = setmetatable({}, SplashScreenManager)
		return self:constructor(...) or self
	end
	function SplashScreenManager:constructor()
		self.loadingTasks = {}
		self.currentProgress = 0
		self.currentText = "Starting up..."
		self.isVisible = true
	end
	function SplashScreenManager:getInstance()
		if not self.instance then
			self.instance = SplashScreenManager.new()
		end
		return self.instance
	end
	function SplashScreenManager:addLoadingTask(task)
		local _loadingTasks = self.loadingTasks
		local _task = task
		table.insert(_loadingTasks, _task)
	end
	function SplashScreenManager:addLoadingTasks(tasks)
		for _, task in tasks do
			local _exp = self.loadingTasks
			table.insert(_exp, task)
		end
	end
	function SplashScreenManager:onStateChanged(callback)
		self.onStateChange = callback
	end
	SplashScreenManager.startLoading = TS.async(function(self)
		self.isVisible = true
		self.currentProgress = 0
		self.currentText = "Initializing Core Framework..."
		self:notifyStateChange()
		-- Ensure we have tasks to run
		if #self.loadingTasks == 0 then
			print("⚠️ No loading tasks found, setting up default tasks...")
			self:setupDefaultCoreTasks()
		end
		local _exp = self.loadingTasks
		-- ▼ ReadonlyArray.reduce ▼
		local _result = 0
		local _callback = function(sum, task)
			return sum + task.weight
		end
		for _i = 1, #_exp do
			_result = _callback(_result, _exp[_i], _i - 1, _exp)
		end
		-- ▲ ReadonlyArray.reduce ▲
		local totalWeight = _result
		if totalWeight == 0 then
			warn("⚠️ Total weight is 0, this may cause division by zero issues")
			self.currentProgress = 1
			self.currentText = "Loading Complete!"
			self:notifyStateChange()
			return nil
		end
		local completedWeight = 0
		local successfulTasks = 0
		local failedTasks = 0
		print(`🚀 Starting loading process with {#self.loadingTasks} tasks (total weight: {totalWeight})`)
		for i = 0, #self.loadingTasks - 1 do
			local task = self.loadingTasks[i + 1]
			self.currentText = task.name
			self:notifyStateChange()
			print(`📋 [{i + 1}/{#self.loadingTasks}] {task.name} (weight: {task.weight})`)
			TS.try(function()
				-- Execute the task with timeout protection
				local result = task.task()
				if result ~= nil then
					-- Add a reasonable timeout for async tasks
					local timeoutPromise = TS.Promise.new(function(_, reject)
						self:delay(10):andThen(function()
							return reject("Task timeout after 10 seconds")
						end)
					end)
					TS.await(TS.Promise.race({ result, timeoutPromise }))
				end
				-- Update progress
				completedWeight += task.weight
				self.currentProgress = math.min(completedWeight / totalWeight, 1)
				self:notifyStateChange()
				successfulTasks += 1
				print(`✅ [{i + 1}/{#self.loadingTasks}] {task.name} completed successfully`)
				-- Small delay for visual feedback
				TS.await(self:delay(0.1))
			end, function(error)
				failedTasks += 1
				warn(`❌ [{i + 1}/{#self.loadingTasks}] Loading task failed: {task.name} - {error}`)
				-- Show error message but continue loading
				self.currentText = `⚠️ {task.name} (failed - continuing...)`
				self:notifyStateChange()
				TS.await(self:delay(0.5))
				-- Still count partial progress for failed tasks to ensure we make progress
				completedWeight += task.weight * 0.5
				self.currentProgress = math.min(completedWeight / totalWeight, 1)
				self:notifyStateChange()
			end)
		end
		-- Ensure we reach 100% regardless of task results
		self.currentProgress = 1
		-- Show appropriate completion message
		if failedTasks == 0 then
			self.currentText = "🎉 Loading Complete!"
			print(`✅ All {successfulTasks} loading tasks completed successfully!`)
		else
			self.currentText = `⚠️ Loading Complete ({failedTasks} warnings)`
			print(`⚠️ Loading completed with {successfulTasks} successful and {failedTasks} failed tasks`)
		end
		self:notifyStateChange()
		-- Wait longer before hiding for better user experience and to show final status
		TS.await(self:delay(if failedTasks > 0 then 3.0 else 2.0))
		print("🎯 Splash screen loading process completed!")
	end)
	function SplashScreenManager:show()
		self.isVisible = true
		self.currentText = "Starting up..."
		self.currentProgress = 0
		self:notifyStateChange()
	end
	function SplashScreenManager:hide()
		self.isVisible = false
		self:notifyStateChange()
	end
	function SplashScreenManager:getState()
		return {
			isVisible = self.isVisible,
			loadingProgress = self.currentProgress,
			loadingText = self.currentText,
		}
	end
	function SplashScreenManager:setupDefaultCoreTasks()
		print("🔧 Setting up default core framework loading tasks...")
		self:addLoadingTasks({ {
			name = "Initializing Physics System...",
			weight = 1,
			task = TS.async(function()
				TS.try(function()
					-- Import and initialize physics
					local _binding = TS.await(TS.Promise.new(function(resolve)
						resolve(TS.import(script, game:GetService("ReplicatedStorage"), "core", "world", "physics", "PhysicsImpactHelper"))
					end))
					local PhysicsImpactHelper = _binding.PhysicsImpactHelper
					PhysicsImpactHelper:initialize()
					print("✅ Physics system initialized")
				end, function(error)
					warn(`⚠️ Physics system initialization failed: {error}`)
					-- Continue without physics - not critical for basic functionality
				end)
			end),
		}, {
			name = "Loading Weather System...",
			weight = 1,
			task = TS.async(function()
				TS.try(function()
					-- Import weather systems
					TS.await(TS.Promise.new(function(resolve)
						resolve(TS.import(script, game:GetService("ReplicatedStorage"), "core", "world", "state", "WeatherController"))
					end))
					TS.await(TS.Promise.new(function(resolve)
						resolve(TS.import(script, game:GetService("ReplicatedStorage"), "core", "effects", "WeatherParticleHelper"))
					end))
					TS.await(TS.Promise.new(function(resolve)
						resolve(TS.import(script, game:GetService("ReplicatedStorage"), "core", "effects", "WeatherSoundHelper"))
					end))
					print("✅ Weather system loaded")
				end, function(error)
					warn(`⚠️ Weather system loading failed: {error}`)
					-- Continue without weather - not critical for basic functionality
				end)
			end),
		}, {
			name = "Initializing UI Framework...",
			weight = 2,
			task = TS.async(function()
				TS.try(function()
					-- Import UI components
					TS.await(TS.Promise.new(function(resolve)
						resolve(TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "layout", "ZIndexManager"))
					end))
					local _binding = TS.await(TS.Promise.new(function(resolve)
						resolve(TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "layout", "ResponsiveManager"))
					end))
					local ResponsiveManager = _binding.ResponsiveManager
					ResponsiveManager:getInstance()
					print("✅ UI framework initialized")
				end, function(error)
					warn(`⚠️ UI framework initialization failed: {error}`)
					-- UI is more critical, but still continue
				end)
			end),
		}, {
			name = "Loading Sound System...",
			weight = 1,
			task = TS.async(function()
				TS.try(function()
					-- Import sound helpers
					TS.await(TS.Promise.new(function(resolve)
						resolve(TS.import(script, game:GetService("ReplicatedStorage"), "core", "effects", "SoundHelper"))
					end))
					print("✅ Sound system loaded")
				end, function(error)
					warn(`⚠️ Sound system loading failed: {error}`)
					-- Continue without sound - not critical for basic functionality
				end)
			end),
		}, {
			name = "Initializing Animation System...",
			weight = 1,
			task = TS.async(function()
				TS.try(function()
					-- Import animation systems
					TS.await(TS.Promise.new(function(resolve)
						resolve(TS.import(script, game:GetService("ReplicatedStorage"), "core", "animations", "AnimationBuilder"))
					end))
					TS.await(TS.Promise.new(function(resolve)
						resolve(TS.import(script, game:GetService("ReplicatedStorage"), "core", "animations", "LimbAnimator"))
					end))
					print("✅ Animation system initialized")
				end, function(error)
					warn(`⚠️ Animation system initialization failed: {error}`)
					-- Continue without animations - not critical for basic functionality
				end)
			end),
		}, {
			name = "Loading Entity Management...",
			weight = 1,
			task = TS.async(function()
				TS.try(function()
					-- Import entity systems
					TS.await(TS.Promise.new(function(resolve)
						resolve(TS.import(script, game:GetService("ReplicatedStorage"), "core", "entities", "EntityManager"))
					end))
					print("✅ Entity management loaded")
				end, function(error)
					warn(`⚠️ Entity management loading failed: {error}`)
					-- Continue without entity management - not critical for basic UI
				end)
			end),
		}, {
			name = "Validating Core Systems...",
			weight = 1,
			task = TS.async(function()
				TS.try(function()
					-- Perform validation checks
					local runService = game:GetService("RunService")
					local workspace = game:GetService("Workspace")
					local players = game:GetService("Players")
					-- Basic service validation
					if not runService or not workspace or not players then
						error("Critical Roblox services are not available")
					end
					-- Check if we're in a valid game environment
					if not players.LocalPlayer then
						error("LocalPlayer is not available")
					end
					print("✅ Core systems validation passed")
				end, function(error)
					warn(`⚠️ Core systems validation failed: {error}`)
					-- Continue anyway - we'll handle these issues as they arise
				end)
			end),
		}, {
			name = "Finalizing Core Framework...",
			weight = 1,
			task = TS.async(function()
				-- Final initialization and setup
				print("🎯 Core Framework loaded successfully!")
				print("📋 All systems ready for user interaction")
				-- Small additional delay to ensure everything is settled
				TS.await(self:delay(0.5))
			end),
		} })
		print(`📋 Set up {#self.loadingTasks} default loading tasks`)
	end
	function SplashScreenManager:notifyStateChange()
		if self.onStateChange then
			self.onStateChange(self:getState())
		end
	end
	function SplashScreenManager:delay(seconds)
		return TS.Promise.new(function(resolve)
			task.delay(seconds, resolve)
		end)
	end
end
return {
	SplashScreenManager = SplashScreenManager,
}
