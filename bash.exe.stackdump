Stack trace:
Frame         Function      Args
0007FFFFA160  00021005FE8E (000210285F68, 00021026AB6E, 0007FFFFA160, 0007FFFF9060) msys-2.0.dll+0x1FE8E
0007FFFFA160  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFA438) msys-2.0.dll+0x67F9
0007FFFFA160  000210046832 (000210286019, 0007FFFFA018, 0007FFFFA160, 000000000000) msys-2.0.dll+0x6832
0007FFFFA160  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFA160  000210068E24 (0007FFFFA170, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFA440  00021006A225 (0007FFFFA170, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFE6FBC0000 ntdll.dll
7FFE6E5C0000 KERNEL32.DLL
7FFE6CD60000 KERNELBASE.dll
7FFE6F620000 USER32.dll
7FFE6D5E0000 win32u.dll
7FFE6E1A0000 GDI32.dll
7FFE6D400000 gdi32full.dll
000210040000 msys-2.0.dll
7FFE6D530000 msvcp_win.dll
7FFE6D780000 ucrtbase.dll
7FFE6E380000 advapi32.dll
7FFE6FAD0000 msvcrt.dll
7FFE6F950000 sechost.dll
7FFE6EDC0000 RPCRT4.dll
7FFE6C380000 CRYPTBASE.DLL
7FFE6D150000 bcryptPrimitives.dll
7FFE6D990000 IMM32.DLL
