-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local TweenService = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "services").TweenService
local EffectTweenBuilder
do
	EffectTweenBuilder = setmetatable({}, {
		__tostring = function()
			return "EffectTweenBuilder"
		end,
	})
	EffectTweenBuilder.__index = EffectTweenBuilder
	function EffectTweenBuilder.new(...)
		local self = setmetatable({}, EffectTweenBuilder)
		return self:constructor(...) or self
	end
	function EffectTweenBuilder:constructor(instance)
		self.properties = {}
		self.delayTime = 0
		self.instance = instance
		self.tweenInfo = TweenInfo.new(0.5, Enum.EasingStyle.Quad, Enum.EasingDirection.Out)
	end
	EffectTweenBuilder["for"] = function(self, instance)
		return EffectTweenBuilder.new(instance)
	end
	function EffectTweenBuilder:expand(size)
		self.properties.Size = size
		return self
	end
	function EffectTweenBuilder:fade(trans)
		self.properties.Transparency = trans
		return self
	end
	function EffectTweenBuilder:move(pos)
		self.properties.Position = pos
		return self
	end
	function EffectTweenBuilder:rotate(angles)
		local _cFrame = (self.instance).CFrame
		local _angles = angles
		self.properties.CFrame = _cFrame * _angles
		return self
	end
	function EffectTweenBuilder:duration(time)
		self.tweenInfo = TweenInfo.new(time, self.tweenInfo.EasingStyle, self.tweenInfo.EasingDirection)
		return self
	end
	function EffectTweenBuilder:easing(style, direction)
		self.tweenInfo = TweenInfo.new(self.tweenInfo.Time, style, direction)
		return self
	end
	function EffectTweenBuilder:delay(delay)
		self.delayTime = delay
		return self
	end
	function EffectTweenBuilder:brightness(brightness)
		-- Only apply brightness to instances that support it (like Lighting)
		if self.instance.Brightness ~= nil then
			self.properties.Brightness = brightness
		end
		return self
	end
	function EffectTweenBuilder:onComplete(callback)
		self.completionCallback = callback
		return self
	end
	function EffectTweenBuilder:play()
		local tween = TweenService:Create(self.instance, self.tweenInfo, self.properties)
		if self.delayTime > 0 then
			task.delay(self.delayTime, function()
				return tween:Play()
			end)
		else
			tween:Play()
		end
		if self.completionCallback then
			tween.Completed:Connect(self.completionCallback)
		end
		return tween
	end
end
return {
	EffectTweenBuilder = EffectTweenBuilder,
}
