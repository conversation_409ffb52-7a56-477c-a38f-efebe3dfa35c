import { UserInputService, ContextActionService, Players, RunService } from "@rbxts/services";
import { BaseService } from "../foundation/BaseService";
import { Result } from "../foundation/types/Result";
import { createError, Error } from "../foundation/types/BrandedTypes";

export enum InputType {
	KeyPress = "KeyPress",
	KeyRelease = "KeyRelease",
	KeyHold = "KeyHold",
	MouseClick = "MouseClick",
	MouseMove = "MouseMove",
	TouchStart = "TouchStart",
	TouchEnd = "TouchEnd",
	GamepadInput = "GamepadInput",
}

export enum InputContext {
	Global = "Global",
	UI = "UI",
	Movement = "Movement",
	Combat = "Combat",
	Camera = "Camera",
	Custom = "Custom",
}

interface InputBinding {
	id: string;
	context: InputContext;
	keys: Enum.KeyCode[];
	gamepadButtons?: Enum.KeyCode[];
	touchGestures?: string[];
	description: string;
	callback: (actionName: string, state: Enum.UserInputState, input: InputObject) => void;
	priority?: number;
	enabled: boolean;
}

interface InputConfiguration {
	enableMouse: boolean;
	enableKeyboard: boolean;
	enableTouch: boolean;
	enableGamepad: boolean;
	mouseSensitivity: number;
	gamepadDeadzone: number;
	touchSensitivity: number;
	holdThreshold: number; // Time in seconds to register as hold
}

interface InputState {
	keysPressed: Set<Enum.KeyCode>;
	mousePosition: Vector2;
	mouseDelta: Vector2;
	gamepadSticks: Record<string, Vector3>;
	touchPositions: Record<number, Vector2>;
	isInputEnabled: boolean;
}

interface InputStats {
	totalInputs: number;
	keyInputs: number;
	mouseInputs: number;
	touchInputs: number;
	gamepadInputs: number;
	averageInputsPerSecond: number;
	lastInputTime: number;
}

/**
 * Comprehensive input management system supporting keyboard, mouse, touch, and gamepad
 * Provides customizable input bindings, contexts, and input state tracking
 */
export class InputManager extends BaseService {
	private static instance: InputManager;
	
	private bindings: Map<string, InputBinding> = new Map();
	private contextStacks: Map<InputContext, string[]> = new Map();
	private activeContext: InputContext = InputContext.Global;
	
	private config: InputConfiguration = {
		enableMouse: true,
		enableKeyboard: true,
		enableTouch: true,
		enableGamepad: true,
		mouseSensitivity: 1.0,
		gamepadDeadzone: 0.1,
		touchSensitivity: 1.0,
		holdThreshold: 0.5,
	};
	
	private inputState: InputState = {
		keysPressed: new Set(),
		mousePosition: new Vector2(0, 0),
		mouseDelta: new Vector2(0, 0),
		gamepadSticks: {},
		touchPositions: {},
		isInputEnabled: true,
	};
	
	private stats: InputStats = {
		totalInputs: 0,
		keyInputs: 0,
		mouseInputs: 0,
		touchInputs: 0,
		gamepadInputs: 0,
		averageInputsPerSecond: 0,
		lastInputTime: 0,
	};
	
	private holdTimers: Map<Enum.KeyCode, number> = new Map();
	private inputConnections: RBXScriptConnection[] = [];
	private lastStatsUpdate = 0;

	constructor() {
		super("InputManager");
		
		// Initialize context stacks
		this.contextStacks.set(InputContext.Global, []);
		this.contextStacks.set(InputContext.UI, []);
		this.contextStacks.set(InputContext.Movement, []);
		this.contextStacks.set(InputContext.Combat, []);
		this.contextStacks.set(InputContext.Camera, []);
		this.contextStacks.set(InputContext.Custom, []);
	}

	public static getInstance(): InputManager {
		if (!InputManager.instance) {
			InputManager.instance = new InputManager();
		}
		return InputManager.instance;
	}

	protected async onInitialize(): Promise<Result<void, Error>> {
		try {
			this.setupInputConnections();
			this.setupDefaultBindings();
			print("🎮 Input Manager initialized");
			return Result.ok(undefined);
		} catch (error) {
			return Result.err(createError(`Failed to initialize InputManager: ${error}`));
		}
	}

	protected async onShutdown(): Promise<Result<void, Error>> {
		this.cleanup();
		print("🎮 Input Manager shutdown");
		return Result.ok(undefined);
	}

	/**
	 * Register an input binding
	 */
	public registerBinding(binding: Omit<InputBinding, "enabled">): Result<void, Error> {
		if (this.bindings.has(binding.id)) {
			return Result.err(createError(`Input binding '${binding.id}' already exists`));
		}

		const fullBinding: InputBinding = {
			...binding,
			enabled: true,
			priority: binding.priority || 0,
		};

		this.bindings.set(binding.id, fullBinding);
		
		// Register with ContextActionService for mobile support
		if (binding.keys.size() > 0 || binding.gamepadButtons || binding.touchGestures) {
			this.registerContextAction(fullBinding);
		}

		return Result.ok(undefined);
	}

	/**
	 * Unregister an input binding
	 */
	public unregisterBinding(id: string): boolean {
		const binding = this.bindings.get(id);
		if (!binding) return false;

		this.bindings.delete(id);
		ContextActionService.UnbindAction(id);
		
		return true;
	}

	/**
	 * Enable or disable a binding
	 */
	public setBindingEnabled(id: string, enabled: boolean): boolean {
		const binding = this.bindings.get(id);
		if (!binding) return false;

		binding.enabled = enabled;
		
		if (enabled) {
			this.registerContextAction(binding);
		} else {
			ContextActionService.UnbindAction(id);
		}
		
		return true;
	}

	/**
	 * Set the active input context
	 */
	public setContext(context: InputContext): void {
		if (this.activeContext !== context) {
			this.activeContext = context;
			this.updateContextBindings();
		}
	}

	/**
	 * Push a context onto the stack (for temporary context changes)
	 */
	public pushContext(context: InputContext, id: string): void {
		const stack = this.contextStacks.get(context);
		if (stack && !stack.includes(id)) {
			stack.push(id);
			this.setContext(context);
		}
	}

	/**
	 * Pop a context from the stack
	 */
	public popContext(context: InputContext, id: string): void {
		const stack = this.contextStacks.get(context);
		if (stack) {
			const index = stack.indexOf(id);
			if (index >= 0) {
				stack.remove(index);
				
				// Restore previous context if stack is empty
				if (stack.size() === 0) {
					this.setContext(InputContext.Global);
				}
			}
		}
	}

	/**
	 * Get current input state
	 */
	public getInputState(): InputState {
		return { ...this.inputState };
	}

	/**
	 * Check if a key is currently pressed
	 */
	public isKeyPressed(key: Enum.KeyCode): boolean {
		return this.inputState.keysPressed.has(key);
	}

	/**
	 * Check if any of the keys are pressed
	 */
	public isAnyKeyPressed(keys: Enum.KeyCode[]): boolean {
		return keys.some(key => this.inputState.keysPressed.has(key));
	}

	/**
	 * Get mouse position
	 */
	public getMousePosition(): Vector2 {
		return this.inputState.mousePosition;
	}

	/**
	 * Get mouse delta (movement since last frame)
	 */
	public getMouseDelta(): Vector2 {
		return this.inputState.mouseDelta;
	}

	/**
	 * Get gamepad stick position
	 */
	public getGamepadStick(stick: "LeftStick" | "RightStick"): Vector3 {
		return this.inputState.gamepadSticks[stick] || new Vector3(0, 0, 0);
	}

	/**
	 * Enable or disable all input processing
	 */
	public setInputEnabled(enabled: boolean): void {
		this.inputState.isInputEnabled = enabled;
		
		if (enabled) {
			this.setupInputConnections();
		} else {
			this.cleanup();
		}
	}

	/**
	 * Update input configuration
	 */
	public updateConfig(newConfig: Partial<InputConfiguration>): void {
		this.config = { ...this.config, ...newConfig };
	}

	/**
	 * Get input statistics
	 */
	public getStats(): InputStats {
		return { ...this.stats };
	}

	/**
	 * Reset input statistics
	 */
	public resetStats(): void {
		this.stats = {
			totalInputs: 0,
			keyInputs: 0,
			mouseInputs: 0,
			touchInputs: 0,
			gamepadInputs: 0,
			averageInputsPerSecond: 0,
			lastInputTime: 0,
		};
	}

	private setupInputConnections(): void {
		if (!RunService.IsClient()) return;

		this.cleanup(); // Clean up existing connections

		// Keyboard and mouse input
		if (this.config.enableKeyboard || this.config.enableMouse) {
			const connection1 = UserInputService.InputBegan.Connect((input, gameProcessed) => {
				if (gameProcessed || !this.inputState.isInputEnabled) return;
				this.handleInputBegan(input);
			});
			
			const connection2 = UserInputService.InputEnded.Connect((input, gameProcessed) => {
				if (gameProcessed || !this.inputState.isInputEnabled) return;
				this.handleInputEnded(input);
			});
			
			const connection3 = UserInputService.InputChanged.Connect((input, gameProcessed) => {
				if (gameProcessed || !this.inputState.isInputEnabled) return;
				this.handleInputChanged(input);
			});

			this.inputConnections.push(connection1, connection2, connection3);
		}

		// Touch input
		if (this.config.enableTouch) {
			const touchConnection1 = UserInputService.TouchStarted.Connect((touch, gameProcessed) => {
				if (gameProcessed || !this.inputState.isInputEnabled) return;
				this.handleTouchStarted(touch);
			});
			
			const touchConnection2 = UserInputService.TouchEnded.Connect((touch, gameProcessed) => {
				if (gameProcessed || !this.inputState.isInputEnabled) return;
				this.handleTouchEnded(touch);
			});

			this.inputConnections.push(touchConnection1, touchConnection2);
		}

		// Hold detection
		const holdConnection = RunService.Heartbeat.Connect(() => {
			this.updateHoldDetection();
			this.updateStats();
		});
		
		this.inputConnections.push(holdConnection);
	}

	private cleanup(): void {
		this.inputConnections.forEach(connection => connection.Disconnect());
		this.inputConnections = [];
		
		// Unbind all context actions
		this.bindings.forEach((_, id) => {
			ContextActionService.UnbindAction(id);
		});
	}

	private handleInputBegan(input: InputObject): void {
		this.updateInputStats("key");

		if (input.UserInputType === Enum.UserInputType.Keyboard) {
			this.inputState.keysPressed.add(input.KeyCode);
			this.holdTimers.set(input.KeyCode, tick());
		} else if (input.UserInputType === Enum.UserInputType.MouseButton1 || 
		          input.UserInputType === Enum.UserInputType.MouseButton2) {
			this.updateInputStats("mouse");
		}
	}

	private handleInputEnded(input: InputObject): void {
		if (input.UserInputType === Enum.UserInputType.Keyboard) {
			this.inputState.keysPressed.delete(input.KeyCode);
			this.holdTimers.delete(input.KeyCode);
		}
	}

	private handleInputChanged(input: InputObject): void {
		if (input.UserInputType === Enum.UserInputType.MouseMovement) {
			const newPosition = new Vector2(input.Position.X, input.Position.Y);
			this.inputState.mouseDelta = newPosition.sub(this.inputState.mousePosition);
			this.inputState.mousePosition = newPosition;
			this.updateInputStats("mouse");
		} else if (input.UserInputType === Enum.UserInputType.Gamepad1) {
			this.updateGamepadState(input);
			this.updateInputStats("gamepad");
		}
	}

	private handleTouchStarted(touch: InputObject): void {
		this.inputState.touchPositions[touch.KeyCode.Value] = new Vector2(touch.Position.X, touch.Position.Y);
		this.updateInputStats("touch");
	}

	private handleTouchEnded(touch: InputObject): void {
		delete this.inputState.touchPositions[touch.KeyCode.Value];
	}

	private updateGamepadState(input: InputObject): void {
		if (input.KeyCode === Enum.KeyCode.Thumbstick1) {
			const position = input.Position;
			if (position.Magnitude > this.config.gamepadDeadzone) {
				this.inputState.gamepadSticks["LeftStick"] = position;
			} else {
				this.inputState.gamepadSticks["LeftStick"] = new Vector3(0, 0, 0);
			}
		} else if (input.KeyCode === Enum.KeyCode.Thumbstick2) {
			const position = input.Position;
			if (position.Magnitude > this.config.gamepadDeadzone) {
				this.inputState.gamepadSticks["RightStick"] = position;
			} else {
				this.inputState.gamepadSticks["RightStick"] = new Vector3(0, 0, 0);
			}
		}
	}

	private updateHoldDetection(): void {
		const currentTime = tick();
		
		this.holdTimers.forEach((startTime, key) => {
			if (currentTime - startTime >= this.config.holdThreshold) {
				// Trigger hold events for bindings that care about holds
				this.triggerHoldBindings(key);
			}
		});
	}

	private triggerHoldBindings(key: Enum.KeyCode): void {
		this.bindings.forEach(binding => {
			if (binding.enabled && binding.keys.includes(key)) {
				// Create a synthetic input object for hold events
				const syntheticInput = {
					KeyCode: key,
					UserInputType: Enum.UserInputType.Keyboard,
				} as InputObject;
				
				binding.callback(binding.id, Enum.UserInputState.Change, syntheticInput);
			}
		});
	}

	private updateInputStats(inputType: "key" | "mouse" | "touch" | "gamepad"): void {
		this.stats.totalInputs++;
		this.stats.lastInputTime = tick();
		
		if (inputType === "key") {
			this.stats.keyInputs++;
		} else if (inputType === "mouse") {
			this.stats.mouseInputs++;
		} else if (inputType === "touch") {
			this.stats.touchInputs++;
		} else if (inputType === "gamepad") {
			this.stats.gamepadInputs++;
		}
	}

	private updateStats(): void {
		const currentTime = tick();
		if (currentTime - this.lastStatsUpdate >= 1) { // Update every second
			this.stats.averageInputsPerSecond = this.stats.totalInputs / (currentTime - this.lastStatsUpdate);
			this.lastStatsUpdate = currentTime;
		}
	}

	private registerContextAction(binding: InputBinding): void {
		const keys = binding.keys.map(key => key.Name).join(", ");
		
		ContextActionService.BindAction(
			binding.id,
			(actionName, state, input) => {
				if (binding.enabled && this.shouldProcessBinding(binding)) {
					binding.callback(actionName, state, input);
				}
			},
			false, // Create touch button
			...binding.keys
		);
	}

	private shouldProcessBinding(binding: InputBinding): boolean {
		return binding.context === this.activeContext || binding.context === InputContext.Global;
	}

	private updateContextBindings(): void {
		// Re-register all bindings to respect new context
		this.bindings.forEach(binding => {
			ContextActionService.UnbindAction(binding.id);
			if (binding.enabled && this.shouldProcessBinding(binding)) {
				this.registerContextAction(binding);
			}
		});
	}

	private setupDefaultBindings(): void {
		// Example default bindings - can be customized per game
		this.registerBinding({
			id: "movement_forward",
			context: InputContext.Movement,
			keys: [Enum.KeyCode.W, Enum.KeyCode.Up],
			description: "Move forward",
			callback: () => {
				// Default movement will be handled by movement system
			},
		});

		this.registerBinding({
			id: "movement_backward",
			context: InputContext.Movement,
			keys: [Enum.KeyCode.S, Enum.KeyCode.Down],
			description: "Move backward",
			callback: () => {
				// Default movement will be handled by movement system
			},
		});

		this.registerBinding({
			id: "movement_left",
			context: InputContext.Movement,
			keys: [Enum.KeyCode.A, Enum.KeyCode.Left],
			description: "Move left",
			callback: () => {
				// Default movement will be handled by movement system
			},
		});

		this.registerBinding({
			id: "movement_right",
			context: InputContext.Movement,
			keys: [Enum.KeyCode.D, Enum.KeyCode.Right],
			description: "Move right",
			callback: () => {
				// Default movement will be handled by movement system
			},
		});

		this.registerBinding({
			id: "movement_jump",
			context: InputContext.Movement,
			keys: [Enum.KeyCode.Space],
			description: "Jump",
			callback: () => {
				// Default jump will be handled by movement system
			},
		});
	}

	private getTouchPointCount(touchPositions: Record<number, Vector2>): number {
		// Simple count by iterating
		let count = 0;
		const keys = touchPositions as Record<string, Vector2>;
		if (keys[0] !== undefined) count++;
		if (keys[1] !== undefined) count++;
		if (keys[2] !== undefined) count++;
		if (keys[3] !== undefined) count++;
		if (keys[4] !== undefined) count++;
		// Support up to 5 touch points (typical for mobile)
		return count;
	}

	/**
	 * Get a comprehensive input report
	 */
	public getReport(): string {
		const stats = this.getStats();
		const state = this.getInputState();
		
		let report = "🎮 Input Manager Report\n";
		report += `Active Context: ${this.activeContext}\n`;
		report += `Input Enabled: ${state.isInputEnabled}\n`;
		report += `Total Bindings: ${this.bindings.size()}\n\n`;
		
		report += "📊 Input Statistics:\n";
		report += `Total Inputs: ${stats.totalInputs}\n`;
		report += `Key: ${stats.keyInputs} | Mouse: ${stats.mouseInputs} | Touch: ${stats.touchInputs} | Gamepad: ${stats.gamepadInputs}\n`;
		report += `Average IPS: ${string.format("%.1f", stats.averageInputsPerSecond)}\n\n`;
		
		report += "🎯 Current State:\n";
		report += `Keys Pressed: ${state.keysPressed.size()}\n`;
		report += `Mouse Position: (${string.format("%.0f", state.mousePosition.X)}, ${string.format("%.0f", state.mousePosition.Y)})\n`;
		report += `Touch Points: ${this.getTouchPointCount(state.touchPositions)}\n`;
		
		return report;
	}
}

// Global input management functions for easy access
export const Input = {
	getInstance: () => InputManager.getInstance(),
	registerBinding: (binding: Omit<InputBinding, "enabled">) => 
		InputManager.getInstance().registerBinding(binding),
	setContext: (context: InputContext) => InputManager.getInstance().setContext(context),
	isKeyPressed: (key: Enum.KeyCode) => InputManager.getInstance().isKeyPressed(key),
	getMousePosition: () => InputManager.getInstance().getMousePosition(),
	getReport: () => InputManager.getInstance().getReport(),
};

// Export types for external use
export type { InputBinding, InputConfiguration, InputState, InputStats };