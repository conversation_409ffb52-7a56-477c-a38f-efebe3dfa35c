import { Workspace, TweenService, Debris } from "@rbxts/services";
import { DestructionOptions, DestructibleZone, PartState, DestructionEffect } from "./interfaces/DestructionOptions";

export class DestructibleManager {
	private static activeZones: Map<string, DestructibleZone> = new Map();
	private static zoneCounter: number = 0;

	/**
	 * Create a destructible zone that affects the environment
	 * Perfect for Whitebeard earthquake effects, explosions, etc.
	 */
	public static createDestructibleZone(options: DestructionOptions): string {
		this.zoneCounter = this.zoneCounter + 1;
		const zoneId = `destruction_${this.zoneCounter}_${tick()}`;

		// Find all parts in the destruction radius
		const affectedParts = this.findPartsInRadius(options.center, options.radius, options.excludeAnchored);

		// Store original states for potential restoration
		const originalStates = new Map<Part, PartState>();
		for (const part of affectedParts) {
			originalStates.set(part, this.capturePartState(part));
		}

		// Create the destructible zone
		const zone: DestructibleZone = {
			id: zoneId,
			center: options.center,
			radius: options.radius,
			createdAt: tick(),
			expiresAt: options.duration ? tick() + options.duration : undefined,
			destructionType: options.destructionType,
			affectedParts: affectedParts,
			originalStates: originalStates,
		};

		this.activeZones.set(zoneId, zone);

		// Apply destruction effects
		this.applyDestruction(zone, options);

		// Set up cleanup if temporary
		if (options.duration) {
			task.delay(options.duration, () => {
				this.restoreZone(zoneId);
			});
		}

		print(`🌍 Created destructible zone: ${zoneId} affecting ${affectedParts.size()} parts`);
		return zoneId;
	}

	/**
	 * Apply destruction effects to parts in the zone
	 */
	private static applyDestruction(zone: DestructibleZone, options: DestructionOptions): void {
		for (const part of zone.affectedParts) {
			const distanceFromCenter = zone.center.sub(part.Position).Magnitude;
			const distanceRatio = distanceFromCenter / zone.radius;
			const effectIntensity = options.intensity * (1 - distanceRatio); // Closer = more intense

			this.applyDestructionToPart(part, options.destructionType, effectIntensity, options);
		}

		// Create visual effects
		if (options.effects) {
			for (const effect of options.effects) {
				this.createVisualEffect(zone.center, effect);
			}
		}
	}

	/**
	 * Apply specific destruction effects to a single part
	 */
	private static applyDestructionToPart(
		part: Part,
		destructionType: string,
		intensity: number,
		options: DestructionOptions,
	): void {
		switch (destructionType) {
			case "earthquake":
				this.applyEarthquakeEffect(part, intensity);
				break;
			case "explosion":
				this.applyExplosionEffect(part, intensity, options.center);
				break;
			case "slash":
				this.applySlashEffect(part, intensity);
				break;
			case "impact":
				this.applyImpactEffect(part, intensity, options.center);
				break;
			case "freeze":
				this.applyFreezeEffect(part, intensity);
				break;
			case "burn":
				this.applyBurnEffect(part, intensity);
				break;
		}

		// Create debris if specified
		if (options.createDebris && intensity > 0.5) {
			this.createDebris(part, intensity);
		}
	}

	/**
	 * Apply earthquake-style destruction (perfect for Whitebeard)
	 */
	private static applyEarthquakeEffect(part: Part, intensity: number): void {
		// Create cracks effect
		this.createCracksOnPart(part, intensity);

		// Shake the part
		const originalPosition = part.Position;
		const shakeIntensity = intensity * 5;

		const shakeTween = TweenService.Create(
			part,
			new TweenInfo(0.1, Enum.EasingStyle.Bounce, Enum.EasingDirection.InOut, 5, true),
			{
				Position: originalPosition.add(
					new Vector3(
						(math.random() - 0.5) * shakeIntensity,
						(math.random() - 0.5) * shakeIntensity,
						(math.random() - 0.5) * shakeIntensity,
					),
				),
			},
		);

		shakeTween.Play();

		// Return to original position after shaking
		shakeTween.Completed.Connect(() => {
			const returnTween = TweenService.Create(
				part,
				new TweenInfo(0.5, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
				{ Position: originalPosition },
			);
			returnTween.Play();
		});

		// Damage the part based on intensity
		if (intensity > 0.7) {
			part.Transparency = math.min(part.Transparency + intensity * 0.3, 0.8);
			part.Material = Enum.Material.CrackedLava; // Looks damaged
		}
	}

	/**
	 * Apply explosion-style destruction
	 */
	private static applyExplosionEffect(part: Part, intensity: number, center: Vector3): void {
		// Calculate force direction
		const forceDirection = part.Position.sub(center).Unit;
		const force = forceDirection.mul(intensity * 50);

		// Apply force if part is not anchored
		if (!part.Anchored) {
			const bodyVelocity = new Instance("BodyVelocity");
			bodyVelocity.MaxForce = new Vector3(4000, 4000, 4000);
			bodyVelocity.Velocity = force;
			bodyVelocity.Parent = part;

			// Remove force after short time
			task.delay(0.5, () => {
				if (bodyVelocity.Parent) {
					bodyVelocity.Destroy();
				}
			});
		}

		// Visual damage
		if (intensity > 0.5) {
			part.Material = Enum.Material.CorrodedMetal;
			part.Color = part.Color.Lerp(new Color3(0.2, 0.2, 0.2), intensity * 0.5);
		}
	}

	/**
	 * Apply freeze effect (for Ice Age ability)
	 */
	private static applyFreezeEffect(part: Part, intensity: number): void {
		// Change material to ice
		part.Material = Enum.Material.Ice;
		part.Color = part.Color.Lerp(new Color3(0.7, 0.9, 1), intensity);
		part.Transparency = math.min(part.Transparency + intensity * 0.2, 0.5);

		// Add frost particles
		this.createFrostEffect(part);
	}

	/**
	 * Create visual cracks on a part
	 */
	private static createCracksOnPart(part: Part, intensity: number): void {
		const crackDecal = new Instance("Decal");
		crackDecal.Texture = "rbxasset://textures/face.png"; // Replace with crack texture
		crackDecal.Face = Enum.NormalId.Top;
		crackDecal.Transparency = 1 - intensity;
		crackDecal.Parent = part;

		// Fade out cracks over time
		const fadeTween = TweenService.Create(crackDecal, new TweenInfo(10, Enum.EasingStyle.Linear), {
			Transparency: 1,
		});
		fadeTween.Play();
		fadeTween.Completed.Connect(() => crackDecal.Destroy());
	}

	/**
	 * Create debris from destroyed parts
	 */
	private static createDebris(originalPart: Part, intensity: number): void {
		const debrisCount = math.floor(intensity * 5) + 1;

		for (let i = 0; i < debrisCount; i++) {
			const debris = new Instance("Part");
			debris.Size = originalPart.Size.div(math.random() * 3 + 2);
			debris.Material = originalPart.Material;
			debris.Color = originalPart.Color;
			debris.Position = originalPart.Position.add(
				new Vector3((math.random() - 0.5) * 10, math.random() * 5, (math.random() - 0.5) * 10),
			);
			debris.Rotation = new Vector3(math.random() * 360, math.random() * 360, math.random() * 360);
			debris.Parent = Workspace;

			// Add random velocity
			const bodyVelocity = new Instance("BodyVelocity");
			bodyVelocity.MaxForce = new Vector3(4000, 4000, 4000);
			bodyVelocity.Velocity = new Vector3(
				(math.random() - 0.5) * 20,
				math.random() * 15 + 5,
				(math.random() - 0.5) * 20,
			);
			bodyVelocity.Parent = debris;

			// Clean up debris after some time
			Debris.AddItem(debris, 30);
			task.delay(1, () => {
				if (bodyVelocity.Parent) {
					bodyVelocity.Destroy();
				}
			});
		}
	}

	/**
	 * Create frost visual effect
	 */
	private static createFrostEffect(part: Part): void {
		// Implementation would create particle effects for frost
		// This is a placeholder for the actual particle system
		print(`❄️ Creating frost effect on ${part.Name}`);
	}

	/**
	 * Create visual effects at a position
	 */
	private static createVisualEffect(position: Vector3, effect: DestructionEffect): void {
		// Implementation would create particle effects, explosions, etc.
		print(`✨ Creating ${effect.type} effect at ${position}`);
	}

	/**
	 * Find all parts within a radius
	 */
	private static findPartsInRadius(center: Vector3, radius: number, excludeAnchored?: boolean): Part[] {
		const parts: Part[] = [];

		// Use GetPartBoundsInBox for efficient spatial queries
		const region = new Region3(
			center.sub(new Vector3(radius, radius, radius)),
			center.add(new Vector3(radius, radius, radius)),
		);

		// Get parts in the region using spatial query
		const partsInRegion = Workspace.GetPartBoundsInBox(
			new CFrame(center),
			new Vector3(radius * 2, radius * 2, radius * 2),
		);

		for (const part of partsInRegion) {
			if (part.IsA("Part")) {
				const distance = center.sub(part.Position).Magnitude;
				if (distance <= radius) {
					if (!excludeAnchored || !part.Anchored) {
						parts.push(part);
					}
				}
			}
		}

		return parts;
	}

	/**
	 * Capture the current state of a part
	 */
	private static capturePartState(part: Part): PartState {
		return {
			position: part.Position,
			rotation: part.Rotation,
			size: part.Size,
			transparency: part.Transparency,
			material: part.Material,
			color: part.Color,
			anchored: part.Anchored,
		};
	}

	/**
	 * Restore a destructible zone to its original state
	 */
	public static restoreZone(zoneId: string): void {
		const zone = this.activeZones.get(zoneId);
		if (!zone) return;

		for (const [part, originalState] of zone.originalStates) {
			if (part.Parent) {
				// Restore original properties
				part.Position = originalState.position;
				part.Rotation = originalState.rotation;
				part.Size = originalState.size;
				part.Transparency = originalState.transparency;
				part.Material = originalState.material;
				part.Color = originalState.color;
				part.Anchored = originalState.anchored;
			}
		}

		this.activeZones.delete(zoneId);
		print(`🔄 Restored destructible zone: ${zoneId}`);
	}

	/**
	 * Get all active destructible zones
	 */
	public static getActiveZones(): Map<string, DestructibleZone> {
		return this.activeZones;
	}

	/**
	 * Clean up expired zones
	 */
	public static cleanupExpiredZones(): void {
		const currentTime = tick();
		for (const [zoneId, zone] of this.activeZones) {
			if (zone.expiresAt && currentTime >= zone.expiresAt) {
				this.restoreZone(zoneId);
			}
		}
	}

	// Additional methods would be added for other destruction types
	private static applySlashEffect(part: Part, intensity: number): void {
		// Implementation for slash effects (Zoro abilities)
	}

	private static applyImpactEffect(part: Part, intensity: number, center: Vector3): void {
		// Implementation for impact effects (punch abilities)
	}

	private static applyBurnEffect(part: Part, intensity: number): void {
		// Implementation for burn effects (Ace abilities)
	}
}
