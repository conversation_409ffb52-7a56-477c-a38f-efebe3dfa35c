-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local React = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "react")
local _core = TS.import(script, game:GetService("ReplicatedStorage"), "core")
local Modal = _core.Modal
local Button = _core.Button
local Label = _core.Label
local VerticalFrame = _core.VerticalFrame
local COLORS = _core.COLORS
local ToastService = _core.ToastService
local ProgressBar = _core.ProgressBar
local ElementalBattleGame = TS.import(script, game:GetService("ReplicatedStorage"), "core", "game", "ElementalBattleGame").ElementalBattleGame
local function ElementalBattleUI(_param)
	local isOpen = _param.isOpen
	local onClose = _param.onClose
	local battleGame = React.useState(function()
		return ElementalBattleGame.new()
	end)
	local gameState, setGameState = React.useState(function()
		return battleGame:getGameState()
	end)
	-- Poll for state changes
	React.useEffect(function()
		local heartbeat = game:GetService("RunService").Heartbeat:Connect(function()
			setGameState(battleGame:getGameState())
		end)
		return function()
			return heartbeat:Disconnect()
		end
	end, { battleGame })
	local handleStartGame = React.useCallback(TS.async(function()
		TS.try(function()
			print("⚔️ [ElementalBattleUI] Starting Elemental Battle...")
			local result = TS.await(battleGame:start())
			if result:isOk() then
				ToastService.showSuccess("⚔️ Battle Started!", "Prepare for elemental combat!")
			else
				local errorResult = result:getError()
				ToastService.showError("❌ Start Failed", errorResult.message)
			end
		end, function(err)
			ToastService.showError("❌ Error", `Failed to start game: {err}`)
		end)
	end), { battleGame })
	local handleStopGame = React.useCallback(TS.async(function()
		TS.try(function()
			print("⚔️ [ElementalBattleUI] Stopping Elemental Battle...")
			local result = TS.await(battleGame:stop())
			if result:isOk() then
				ToastService.showSuccess("⚔️ Battle Ended", "Thanks for playing!")
			else
				local errorResult = result:getError()
				ToastService.showError("❌ Stop Failed", errorResult.message)
			end
		end, function(err)
			ToastService.showError("❌ Error", `Failed to stop game: {err}`)
		end)
	end), { battleGame })
	local handleClose = React.useCallback(function()
		if gameState.isGameActive then
			ToastService.showWarning("⚠️ Game Active", "Stop the game before closing the panel")
			return nil
		end
		onClose()
	end, { gameState.isGameActive, onClose })
	-- Calculate health percentage for progress bar
	local healthPercentage = if gameState.maxPlayerHealth > 0 then (gameState.playerHealth / gameState.maxPlayerHealth) * 100 else 0
	local experiencePercentage = if gameState.experienceToNextLevel > 0 then (gameState.experience / gameState.experienceToNextLevel) * 100 else 0
	-- Get phase display text
	local getPhaseDisplayText = function()
		local _exp = gameState.gamePhase
		repeat
			if _exp == "waiting" then
				return "Ready to Start"
			end
			if _exp == "playing" then
				return `Wave {gameState.currentWave} - {gameState.enemiesRemaining} enemies left`
			end
			if _exp == "betweenWaves" then
				return `Next wave in {math.ceil(gameState.timeUntilNextWave)}s`
			end
			if _exp == "ended" then
				return "Game Over"
			end
			return "Unknown"
		until true
	end
	local _exp = React.createElement(VerticalFrame, {
		spacing = 8,
	}, React.createElement(Label, {
		text = "🎮 Game Status",
		fontSize = 18,
		bold = true,
	}), React.createElement(Label, {
		text = `Status: {getPhaseDisplayText()}`,
		fontSize = 14,
		textColor = if gameState.isGameActive then COLORS.success else COLORS.text.secondary,
	}), React.createElement(Label, {
		text = `Score: {gameState.score} | Level: {gameState.playerLevel}`,
		fontSize = 14,
		textColor = COLORS.primary,
	}))
	local _exp_1 = gameState.isGameActive and (React.createElement(VerticalFrame, {
		spacing = 8,
	}, React.createElement(Label, {
		text = "⚡ Player Stats",
		fontSize = 16,
		bold = true,
	}), React.createElement(VerticalFrame, {
		spacing = 4,
	}, React.createElement(Label, {
		text = `❤️ Health: {gameState.playerHealth}/{gameState.maxPlayerHealth}`,
		fontSize = 12,
	}), React.createElement(ProgressBar, {
		progress = healthPercentage / 100,
		barColor = if healthPercentage > 60 then Color3.fromHex(COLORS.success) elseif healthPercentage > 30 then Color3.fromHex(COLORS.warning) else Color3.fromHex(COLORS.error),
		backgroundColor = Color3.fromHex(COLORS.bg.secondary),
		size = UDim2.new(1, 0, 0, 20),
	})), React.createElement(VerticalFrame, {
		spacing = 4,
	}, React.createElement(Label, {
		text = `✨ Experience: {gameState.experience}/{gameState.experienceToNextLevel}`,
		fontSize = 12,
	}), React.createElement(ProgressBar, {
		progress = experiencePercentage / 100,
		barColor = Color3.fromHex(COLORS.primary),
		backgroundColor = Color3.fromHex(COLORS.bg.secondary),
		size = UDim2.new(1, 0, 0, 20),
	}))))
	local _exp_2 = gameState.isGameActive and (React.createElement(VerticalFrame, {
		spacing = 8,
	}, React.createElement(Label, {
		text = "⚔️ Battle Info",
		fontSize = 16,
		bold = true,
	}), React.createElement(Label, {
		text = `Wave: {gameState.currentWave} | Enemies: {gameState.enemiesRemaining}/{gameState.enemiesInWave}`,
		fontSize = 14,
	}), gameState.gamePhase == "betweenWaves" and (React.createElement(Label, {
		text = `⏰ Next wave in {math.ceil(gameState.timeUntilNextWave)} seconds`,
		fontSize = 14,
		textColor = COLORS.primary,
	}))))
	local _condition = gameState.isGameActive
	if _condition then
		local _exp_3 = React.createElement(Label, {
			text = "🔥 Unlocked Abilities",
			fontSize = 16,
			bold = true,
		})
		local _exp_4 = gameState.abilitiesUnlocked
		-- ▼ ReadonlyArray.map ▼
		local _newValue = table.create(#_exp_4)
		local _callback = function(abilityId, index)
			local abilityNames = {
				QUAKE_PUNCH = "👊 Quake Punch",
				FIRE_FIST = "🔥 Fire Fist",
				ICE_AGE = "❄️ Ice Age",
				THREE_SWORD_STYLE = "⚔️ Three Sword Style",
				ROOM = "🔵 Room",
			}
			local _attributes = {
				key = index,
			}
			local _condition_1 = abilityNames[abilityId]
			if not (_condition_1 ~= "" and _condition_1) then
				_condition_1 = abilityId
			end
			_attributes.text = _condition_1
			_attributes.fontSize = 12
			_attributes.textColor = COLORS.success
			return React.createElement(Label, _attributes)
		end
		for _k, _v in _exp_4 do
			_newValue[_k] = _callback(_v, _k - 1, _exp_4)
		end
		-- ▲ ReadonlyArray.map ▲
		_condition = (React.createElement(VerticalFrame, {
			spacing = 8,
		}, _exp_3, React.createElement(VerticalFrame, {
			spacing = 4,
		}, _newValue)))
	end
	return React.createElement(Modal, {
		isOpen = isOpen,
		title = "⚔️ Elemental Battle Arena",
		width = 500,
		height = 600,
		onClose = handleClose,
	}, React.createElement(VerticalFrame, {
		spacing = 16,
		padding = 16,
	}, _exp, _exp_1, _exp_2, _condition, React.createElement(VerticalFrame, {
		spacing = 8,
	}, React.createElement(Label, {
		text = "🎮 Controls",
		fontSize = 16,
		bold = true,
	}), if not gameState.isGameActive then (React.createElement(React.Fragment, nil, React.createElement(Button, {
		text = "⚔️ Start Battle",
		variant = "primary",
		onClick = handleStartGame,
		size = UDim2.new(1, 0, 0, 40),
	}), React.createElement(Label, {
		text = "💡 Use abilities (1-5 keys) to defeat waves of enemies! Level up to unlock new abilities!",
		fontSize = 12,
		textColor = COLORS.text.secondary,
		textWrapped = true,
	}))) else (React.createElement(React.Fragment, nil, React.createElement(Button, {
		text = "🛑 Stop Battle",
		variant = "danger",
		onClick = handleStopGame,
		size = UDim2.new(1, 0, 0, 40),
	}), React.createElement(Label, {
		text = "⌨️ Press 1-5 to use abilities. Defeat all enemies to advance to the next wave!",
		fontSize = 12,
		textColor = COLORS.text.secondary,
		textWrapped = true,
	}))), gameState.gamePhase == "ended" and gameState.playerHealth <= 0 and (React.createElement(VerticalFrame, {
		spacing = 8,
	}, React.createElement(Label, {
		text = "💀 Game Over!",
		fontSize = 18,
		textColor = COLORS.error,
		bold = true,
	}), React.createElement(Label, {
		text = `Final Score: {gameState.score} | Waves: {gameState.currentWave - 1} | Level: {gameState.playerLevel}`,
		fontSize = 14,
		textWrapped = true,
	}))))))
end
return {
	ElementalBattleUI = ElementalBattleUI,
}
