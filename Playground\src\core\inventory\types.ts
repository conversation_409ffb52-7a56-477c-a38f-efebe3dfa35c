export enum ItemType {
	Weapon = "Weapon",
	Armor = "Armor",
	Consumable = "Consumable",
	Material = "Material",
	Quest = "Quest",
	Currency = "Currency",
	Cosmetic = "Cosmetic",
	Tool = "Tool",
	Key = "Key",
	Collectible = "Collectible",
}

export enum ItemRarity {
	Common = "Common",
	Uncommon = "Uncommon", 
	Rare = "Rare",
	Epic = "Epic",
	Legendary = "Legendary",
	Mythic = "Mythic",
}

export enum ItemQuality {
	Poor = "Poor",
	Normal = "Normal",
	Good = "Good",
	Superior = "Superior",
	Perfect = "Perfect",
}

export interface ItemAttributes {
	[key: string]: number | string | boolean;
}

export interface ItemEffect {
	type: string;
	value: number;
	duration?: number;
	description: string;
}

export interface ItemDefinition {
	id: string;
	name: string;
	description: string;
	type: ItemType;
	rarity: ItemRarity;
	quality: ItemQuality;
	maxStack: number;
	value: number; // Base monetary value
	weight: number;
	level: number; // Required level to use
	attributes: ItemAttributes;
	effects: ItemEffect[];
	imageId?: string;
	soundId?: string;
	tags: string[];
	craftingComponents?: Record<string, number>;
	canTrade: boolean;
	canSell: boolean;
	canDrop: boolean;
	durability?: {
		current: number;
		max: number;
	};
	requirements?: {
		level?: number;
		stats?: Record<string, number>;
		items?: Record<string, number>;
	};
}

export interface InventorySlot {
	item: ItemDefinition | undefined;
	quantity: number;
	slotIndex: number;
	locked: boolean;
}

export interface InventoryConfiguration {
	size: number;
	maxWeight: number;
	allowOverflow: boolean;
	autoSort: boolean;
	autoStack: boolean;
	enableSearch: boolean;
	filterTypes: ItemType[];
}

export interface InventoryStats {
	totalItems: number;
	totalWeight: number;
	totalValue: number;
	itemsByType: Record<ItemType, number>;
	itemsByRarity: Record<ItemRarity, number>;
	emptySlots: number;
	occupancyRate: number;
}

export interface ItemTransaction {
	id: string;
	type: "add" | "remove" | "move" | "split" | "merge";
	itemId: string;
	quantity: number;
	fromSlot?: number;
	toSlot?: number;
	timestamp: number;
	playerId?: string;
}