-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local React = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "react")
local COLORS = TS.import(script, game:GetService("ReplicatedStorage"), "core", "design").COLORS
local function Overlay(props)
	local backgroundColor = props.backgroundColor or Color3.fromHex(COLORS.bg.modal)
	local _condition = props.backgroundTransparency
	if _condition == nil then
		_condition = 0.5
	end
	local backgroundTransparency = _condition
	local _condition_1 = props.zIndex
	if _condition_1 == nil then
		_condition_1 = 10
	end
	local zIndex = _condition_1
	local _condition_2 = props.fullScreen
	if _condition_2 == nil then
		_condition_2 = true
	end
	local fullScreen = _condition_2
	-- For full screen overlays, we need to break out of parent constraints
	local frameProps = if fullScreen then {
		BackgroundTransparency = 1,
		Size = UDim2.new(1, 0, 1, 0),
		Position = UDim2.new(0, 0, 0, 0),
		AnchorPoint = Vector2.new(0, 0),
		ZIndex = zIndex,
		ClipsDescendants = false,
	} else {
		BackgroundTransparency = 1,
		Size = UDim2.new(1, 0, 1, 0),
		Position = UDim2.new(0, 0, 0, 0),
		AnchorPoint = Vector2.new(0, 0),
		ZIndex = zIndex,
	}
	local _attributes = table.clone(frameProps)
	setmetatable(_attributes, nil)
	return React.createElement("frame", _attributes, backgroundTransparency < 1 and (React.createElement("textbutton", {
		BackgroundColor3 = backgroundColor,
		BackgroundTransparency = backgroundTransparency,
		Size = UDim2.new(1, 0, 1, 0),
		Position = UDim2.new(0, 0, 0, 0),
		ZIndex = zIndex,
		Text = "",
		AutoButtonColor = false,
		Event = {
			Activated = props.onBackdropClick,
		},
	})), props.children)
end
return {
	Overlay = Overlay,
}
