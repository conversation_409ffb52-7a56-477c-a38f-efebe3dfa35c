import * as React from "@rbxts/react";
import { ErrorContext as CoreErrorContext } from "./types/ErrorTypes";
/**
 * Centralized error handling utilities for the Playground application
 * Provides consistent error logging, recovery strategies, and user feedback
 */
export interface ErrorHandlerContext extends CoreErrorContext {
    timestamp?: number;
}
export interface ErrorRecoveryOptions {
    showToast?: boolean;
    fallbackValue?: unknown;
    retryCallback?: () => void;
    maxRetries?: number;
}
export declare class ErrorHandler {
    private static instance;
    private errorCounts;
    private readonly maxErrorCount;
    static getInstance(): ErrorHandler;
    /**
     * Handle and log an error with context
     */
    handleError(err: unknown, context?: ErrorHandlerContext, options?: ErrorRecoveryOptions): unknown;
    /**
     * Safely execute a function with error handling
     */
    safeExecute<T>(fn: () => T, context?: ErrorHandlerContext, options?: ErrorRecoveryOptions): T | undefined;
    /**
     * Safely execute an async function with error handling
     */
    safeExecuteAsync<T>(fn: () => Promise<T>, context?: ErrorHandlerContext, options?: ErrorRecoveryOptions): Promise<T | undefined>;
    /**
     * Create a safe wrapper for React components
     */
    wrapComponent<T extends Record<string, unknown>>(Component: React.FC<T>, componentName: string): React.FC<T>;
    /**
     * Clear error counts (useful for testing or after fixes)
     */
    clearErrorCounts(): void;
    /**
     * Get error statistics
     */
    getErrorStats(): Array<[string, number]>;
    private formatError;
    private createErrorLog;
    private showErrorToast;
}
export declare const GlobalErrorHandler: ErrorHandler;
export declare function safeCall<T>(fn: () => T, component: string, operation: string, fallback?: T): T | undefined;
export declare function safeCallAsync<T>(fn: () => Promise<T>, component: string, operation: string, fallback?: T): Promise<T | undefined>;
export declare function safeRender<T extends Record<string, unknown>>(Component: React.FC<T>, componentName: string): React.FC<T>;
