import { Result } from "../foundation/types/Result";
import { Error } from "../foundation/types/RobloxError";
export declare enum GameMode {
    Playground = "Playground",
    CollectorArena = "CollectorArena"
}
export interface GameModeState {
    currentMode: GameMode;
    isGameActive: boolean;
    canSwitchMode: boolean;
}
export interface GameController {
    start(): Promise<Result<void, Error>>;
    stop(): Promise<Result<void, Error>>;
    cleanup(): Promise<Result<void, Error>>;
    getName(): string;
}
export declare class GameModeManager {
    private static instance;
    private gameControllers;
    private currentController?;
    private currentMode;
    private isGameActive;
    private canSwitchMode;
    private constructor();
    static getInstance(): GameModeManager;
    registerGameController(mode: GameMode, controller: GameController): void;
    switchToMode(mode: GameMode): Promise<Result<void, Error>>;
    startCurrentGame(): Promise<Result<void, Error>>;
    stopCurrentGame(): Promise<Result<void, Error>>;
    getCurrentMode(): GameMode;
    isGameActiveState(): boolean;
    canSwitchModeState(): boolean;
    getAvailableModes(): GameMode[];
}
