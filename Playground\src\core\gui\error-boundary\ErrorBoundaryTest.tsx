import * as React from "@rbxts/react";
import { ErrorBoundary } from "./ErrorBoundary";

interface ErrorTestProps {
	isOpen: boolean;
	onClose: () => void;
}

/**
 * Test component for verifying error boundary functionality
 * This component can be used to test that error boundaries properly contain errors
 */
export function ErrorBoundaryTest(props: ErrorTestProps) {
	const [shouldError, setShouldError] = React.useState(false);
	const [errorType, setErrorType] = React.useState<string>("throw");

	if (!props.isOpen) {
		return <></>;
	}

	// Test component that throws errors on demand
	function TestErrorComponent(): React.ReactElement {
		if (shouldError) {
			switch (errorType) {
				case "throw":
					throw "Test error: Component deliberately throwing an error";
				case "nil":
					// Simulate calling a nil value
					const nilFunction: unknown = undefined;
					(nilFunction as () => void)();
					break;
				case "runtime":
					// Simulate a runtime error
					const obj: unknown = undefined;
					print((obj as { nonExistentProperty: string }).nonExistentProperty);
					break;
				default:
					throw "Unknown error type";
			}
		}

		return (
			<frame Size={new UDim2(1, 0, 1, 0)} BackgroundTransparency={1}>
				<uilistlayout
					SortOrder={Enum.SortOrder.LayoutOrder}
					FillDirection={Enum.FillDirection.Vertical}
					HorizontalAlignment={Enum.HorizontalAlignment.Center}
					Padding={new UDim(0, 8)}
				/>
				<uipadding
					PaddingTop={new UDim(0, 16)}
					PaddingBottom={new UDim(0, 16)}
					PaddingLeft={new UDim(0, 16)}
					PaddingRight={new UDim(0, 16)}
				/>

				<textlabel
					Text="🧪 Error Test Component"
					Size={new UDim2(1, 0, 0, 24)}
					BackgroundTransparency={1}
					TextColor3={Color3.fromHex("#ffffff")}
					TextSize={16}
					Font={Enum.Font.SourceSansBold}
					TextXAlignment={Enum.TextXAlignment.Center}
					LayoutOrder={1}
				/>

				<textlabel
					Text="This component will throw an error when you click the trigger button. The error boundary should catch it and show a fallback UI."
					Size={new UDim2(1, 0, 0, 60)}
					BackgroundTransparency={1}
					TextColor3={Color3.fromHex("#cccccc")}
					TextSize={12}
					Font={Enum.Font.SourceSans}
					TextXAlignment={Enum.TextXAlignment.Center}
					TextWrapped={true}
					LayoutOrder={2}
				/>

				<textlabel
					Text="Select error type:"
					Size={new UDim2(1, 0, 0, 20)}
					BackgroundTransparency={1}
					TextColor3={Color3.fromHex("#ffffff")}
					TextSize={14}
					Font={Enum.Font.SourceSansBold}
					TextXAlignment={Enum.TextXAlignment.Center}
					LayoutOrder={3}
				/>

				<textbutton
					Text={errorType === "throw" ? "✓ Throw Error" : "Throw Error"}
					Size={new UDim2(1, -32, 0, 32)}
					BackgroundColor3={errorType === "throw" ? Color3.fromHex("#4a9eff") : Color3.fromHex("#666666")}
					BorderSizePixel={0}
					TextColor3={Color3.fromHex("#ffffff")}
					TextSize={14}
					Font={Enum.Font.SourceSans}
					LayoutOrder={4}
					Event={{
						Activated: () => setErrorType("throw"),
					}}
				>
					<uicorner CornerRadius={new UDim(0, 4)} />
				</textbutton>

				<textbutton
					Text={errorType === "nil" ? "✓ Nil Call Error" : "Nil Call Error"}
					Size={new UDim2(1, -32, 0, 32)}
					BackgroundColor3={errorType === "nil" ? Color3.fromHex("#4a9eff") : Color3.fromHex("#666666")}
					BorderSizePixel={0}
					TextColor3={Color3.fromHex("#ffffff")}
					TextSize={14}
					Font={Enum.Font.SourceSans}
					LayoutOrder={5}
					Event={{
						Activated: () => setErrorType("nil"),
					}}
				>
					<uicorner CornerRadius={new UDim(0, 4)} />
				</textbutton>

				<textbutton
					Text={errorType === "runtime" ? "✓ Runtime Error" : "Runtime Error"}
					Size={new UDim2(1, -32, 0, 32)}
					BackgroundColor3={errorType === "runtime" ? Color3.fromHex("#4a9eff") : Color3.fromHex("#666666")}
					BorderSizePixel={0}
					TextColor3={Color3.fromHex("#ffffff")}
					TextSize={14}
					Font={Enum.Font.SourceSans}
					LayoutOrder={6}
					Event={{
						Activated: () => setErrorType("runtime"),
					}}
				>
					<uicorner CornerRadius={new UDim(0, 4)} />
				</textbutton>

				<textbutton
					Text="🔥 TRIGGER ERROR"
					Size={new UDim2(1, -32, 0, 40)}
					BackgroundColor3={Color3.fromHex("#ff4444")}
					BorderSizePixel={0}
					TextColor3={Color3.fromHex("#ffffff")}
					TextSize={16}
					Font={Enum.Font.SourceSansBold}
					LayoutOrder={7}
					Event={{
						Activated: () => {
							print(`🧪 Triggering ${errorType} error...`);
							setShouldError(true);
						},
					}}
				>
					<uicorner CornerRadius={new UDim(0, 4)} />
				</textbutton>
			</frame>
		);
	}

	return (
		<frame
			Size={new UDim2(0, 400, 0, 500)}
			Position={new UDim2(0.5, 0, 0.5, 0)}
			AnchorPoint={new Vector2(0.5, 0.5)}
			BackgroundColor3={Color3.fromHex("#2a2a2a")}
			BorderSizePixel={2}
			BorderColor3={Color3.fromHex("#4a9eff")}
		>
			<uicorner CornerRadius={new UDim(0, 8)} />
			<uipadding
				PaddingTop={new UDim(0, 16)}
				PaddingBottom={new UDim(0, 16)}
				PaddingLeft={new UDim(0, 16)}
				PaddingRight={new UDim(0, 16)}
			/>

			<uilistlayout
				SortOrder={Enum.SortOrder.LayoutOrder}
				FillDirection={Enum.FillDirection.Vertical}
				HorizontalAlignment={Enum.HorizontalAlignment.Center}
				Padding={new UDim(0, 8)}
			/>

			{/* Header */}
			<frame Size={new UDim2(1, 0, 0, 40)} BackgroundTransparency={1} LayoutOrder={1}>
				<uilistlayout
					SortOrder={Enum.SortOrder.LayoutOrder}
					FillDirection={Enum.FillDirection.Horizontal}
					VerticalAlignment={Enum.VerticalAlignment.Center}
				/>

				<textlabel
					Text="🧪 Error Boundary Test"
					Size={new UDim2(1, -40, 1, 0)}
					BackgroundTransparency={1}
					TextColor3={Color3.fromHex("#ffffff")}
					TextSize={18}
					Font={Enum.Font.SourceSansBold}
					TextXAlignment={Enum.TextXAlignment.Left}
					LayoutOrder={1}
				/>

				<textbutton
					Text="✕"
					Size={new UDim2(0, 32, 0, 32)}
					BackgroundColor3={Color3.fromHex("#ff4444")}
					BorderSizePixel={0}
					TextColor3={Color3.fromHex("#ffffff")}
					TextSize={16}
					Font={Enum.Font.SourceSansBold}
					LayoutOrder={2}
					Event={{
						Activated: props.onClose,
					}}
				>
					<uicorner CornerRadius={new UDim(0, 4)} />
				</textbutton>
			</frame>

			{/* Content */}
			<frame Size={new UDim2(1, 0, 1, -50)} BackgroundTransparency={1} LayoutOrder={2}>
				<ErrorBoundary
					onError={(err: unknown, errorId: string) => {
						warn(`🧪 [ErrorBoundaryTest] Caught test error: ${err} (ID: ${errorId})`);
						print("✅ Error boundary is working correctly!");
					}}
					fallback={(err: unknown, errorId: string, retry: () => void) => (
						<frame Size={new UDim2(1, 0, 1, 0)} BackgroundTransparency={1}>
							<uilistlayout
								SortOrder={Enum.SortOrder.LayoutOrder}
								FillDirection={Enum.FillDirection.Vertical}
								HorizontalAlignment={Enum.HorizontalAlignment.Center}
								Padding={new UDim(0, 8)}
							/>
							<uipadding
								PaddingTop={new UDim(0, 16)}
								PaddingBottom={new UDim(0, 16)}
								PaddingLeft={new UDim(0, 16)}
								PaddingRight={new UDim(0, 16)}
							/>

							<textlabel
								Text="✅ Error Boundary Works!"
								Size={new UDim2(1, 0, 0, 24)}
								BackgroundTransparency={1}
								TextColor3={Color3.fromHex("#44ff44")}
								TextSize={16}
								Font={Enum.Font.SourceSansBold}
								TextXAlignment={Enum.TextXAlignment.Center}
								LayoutOrder={1}
							/>

							<textlabel
								Text={`Successfully caught error: ${err}`}
								Size={new UDim2(1, 0, 0, 60)}
								BackgroundTransparency={1}
								TextColor3={Color3.fromHex("#cccccc")}
								TextSize={12}
								Font={Enum.Font.SourceSans}
								TextXAlignment={Enum.TextXAlignment.Center}
								TextWrapped={true}
								LayoutOrder={2}
							/>

							<textlabel
								Text={`Error ID: ${errorId}`}
								Size={new UDim2(1, 0, 0, 20)}
								BackgroundTransparency={1}
								TextColor3={Color3.fromHex("#888888")}
								TextSize={12}
								Font={Enum.Font.SourceSans}
								TextXAlignment={Enum.TextXAlignment.Center}
								LayoutOrder={3}
							/>

							<textbutton
								Text="🔄 Reset Test"
								Size={new UDim2(1, -32, 0, 32)}
								BackgroundColor3={Color3.fromHex("#4a9eff")}
								BorderSizePixel={0}
								TextColor3={Color3.fromHex("#ffffff")}
								TextSize={14}
								Font={Enum.Font.SourceSansBold}
								LayoutOrder={4}
								Event={{
									Activated: retry,
								}}
							>
								<uicorner CornerRadius={new UDim(0, 4)} />
							</textbutton>

							<textlabel
								Text="This proves that the error boundary is properly isolating component failures and preventing UI crashes."
								Size={new UDim2(1, 0, 0, 40)}
								BackgroundTransparency={1}
								TextColor3={Color3.fromHex("#aaaaaa")}
								TextSize={12}
								Font={Enum.Font.SourceSans}
								TextXAlignment={Enum.TextXAlignment.Center}
								TextWrapped={true}
								LayoutOrder={5}
							/>
						</frame>
					)}
				>
					<TestErrorComponent />
				</ErrorBoundary>
			</frame>

			{/* Instructions */}
			<frame Size={new UDim2(1, 0, 0, 80)} BackgroundTransparency={1} LayoutOrder={3}>
				<textlabel
					Text="Instructions: Select an error type and click 'TRIGGER ERROR'. The error boundary should catch it and show a success message instead of crashing the UI."
					Size={new UDim2(1, 0, 1, 0)}
					BackgroundTransparency={1}
					TextColor3={Color3.fromHex("#aaaaaa")}
					TextSize={12}
					Font={Enum.Font.SourceSans}
					TextWrapped={true}
					TextXAlignment={Enum.TextXAlignment.Center}
					TextYAlignment={Enum.TextYAlignment.Top}
				/>
			</frame>
		</frame>
	);
}