-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local _services = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "services")
local Players = _services.Players
local RunService = _services.RunService
local Lighting = _services.Lighting
local WorldEventBroadcaster
do
	WorldEventBroadcaster = setmetatable({}, {
		__tostring = function()
			return "WorldEventBroadcaster"
		end,
	})
	WorldEventBroadcaster.__index = WorldEventBroadcaster
	function WorldEventBroadcaster.new(...)
		local self = setmetatable({}, WorldEventBroadcaster)
		return self:constructor(...) or self
	end
	function WorldEventBroadcaster:constructor()
	end
	function WorldEventBroadcaster:initialize()
		if self.updateConnection then
			return nil
		end
		self.updateConnection = RunService.Heartbeat:Connect(function()
			self:updateActiveEvents()
		end)
		print("🌍 WorldEventBroadcaster initialized")
	end
	function WorldEventBroadcaster:triggerEarthquake(options)
		self.eventCounter = self.eventCounter + 1
		local eventId = `earthquake_{self.eventCounter}_{tick()}`
		local _object = {
			id = eventId,
			options = options,
			startTime = tick(),
		}
		local _left = "endTime"
		local _value = options.duration
		_object[_left] = if _value ~= 0 and _value == _value and _value then tick() + options.duration else nil
		_object.status = "active"
		_object.affectedRegions = self:calculateAffectedRegions(options)
		_object.participants = {}
		local worldEvent = _object
		self.activeEvents[eventId] = worldEvent
		self:broadcastEventStart(worldEvent)
		-- Execute earthquake phases
		self:executeEarthquakePhases(worldEvent, options)
		print(`🌍 Triggered earthquake event: {eventId} magnitude {options.magnitude}`)
		return eventId
	end
	function WorldEventBroadcaster:triggerTsunami(options)
		self.eventCounter = self.eventCounter + 1
		local eventId = `tsunami_{self.eventCounter}_{tick()}`
		local _object = {
			id = eventId,
			options = options,
			startTime = tick(),
		}
		local _left = "endTime"
		local _value = options.duration
		_object[_left] = if _value ~= 0 and _value == _value and _value then tick() + options.duration else nil
		_object.status = "active"
		_object.affectedRegions = self:calculateAffectedRegions(options)
		_object.participants = {}
		local worldEvent = _object
		self.activeEvents[eventId] = worldEvent
		self:broadcastEventStart(worldEvent)
		self:executeTsunamiPhases(worldEvent, options)
		print(`🌊 Triggered tsunami event: {eventId}`)
		return eventId
	end
	function WorldEventBroadcaster:triggerMeteorImpact(options)
		self.eventCounter = self.eventCounter + 1
		local eventId = `meteor_{self.eventCounter}_{tick()}`
		local _object = {
			id = eventId,
			options = options,
			startTime = tick(),
		}
		local _left = "endTime"
		local _value = options.duration
		_object[_left] = if _value ~= 0 and _value == _value and _value then tick() + options.duration else nil
		_object.status = "active"
		_object.affectedRegions = self:calculateAffectedRegions(options)
		_object.participants = {}
		local worldEvent = _object
		self.activeEvents[eventId] = worldEvent
		self:broadcastEventStart(worldEvent)
		self:executeMeteorPhases(worldEvent, options)
		print(`☄️ Triggered meteor event: {eventId}`)
		return eventId
	end
	function WorldEventBroadcaster:triggerLightningStorm(options)
		self.eventCounter = self.eventCounter + 1
		local eventId = `lightning_{self.eventCounter}_{tick()}`
		local _object = {
			id = eventId,
			options = options,
			startTime = tick(),
		}
		local _left = "endTime"
		local _value = options.duration
		_object[_left] = if _value ~= 0 and _value == _value and _value then tick() + options.duration else nil
		_object.status = "active"
		_object.affectedRegions = self:calculateAffectedRegions(options)
		_object.participants = {}
		local worldEvent = _object
		self.activeEvents[eventId] = worldEvent
		self:broadcastEventStart(worldEvent)
		self:executeLightningStormPhases(worldEvent, options)
		print(`⚡ Triggered lightning storm event: {eventId}`)
		return eventId
	end
	function WorldEventBroadcaster:executeEarthquakePhases(event, options)
		local _exp = {
			name = "initial_tremor",
			startTime = 0,
			duration = 2,
			effects = { {
				type = "physics",
				data = {
					intensity = options.magnitude * 0.3,
				},
			}, {
				type = "audio",
				data = {
					sound = "rumble_low",
				},
			} },
		}
		local _object = {
			name = "main_quake",
			startTime = 2,
		}
		local _left = "duration"
		local _value = options.duration
		_object[_left] = if _value ~= 0 and _value == _value and _value then options.duration * 0.6 else 10
		_object.effects = { {
			type = "physics",
			data = {
				intensity = options.magnitude,
			},
		}, {
			type = "visual",
			data = {
				cracks = true,
				debris = true,
			},
		}, {
			type = "audio",
			data = {
				sound = "earthquake_main",
			},
		}, {
			type = "environmental",
			data = {
				lighting = "shake",
			},
		} }
		local _object_1 = {
			name = "aftershock",
		}
		local _left_1 = "startTime"
		local _condition = options.duration
		if not (_condition ~= 0 and _condition == _condition and _condition) then
			_condition = 15
		end
		_object_1[_left_1] = _condition * 0.8
		local _left_2 = "duration"
		local _condition_1 = options.duration
		if not (_condition_1 ~= 0 and _condition_1 == _condition_1 and _condition_1) then
			_condition_1 = 15
		end
		_object_1[_left_2] = _condition_1 * 0.2
		_object_1.effects = { {
			type = "physics",
			data = {
				intensity = options.magnitude * 0.5,
			},
		}, {
			type = "audio",
			data = {
				sound = "rumble_fade",
			},
		} }
		local phases = { _exp, _object, _object_1 }
		self:executeEventPhases(event, phases)
	end
	function WorldEventBroadcaster:executeTsunamiPhases(event, options)
		local _exp = {
			name = "water_recede",
			startTime = 0,
			duration = 5,
			effects = { {
				type = "environmental",
				data = {
					waterLevel = -10,
				},
			}, {
				type = "audio",
				data = {
					sound = "water_recede",
				},
			} },
		}
		local _exp_1 = {
			name = "wave_approach",
			startTime = 5,
			duration = 10,
			effects = { {
				type = "visual",
				data = {
					waveHeight = options.waveHeight,
				},
			}, {
				type = "audio",
				data = {
					sound = "tsunami_roar",
				},
			} },
		}
		local _object = {
			name = "impact",
			startTime = 15,
		}
		local _left = "duration"
		local _value = options.duration
		_object[_left] = if _value ~= 0 and _value == _value and _value then options.duration - 15 else 20
		_object.effects = { {
			type = "physics",
			data = {
				waterForce = options.waveSpeed,
			},
		}, {
			type = "environmental",
			data = {
				flooding = true,
			},
		}, {
			type = "visual",
			data = {
				waterEffects = true,
			},
		} }
		local phases = { _exp, _exp_1, _object }
		self:executeEventPhases(event, phases)
	end
	function WorldEventBroadcaster:executeMeteorPhases(event, options)
		local _exp = {
			name = "meteor_visible",
			startTime = 0,
			duration = 5,
			effects = { {
				type = "visual",
				data = {
					meteorTrail = true,
					size = options.meteorSize,
				},
			}, {
				type = "audio",
				data = {
					sound = "meteor_whistle",
				},
			}, {
				type = "environmental",
				data = {
					lighting = "meteor_glow",
				},
			} },
		}
		local _exp_1 = {
			name = "impact",
			startTime = 5,
			duration = 1,
			effects = { {
				type = "physics",
				data = {
					explosionForce = options.explosionRadius * 100,
				},
			}, {
				type = "visual",
				data = {
					explosion = true,
					crater = true,
				},
			}, {
				type = "audio",
				data = {
					sound = "meteor_impact",
				},
			}, {
				type = "environmental",
				data = {
					lighting = "flash",
				},
			} },
		}
		local _object = {
			name = "aftermath",
			startTime = 6,
		}
		local _left = "duration"
		local _condition = options.duration
		if not (_condition ~= 0 and _condition == _condition and _condition) then
			_condition = 30
		end
		_object[_left] = _condition - 6
		_object.effects = { {
			type = "environmental",
			data = {
				dust = true,
				debris = true,
			},
		}, {
			type = "audio",
			data = {
				sound = "aftermath_rumble",
			},
		} }
		local phases = { _exp, _exp_1, _object }
		self:executeEventPhases(event, phases)
	end
	function WorldEventBroadcaster:executeLightningStormPhases(event, options)
		local _exp = {
			name = "storm_buildup",
			startTime = 0,
			duration = 10,
			effects = { {
				type = "environmental",
				data = {
					clouds = "dark",
					wind = "strong",
				},
			}, {
				type = "audio",
				data = {
					sound = "thunder_distant",
				},
			} },
		}
		local _object = {
			name = "active_storm",
			startTime = 10,
		}
		local _left = "duration"
		local _value = options.duration
		_object[_left] = if _value ~= 0 and _value == _value and _value then options.duration - 20 else 30
		_object.effects = { {
			type = "visual",
			data = {
				lightning = true,
				frequency = options.strikeInterval,
			},
		}, {
			type = "audio",
			data = {
				sound = "thunder_close",
			},
		}, {
			type = "environmental",
			data = {
				lighting = "storm",
			},
		} }
		local _object_1 = {
			name = "storm_fade",
		}
		local _left_1 = "startTime"
		local _condition = options.duration
		if not (_condition ~= 0 and _condition == _condition and _condition) then
			_condition = 50
		end
		_object_1[_left_1] = _condition - 10
		_object_1.duration = 10
		_object_1.effects = { {
			type = "environmental",
			data = {
				clouds = "clearing",
			},
		}, {
			type = "audio",
			data = {
				sound = "thunder_fade",
			},
		} }
		local phases = { _exp, _object, _object_1 }
		self:executeEventPhases(event, phases)
	end
	function WorldEventBroadcaster:executeEventPhases(event, phases)
		for _, phase in phases do
			task.delay(phase.startTime, function()
				local _activeEvents = self.activeEvents
				local _id = event.id
				if _activeEvents[_id] ~= nil then
					self:executePhase(event, phase)
				end
			end)
		end
	end
	function WorldEventBroadcaster:executePhase(event, phase)
		print(`🌍 Executing phase: {phase.name} for event {event.id}`)
		for _, effect in phase.effects do
			local _condition = effect.delay
			if not (_condition ~= 0 and _condition == _condition and _condition) then
				_condition = 0
			end
			local delay = _condition
			task.delay(delay, function()
				self:applyEventEffect(event, effect)
			end)
		end
	end
	function WorldEventBroadcaster:applyEventEffect(event, effect)
		local _condition = not (effect ~= 0 and effect == effect and effect ~= "" and effect)
		if not _condition then
			local _effect = effect
			_condition = not (type(_effect) == "table")
		end
		if _condition then
			return nil
		end
		local effectObj = effect
		local effectType = effectObj.type
		local effectData = effectObj.data
		repeat
			if effectType == "environmental" then
				self:applyEnvironmentalEffect(event, effectData)
				break
			end
			if effectType == "visual" then
				self:applyVisualEffect(event, effectData)
				break
			end
			if effectType == "audio" then
				self:applyAudioEffect(event, effectData)
				break
			end
			if effectType == "physics" then
				self:applyPhysicsEffect(event, effectData)
				break
			end
		until true
	end
	function WorldEventBroadcaster:applyEnvironmentalEffect(event, data)
		local _condition = not (data ~= 0 and data == data and data ~= "" and data)
		if not _condition then
			local _data = data
			_condition = not (type(_data) == "table")
		end
		if _condition then
			return nil
		end
		local dataObj = data
		if dataObj.lighting == "shake" then
			-- Shake the lighting
			local originalBrightness = Lighting.Brightness
			for i = 0, 9 do
				task.delay(i * 0.1, function()
					Lighting.Brightness = originalBrightness + (math.random() - 0.5) * 0.5
				end)
			end
			task.delay(1, function()
				Lighting.Brightness = originalBrightness
			end)
		end
		if dataObj.lighting == "flash" then
			local originalBrightness = Lighting.Brightness
			Lighting.Brightness = 5
			task.delay(0.1, function()
				Lighting.Brightness = originalBrightness
			end)
		end
	end
	function WorldEventBroadcaster:applyVisualEffect(event, data)
		-- Implementation would create particle effects, explosions, etc.
		print(`✨ Applying visual effect: {tostring(data)}`)
	end
	function WorldEventBroadcaster:applyAudioEffect(event, data)
		local _condition = not (data ~= 0 and data == data and data ~= "" and data)
		if not _condition then
			local _data = data
			_condition = not (type(_data) == "table")
		end
		if _condition then
			return nil
		end
		local dataObj = data
		local sound = dataObj.sound
		-- Implementation would play sounds
		print(`🔊 Playing sound: {tostring(sound)}`)
	end
	function WorldEventBroadcaster:applyPhysicsEffect(event, data)
		local _condition = not (data ~= 0 and data == data and data ~= "" and data)
		if not _condition then
			local _data = data
			_condition = not (type(_data) == "table")
		end
		if _condition then
			return nil
		end
		local dataObj = data
		local intensity = dataObj.intensity
		-- Implementation would apply forces, shake objects, etc.
		print(`⚡ Applying physics effect with intensity: {tostring(intensity)}`)
	end
	function WorldEventBroadcaster:calculateAffectedRegions(options)
		local regions = {}
		local _value = options.center and options.radius
		if _value ~= 0 and _value == _value and _value then
			local _center = options.center
			local _vector3 = Vector3.new(options.radius, options.radius, options.radius)
			local _exp = _center - _vector3
			local _center_1 = options.center
			local _vector3_1 = Vector3.new(options.radius, options.radius, options.radius)
			local region = Region3.new(_exp, _center_1 + _vector3_1)
			table.insert(regions, region)
		end
		return regions
	end
	function WorldEventBroadcaster:broadcastEventStart(event)
		local players = self:getAffectedPlayers(event)
		for _, player in players do
			-- Send event notification to client
			-- Implementation would use RemoteEvents
			print(`📢 Notifying {player.Name} of {event.options.eventType} event`)
		end
	end
	function WorldEventBroadcaster:getAffectedPlayers(event)
		local options = event.options
		if options.affectedPlayers == "all" then
			return Players:GetPlayers()
		elseif options.affectedPlayers == "specific" and options.specificPlayers then
			return options.specificPlayers
		else
			local _value = options.affectedPlayers == "nearby" and options.center and options.radius
			if _value ~= 0 and _value == _value and _value then
				return self:getPlayersInRadius(options.center, options.radius)
			end
		end
		return {}
	end
	function WorldEventBroadcaster:getPlayersInRadius(center, radius)
		local playersInRadius = {}
		for _, player in Players:GetPlayers() do
			if player.Character then
				local humanoidRootPart = player.Character:FindFirstChild("HumanoidRootPart")
				if humanoidRootPart then
					local _center = center
					local _position = humanoidRootPart.Position
					local distance = (_center - _position).Magnitude
					if distance <= radius then
						table.insert(playersInRadius, player)
					end
				end
			end
		end
		return playersInRadius
	end
	function WorldEventBroadcaster:updateActiveEvents()
		local currentTime = tick()
		for eventId, event in self.activeEvents do
			local _condition = event.endTime
			if _condition ~= 0 and _condition == _condition and _condition then
				_condition = currentTime >= event.endTime
			end
			if _condition ~= 0 and _condition == _condition and _condition then
				self:completeEvent(eventId)
			end
		end
	end
	function WorldEventBroadcaster:completeEvent(eventId)
		local _activeEvents = self.activeEvents
		local _eventId = eventId
		local event = _activeEvents[_eventId]
		if not event then
			return nil
		end
		event.status = "completed"
		local _exp = self.eventHistory
		table.insert(_exp, event)
		local _activeEvents_1 = self.activeEvents
		local _eventId_1 = eventId
		_activeEvents_1[_eventId_1] = nil
		print(`🌍 Completed world event: {eventId}`)
	end
	function WorldEventBroadcaster:cancelEvent(eventId)
		local _activeEvents = self.activeEvents
		local _eventId = eventId
		local event = _activeEvents[_eventId]
		if not event then
			return nil
		end
		event.status = "cancelled"
		local _activeEvents_1 = self.activeEvents
		local _eventId_1 = eventId
		_activeEvents_1[_eventId_1] = nil
		print(`🌍 Cancelled world event: {eventId}`)
	end
	function WorldEventBroadcaster:getActiveEvents()
		return self.activeEvents
	end
	function WorldEventBroadcaster:getEventHistory()
		return self.eventHistory
	end
	function WorldEventBroadcaster:shutdown()
		if self.updateConnection then
			self.updateConnection:Disconnect()
			self.updateConnection = nil
		end
		-- Cancel all active events
		local eventIds = {}
		for eventId in self.activeEvents do
			table.insert(eventIds, eventId)
		end
		for _, eventId in eventIds do
			self:cancelEvent(eventId)
		end
		print("🌍 WorldEventBroadcaster shutdown")
	end
	WorldEventBroadcaster.activeEvents = {}
	WorldEventBroadcaster.eventCounter = 0
	WorldEventBroadcaster.eventHistory = {}
end
return {
	WorldEventBroadcaster = WorldEventBroadcaster,
}
