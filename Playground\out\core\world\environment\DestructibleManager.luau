-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local _services = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "services")
local Workspace = _services.Workspace
local TweenService = _services.TweenService
local Debris = _services.Debris
local DestructibleManager
do
	DestructibleManager = setmetatable({}, {
		__tostring = function()
			return "DestructibleManager"
		end,
	})
	DestructibleManager.__index = DestructibleManager
	function DestructibleManager.new(...)
		local self = setmetatable({}, DestructibleManager)
		return self:constructor(...) or self
	end
	function DestructibleManager:constructor()
	end
	function DestructibleManager:createDestructibleZone(options)
		self.zoneCounter = self.zoneCounter + 1
		local zoneId = `destruction_{self.zoneCounter}_{tick()}`
		-- Find all parts in the destruction radius
		local affectedParts = self:findPartsInRadius(options.center, options.radius, options.excludeAnchored)
		-- Store original states for potential restoration
		local originalStates = {}
		for _, part in affectedParts do
			local _arg1 = self:capturePartState(part)
			originalStates[part] = _arg1
		end
		-- Create the destructible zone
		local _object = {
			id = zoneId,
			center = options.center,
			radius = options.radius,
			createdAt = tick(),
		}
		local _left = "expiresAt"
		local _value = options.duration
		_object[_left] = if _value ~= 0 and _value == _value and _value then tick() + options.duration else nil
		_object.destructionType = options.destructionType
		_object.affectedParts = affectedParts
		_object.originalStates = originalStates
		local zone = _object
		self.activeZones[zoneId] = zone
		-- Apply destruction effects
		self:applyDestruction(zone, options)
		-- Set up cleanup if temporary
		local _value_1 = options.duration
		if _value_1 ~= 0 and _value_1 == _value_1 and _value_1 then
			task.delay(options.duration, function()
				self:restoreZone(zoneId)
			end)
		end
		print(`🌍 Created destructible zone: {zoneId} affecting {#affectedParts} parts`)
		return zoneId
	end
	function DestructibleManager:applyDestruction(zone, options)
		for _, part in zone.affectedParts do
			local _center = zone.center
			local _position = part.Position
			local distanceFromCenter = (_center - _position).Magnitude
			local distanceRatio = distanceFromCenter / zone.radius
			local effectIntensity = options.intensity * (1 - distanceRatio)
			self:applyDestructionToPart(part, options.destructionType, effectIntensity, options)
		end
		-- Create visual effects
		if options.effects then
			for _, effect in options.effects do
				self:createVisualEffect(zone.center, effect)
			end
		end
	end
	function DestructibleManager:applyDestructionToPart(part, destructionType, intensity, options)
		repeat
			if destructionType == "earthquake" then
				self:applyEarthquakeEffect(part, intensity)
				break
			end
			if destructionType == "explosion" then
				self:applyExplosionEffect(part, intensity, options.center)
				break
			end
			if destructionType == "slash" then
				self:applySlashEffect(part, intensity)
				break
			end
			if destructionType == "impact" then
				self:applyImpactEffect(part, intensity, options.center)
				break
			end
			if destructionType == "freeze" then
				self:applyFreezeEffect(part, intensity)
				break
			end
			if destructionType == "burn" then
				self:applyBurnEffect(part, intensity)
				break
			end
		until true
		-- Create debris if specified
		if options.createDebris and intensity > 0.5 then
			self:createDebris(part, intensity)
		end
	end
	function DestructibleManager:applyEarthquakeEffect(part, intensity)
		-- Create cracks effect
		self:createCracksOnPart(part, intensity)
		-- Shake the part
		local originalPosition = part.Position
		local shakeIntensity = intensity * 5
		local _exp = part
		local _exp_1 = TweenInfo.new(0.1, Enum.EasingStyle.Bounce, Enum.EasingDirection.InOut, 5, true)
		local _object = {}
		local _left = "Position"
		local _vector3 = Vector3.new((math.random() - 0.5) * shakeIntensity, (math.random() - 0.5) * shakeIntensity, (math.random() - 0.5) * shakeIntensity)
		_object[_left] = originalPosition + _vector3
		local shakeTween = TweenService:Create(_exp, _exp_1, _object)
		shakeTween:Play()
		-- Return to original position after shaking
		shakeTween.Completed:Connect(function()
			local returnTween = TweenService:Create(part, TweenInfo.new(0.5, Enum.EasingStyle.Quad, Enum.EasingDirection.Out), {
				Position = originalPosition,
			})
			returnTween:Play()
		end)
		-- Damage the part based on intensity
		if intensity > 0.7 then
			part.Transparency = math.min(part.Transparency + intensity * 0.3, 0.8)
			part.Material = Enum.Material.CrackedLava
		end
	end
	function DestructibleManager:applyExplosionEffect(part, intensity, center)
		-- Calculate force direction
		local _position = part.Position
		local _center = center
		local forceDirection = (_position - _center).Unit
		local _arg0 = intensity * 50
		local force = forceDirection * _arg0
		-- Apply force if part is not anchored
		if not part.Anchored then
			local bodyVelocity = Instance.new("BodyVelocity")
			bodyVelocity.MaxForce = Vector3.new(4000, 4000, 4000)
			bodyVelocity.Velocity = force
			bodyVelocity.Parent = part
			-- Remove force after short time
			task.delay(0.5, function()
				if bodyVelocity.Parent then
					bodyVelocity:Destroy()
				end
			end)
		end
		-- Visual damage
		if intensity > 0.5 then
			part.Material = Enum.Material.CorrodedMetal
			part.Color = part.Color:Lerp(Color3.new(0.2, 0.2, 0.2), intensity * 0.5)
		end
	end
	function DestructibleManager:applyFreezeEffect(part, intensity)
		-- Change material to ice
		part.Material = Enum.Material.Ice
		part.Color = part.Color:Lerp(Color3.new(0.7, 0.9, 1), intensity)
		part.Transparency = math.min(part.Transparency + intensity * 0.2, 0.5)
		-- Add frost particles
		self:createFrostEffect(part)
	end
	function DestructibleManager:createCracksOnPart(part, intensity)
		local crackDecal = Instance.new("Decal")
		crackDecal.Texture = "rbxasset://textures/face.png"
		crackDecal.Face = Enum.NormalId.Top
		crackDecal.Transparency = 1 - intensity
		crackDecal.Parent = part
		-- Fade out cracks over time
		local fadeTween = TweenService:Create(crackDecal, TweenInfo.new(10, Enum.EasingStyle.Linear), {
			Transparency = 1,
		})
		fadeTween:Play()
		fadeTween.Completed:Connect(function()
			return crackDecal:Destroy()
		end)
	end
	function DestructibleManager:createDebris(originalPart, intensity)
		local debrisCount = math.floor(intensity * 5) + 1
		do
			local i = 0
			local _shouldIncrement = false
			while true do
				if _shouldIncrement then
					i += 1
				else
					_shouldIncrement = true
				end
				if not (i < debrisCount) then
					break
				end
				local debris = Instance.new("Part")
				local _size = originalPart.Size
				local _arg0 = math.random() * 3 + 2
				debris.Size = _size / _arg0
				debris.Material = originalPart.Material
				debris.Color = originalPart.Color
				local _position = originalPart.Position
				local _vector3 = Vector3.new((math.random() - 0.5) * 10, math.random() * 5, (math.random() - 0.5) * 10)
				debris.Position = _position + _vector3
				debris.Rotation = Vector3.new(math.random() * 360, math.random() * 360, math.random() * 360)
				debris.Parent = Workspace
				-- Add random velocity
				local bodyVelocity = Instance.new("BodyVelocity")
				bodyVelocity.MaxForce = Vector3.new(4000, 4000, 4000)
				bodyVelocity.Velocity = Vector3.new((math.random() - 0.5) * 20, math.random() * 15 + 5, (math.random() - 0.5) * 20)
				bodyVelocity.Parent = debris
				-- Clean up debris after some time
				Debris:AddItem(debris, 30)
				task.delay(1, function()
					if bodyVelocity.Parent then
						bodyVelocity:Destroy()
					end
				end)
			end
		end
	end
	function DestructibleManager:createFrostEffect(part)
		-- Implementation would create particle effects for frost
		-- This is a placeholder for the actual particle system
		print(`❄️ Creating frost effect on {part.Name}`)
	end
	function DestructibleManager:createVisualEffect(position, effect)
		-- Implementation would create particle effects, explosions, etc.
		print(`✨ Creating {effect.type} effect at {position}`)
	end
	function DestructibleManager:findPartsInRadius(center, radius, excludeAnchored)
		local parts = {}
		-- Use GetPartBoundsInBox for efficient spatial queries
		local _center = center
		local _vector3 = Vector3.new(radius, radius, radius)
		local _exp = _center - _vector3
		local _center_1 = center
		local _vector3_1 = Vector3.new(radius, radius, radius)
		local region = Region3.new(_exp, _center_1 + _vector3_1)
		-- Get parts in the region using spatial query
		local partsInRegion = Workspace:GetPartBoundsInBox(CFrame.new(center), Vector3.new(radius * 2, radius * 2, radius * 2))
		for _, part in partsInRegion do
			if part:IsA("Part") then
				local _center_2 = center
				local _position = part.Position
				local distance = (_center_2 - _position).Magnitude
				if distance <= radius then
					if not excludeAnchored or not part.Anchored then
						table.insert(parts, part)
					end
				end
			end
		end
		return parts
	end
	function DestructibleManager:capturePartState(part)
		return {
			position = part.Position,
			rotation = part.Rotation,
			size = part.Size,
			transparency = part.Transparency,
			material = part.Material,
			color = part.Color,
			anchored = part.Anchored,
		}
	end
	function DestructibleManager:restoreZone(zoneId)
		local _activeZones = self.activeZones
		local _zoneId = zoneId
		local zone = _activeZones[_zoneId]
		if not zone then
			return nil
		end
		for part, originalState in zone.originalStates do
			if part.Parent then
				-- Restore original properties
				part.Position = originalState.position
				part.Rotation = originalState.rotation
				part.Size = originalState.size
				part.Transparency = originalState.transparency
				part.Material = originalState.material
				part.Color = originalState.color
				part.Anchored = originalState.anchored
			end
		end
		local _activeZones_1 = self.activeZones
		local _zoneId_1 = zoneId
		_activeZones_1[_zoneId_1] = nil
		print(`🔄 Restored destructible zone: {zoneId}`)
	end
	function DestructibleManager:getActiveZones()
		return self.activeZones
	end
	function DestructibleManager:cleanupExpiredZones()
		local currentTime = tick()
		for zoneId, zone in self.activeZones do
			local _condition = zone.expiresAt
			if _condition ~= 0 and _condition == _condition and _condition then
				_condition = currentTime >= zone.expiresAt
			end
			if _condition ~= 0 and _condition == _condition and _condition then
				self:restoreZone(zoneId)
			end
		end
	end
	function DestructibleManager:applySlashEffect(part, intensity)
		-- Implementation for slash effects (Zoro abilities)
	end
	function DestructibleManager:applyImpactEffect(part, intensity, center)
		-- Implementation for impact effects (punch abilities)
	end
	function DestructibleManager:applyBurnEffect(part, intensity)
		-- Implementation for burn effects (Ace abilities)
	end
	DestructibleManager.activeZones = {}
	DestructibleManager.zoneCounter = 0
end
return {
	DestructibleManager = DestructibleManager,
}
