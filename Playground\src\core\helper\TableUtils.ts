/**
 * Table and data structure utilities for game development
 */
export class TableUtils {
	/**
	 * Deep copy a table/object
	 */
	static deepCopy<T>(obj: T): T {
		if (typeOf(obj) !== "table") {
			return obj;
		}

		const copy = {} as T;
		for (const [key, value] of pairs(obj as Record<string, unknown>)) {
			(copy as Record<string, unknown>)[key] = this.deepCopy(value);
		}

		return copy;
	}

	/**
	 * Shallow copy a table/object
	 */
	static shallowCopy<T>(obj: T): T {
		if (typeOf(obj) !== "table") {
			return obj;
		}

		const copy = {} as T;
		for (const [key, value] of pairs(obj as Record<string, unknown>)) {
			(copy as Record<string, unknown>)[key] = value;
		}

		return copy;
	}

	/**
	 * Merge two or more objects
	 */
	static merge<T>(...objects: Partial<T>[]): T {
		const result = {} as T;
		
		for (const obj of objects) {
			if (typeOf(obj) === "table") {
				for (const [key, value] of pairs(obj as Record<string, unknown>)) {
					(result as Record<string, unknown>)[key] = value;
				}
			}
		}

		return result;
	}

	/**
	 * Deep merge objects (recursive)
	 */
	static deepMerge<T>(...objects: Partial<T>[]): T {
		const result = {} as T;

		for (const obj of objects) {
			if (typeOf(obj) === "table") {
				for (const [key, value] of pairs(obj as Record<string, unknown>)) {
					const currentValue = (result as Record<string, unknown>)[key];
					
					if (typeOf(currentValue) === "table" && typeOf(value) === "table") {
						(result as Record<string, unknown>)[key] = this.deepMerge(
							currentValue as Record<string, unknown>,
							value as Record<string, unknown>
						);
					} else {
						(result as Record<string, unknown>)[key] = value;
					}
				}
			}
		}

		return result;
	}

	/**
	 * Get value from nested object using dot notation
	 */
	static get(obj: Record<string, unknown>, path: string): unknown {
		const keys = path.split(".");
		let current = obj;

		for (const key of keys) {
			if (typeOf(current) !== "table" || current[key] === undefined) {
				return undefined;
			}
			current = current[key] as Record<string, unknown>;
		}

		return current;
	}

	/**
	 * Set value in nested object using dot notation
	 */
	static set(obj: Record<string, unknown>, path: string, value: unknown): void {
		const keys = path.split(".");
		let current = obj;

		for (let i = 0; i < keys.size() - 1; i++) {
			const key = keys[i];
			
			if (typeOf(current[key]) !== "table") {
				current[key] = {};
			}
			
			current = current[key] as Record<string, unknown>;
		}

		current[keys[keys.size() - 1]] = value;
	}

	/**
	 * Check if object has a property (including nested)
	 */
	static has(obj: Record<string, unknown>, path: string): boolean {
		return this.get(obj, path) !== undefined;
	}

	/**
	 * Remove property from object using dot notation
	 */
	static remove(obj: Record<string, unknown>, path: string): boolean {
		const keys = path.split(".");
		let current = obj;

		// Navigate to parent object
		for (let i = 0; i < keys.size() - 1; i++) {
			const key = keys[i];
			if (typeOf(current[key]) !== "table") {
				return false; // Path doesn't exist
			}
			current = current[key] as Record<string, unknown>;
		}

		const lastKey = keys[keys.size() - 1];
		if (current[lastKey] !== undefined) {
			current[lastKey] = undefined;
			return true;
		}

		return false;
	}

	/**
	 * Get all keys from an object
	 */
	static keys(obj: Record<string, unknown>): string[] {
		const keys: string[] = [];
		for (const [key] of pairs(obj)) {
			keys.push(key);
		}
		return keys;
	}

	/**
	 * Get all values from an object
	 */
	static values<T extends defined>(obj: Record<string, T>): T[] {
		const values: T[] = [];
		for (const [, value] of pairs(obj)) {
			values.push(value);
		}
		return values;
	}

	/**
	 * Get all key-value pairs from an object
	 */
	static entries<T>(obj: Record<string, T>): Array<[string, T]> {
		const entries: Array<[string, T]> = [];
		for (const [key, value] of pairs(obj)) {
			entries.push([key, value]);
		}
		return entries;
	}

	/**
	 * Count number of properties in object
	 */
	static size(obj: Record<string, unknown>): number {
		let count = 0;
		for (const [key] of pairs(obj)) {
			count++;
		}
		return count;
	}

	/**
	 * Check if object is empty
	 */
	static isEmpty(obj: Record<string, unknown>): boolean {
		for (const [key] of pairs(obj)) {
			return false; // Has at least one property
		}
		return true;
	}

	/**
	 * Filter object properties
	 */
	static filter<T>(
		obj: Record<string, T>, 
		predicate: (value: T, key: string) => boolean
	): Record<string, T> {
		const result: Record<string, T> = {};
		
		for (const [key, value] of pairs(obj)) {
			if (predicate(value, key)) {
				result[key] = value;
			}
		}

		return result;
	}

	/**
	 * Map object values
	 */
	static map<T, U>(
		obj: Record<string, T>, 
		mapper: (value: T, key: string) => U
	): Record<string, U> {
		const result: Record<string, U> = {};
		
		for (const [key, value] of pairs(obj)) {
			result[key] = mapper(value, key);
		}

		return result;
	}

	/**
	 * Reduce object to a single value
	 */
	static reduce<T, U>(
		obj: Record<string, T>,
		reducer: (accumulator: U, value: T, key: string) => U,
		initialValue: U
	): U {
		let accumulator = initialValue;
		
		for (const [key, value] of pairs(obj)) {
			accumulator = reducer(accumulator, value, key);
		}

		return accumulator;
	}

	/**
	 * Find first value that matches predicate
	 */
	static find<T>(
		obj: Record<string, T>,
		predicate: (value: T, key: string) => boolean
	): T | undefined {
		for (const [key, value] of pairs(obj)) {
			if (predicate(value, key)) {
				return value;
			}
		}
		return undefined;
	}

	/**
	 * Find key of first value that matches predicate
	 */
	static findKey<T>(
		obj: Record<string, T>,
		predicate: (value: T, key: string) => boolean
	): string | undefined {
		for (const [key, value] of pairs(obj)) {
			if (predicate(value, key)) {
				return key;
			}
		}
		return undefined;
	}

	/**
	 * Check if any value matches predicate
	 */
	static some<T>(
		obj: Record<string, T>,
		predicate: (value: T, key: string) => boolean
	): boolean {
		for (const [key, value] of pairs(obj)) {
			if (predicate(value, key)) {
				return true;
			}
		}
		return false;
	}

	/**
	 * Check if all values match predicate
	 */
	static every<T>(
		obj: Record<string, T>,
		predicate: (value: T, key: string) => boolean
	): boolean {
		for (const [key, value] of pairs(obj)) {
			if (!predicate(value, key)) {
				return false;
			}
		}
		return true;
	}

	/**
	 * Group array of objects by a property
	 */
	static groupBy<T extends defined>(
		array: T[],
		keySelector: (item: T) => string
	): Record<string, T[]> {
		const groups: Record<string, T[]> = {};
		
		for (const item of array) {
			const key = keySelector(item);
			if (!groups[key]) {
				groups[key] = [];
			}
			groups[key].push(item);
		}

		return groups;
	}

	/**
	 * Create object from array of key-value pairs
	 */
	static fromEntries<T>(entries: Array<[string, T]>): Record<string, T> {
		const result: Record<string, T> = {};
		
		for (const [key, value] of entries) {
			result[key] = value;
		}

		return result;
	}

	/**
	 * Pick specific properties from object
	 */
	static pick<T, K extends keyof T>(obj: T, keys: K[]): Pick<T, K> {
		const result = {} as Pick<T, K>;
		
		for (const key of keys) {
			if (obj[key] !== undefined) {
				result[key] = obj[key];
			}
		}

		return result;
	}

	/**
	 * Omit specific properties from object
	 */
	static omit<T, K extends keyof T>(obj: T, keys: K[]): Omit<T, K> {
		const result = this.shallowCopy(obj) as unknown as Omit<T, K>;
		
		for (const key of keys) {
			delete (result as Record<string, unknown>)[key as string];
		}

		return result;
	}

	/**
	 * Invert object (swap keys and values)
	 */
	static invert(obj: Record<string, string | number>): Record<string, string> {
		const result: Record<string, string> = {};
		
		for (const [key, value] of pairs(obj)) {
			result[tostring(value)] = key;
		}

		return result;
	}

	/**
	 * Flatten nested object with dot notation keys
	 */
	static flatten(obj: Record<string, unknown>, prefix = ""): Record<string, unknown> {
		const result: Record<string, unknown> = {};
		
		for (const [key, value] of pairs(obj)) {
			const newKey = prefix ? `${prefix}.${key}` : key;
			
			if (typeOf(value) === "table" && value !== undefined) {
				const flattened = this.flatten(value as Record<string, unknown>, newKey);
				for (const [flatKey, flatValue] of pairs(flattened)) {
					result[flatKey] = flatValue;
				}
			} else {
				result[newKey] = value;
			}
		}

		return result;
	}

	/**
	 * Unflatten object with dot notation keys
	 */
	static unflatten(obj: Record<string, unknown>): Record<string, unknown> {
		const result: Record<string, unknown> = {};
		
		for (const [key, value] of pairs(obj)) {
			this.set(result, key, value);
		}

		return result;
	}

	/**
	 * Deep equal comparison
	 */
	static deepEqual(a: unknown, b: unknown): boolean {
		if (a === b) return true;
		
		if (typeOf(a) !== typeOf(b)) return false;
		
		if (typeOf(a) !== "table") return false;
		
		const objA = a as Record<string, unknown>;
		const objB = b as Record<string, unknown>;
		
		const keysA = this.keys(objA);
		const keysB = this.keys(objB);
		
		if (keysA.size() !== keysB.size()) return false;
		
		for (const key of keysA) {
			if (!this.deepEqual(objA[key], objB[key])) {
				return false;
			}
		}
		
		return true;
	}

	/**
	 * Serialize object to JSON-like string (for debugging)
	 */
	static serialize(obj: unknown, indent = 0): string {
		const indentStr = " ".rep(indent);
		
		if (typeOf(obj) === "string") {
			return `"${obj}"`;
		} else if (typeOf(obj) === "number" || typeOf(obj) === "boolean") {
			return tostring(obj);
		} else if (obj === undefined) {
			return "null";
		} else if (typeOf(obj) === "table") {
			const objTable = obj as Record<string, unknown>;
			const entries: string[] = [];
			
			for (const [key, value] of pairs(objTable)) {
				const serializedValue = this.serialize(value, indent + 2);
				entries.push(`${indentStr}  "${key}": ${serializedValue}`);
			}
			
			if (entries.size() === 0) {
				return "{}";
			}
			
			return `{\n${entries.join(",\n")}\n${indentStr}}`;
		} else {
			return `"${tostring(obj)}"`;
		}
	}
}