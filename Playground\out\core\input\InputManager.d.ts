import { BaseService } from "../foundation/BaseService";
import { Result } from "../foundation/types/Result";
import { Error } from "../foundation/types/BrandedTypes";
export declare enum InputType {
    KeyPress = "KeyPress",
    KeyRelease = "KeyRelease",
    KeyHold = "KeyHold",
    MouseClick = "MouseClick",
    MouseMove = "MouseMove",
    TouchStart = "TouchStart",
    TouchEnd = "TouchEnd",
    GamepadInput = "GamepadInput"
}
export declare enum InputContext {
    Global = "Global",
    UI = "UI",
    Movement = "Movement",
    Combat = "Combat",
    Camera = "Camera",
    Custom = "Custom"
}
interface InputBinding {
    id: string;
    context: InputContext;
    keys: Enum.KeyCode[];
    gamepadButtons?: Enum.KeyCode[];
    touchGestures?: string[];
    description: string;
    callback: (actionName: string, state: Enum.UserInputState, input: InputObject) => void;
    priority?: number;
    enabled: boolean;
}
interface InputConfiguration {
    enableMouse: boolean;
    enableKeyboard: boolean;
    enableTouch: boolean;
    enableGamepad: boolean;
    mouseSensitivity: number;
    gamepadDeadzone: number;
    touchSensitivity: number;
    holdThreshold: number;
}
interface InputState {
    keysPressed: Set<Enum.KeyCode>;
    mousePosition: Vector2;
    mouseDelta: Vector2;
    gamepadSticks: Record<string, Vector3>;
    touchPositions: Record<number, Vector2>;
    isInputEnabled: boolean;
}
interface InputStats {
    totalInputs: number;
    keyInputs: number;
    mouseInputs: number;
    touchInputs: number;
    gamepadInputs: number;
    averageInputsPerSecond: number;
    lastInputTime: number;
}
/**
 * Comprehensive input management system supporting keyboard, mouse, touch, and gamepad
 * Provides customizable input bindings, contexts, and input state tracking
 */
export declare class InputManager extends BaseService {
    private static instance;
    private bindings;
    private contextStacks;
    private activeContext;
    private config;
    private inputState;
    private stats;
    private holdTimers;
    private inputConnections;
    private lastStatsUpdate;
    constructor();
    static getInstance(): InputManager;
    protected onInitialize(): Promise<Result<void, Error>>;
    protected onShutdown(): Promise<Result<void, Error>>;
    /**
     * Register an input binding
     */
    registerBinding(binding: Omit<InputBinding, "enabled">): Result<void, Error>;
    /**
     * Unregister an input binding
     */
    unregisterBinding(id: string): boolean;
    /**
     * Enable or disable a binding
     */
    setBindingEnabled(id: string, enabled: boolean): boolean;
    /**
     * Set the active input context
     */
    setContext(context: InputContext): void;
    /**
     * Push a context onto the stack (for temporary context changes)
     */
    pushContext(context: InputContext, id: string): void;
    /**
     * Pop a context from the stack
     */
    popContext(context: InputContext, id: string): void;
    /**
     * Get current input state
     */
    getInputState(): InputState;
    /**
     * Check if a key is currently pressed
     */
    isKeyPressed(key: Enum.KeyCode): boolean;
    /**
     * Check if any of the keys are pressed
     */
    isAnyKeyPressed(keys: Enum.KeyCode[]): boolean;
    /**
     * Get mouse position
     */
    getMousePosition(): Vector2;
    /**
     * Get mouse delta (movement since last frame)
     */
    getMouseDelta(): Vector2;
    /**
     * Get gamepad stick position
     */
    getGamepadStick(stick: "LeftStick" | "RightStick"): Vector3;
    /**
     * Enable or disable all input processing
     */
    setInputEnabled(enabled: boolean): void;
    /**
     * Update input configuration
     */
    updateConfig(newConfig: Partial<InputConfiguration>): void;
    /**
     * Get input statistics
     */
    getStats(): InputStats;
    /**
     * Reset input statistics
     */
    resetStats(): void;
    private setupInputConnections;
    private cleanup;
    private handleInputBegan;
    private handleInputEnded;
    private handleInputChanged;
    private handleTouchStarted;
    private handleTouchEnded;
    private updateGamepadState;
    private updateHoldDetection;
    private triggerHoldBindings;
    private updateInputStats;
    private updateStats;
    private registerContextAction;
    private shouldProcessBinding;
    private updateContextBindings;
    private setupDefaultBindings;
    private getTouchPointCount;
    /**
     * Get a comprehensive input report
     */
    getReport(): string;
}
export declare const Input: {
    getInstance: () => InputManager;
    registerBinding: (binding: Omit<InputBinding, "enabled">) => Result<void, Error>;
    setContext: (context: InputContext) => void;
    isKeyPressed: (key: Enum.KeyCode) => boolean;
    getMousePosition: () => Vector2;
    getReport: () => string;
};
export type { InputBinding, InputConfiguration, InputState, InputStats };
