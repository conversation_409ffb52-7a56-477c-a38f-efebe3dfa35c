import { BaseService } from "../foundation/BaseService";
import { Result } from "../foundation/types/Result";
import { Error } from "../foundation/types/BrandedTypes";
export declare enum CameraMode {
    ThirdPerson = "ThirdPerson",
    FirstPerson = "FirstPerson",
    Fixed = "Fixed",
    Cinematic = "Cinematic",
    Orbital = "Orbital",
    Free = "Free",
    Follow = "Follow"
}
export interface CameraConfiguration {
    defaultMode: CameraMode;
    sensitivity: number;
    smoothing: number;
    maxDistance: number;
    minDistance: number;
    maxAngle: number;
    minAngle: number;
    enableCollision: boolean;
    enableSmoothing: boolean;
    enableInput: boolean;
    fieldOfView: number;
}
export interface CameraState {
    mode: CameraMode;
    position: Vector3;
    lookAt: Vector3;
    distance: number;
    angleX: number;
    angleY: number;
    fieldOfView: number;
    isTransitioning: boolean;
}
export interface CameraTransition {
    duration: number;
    easingStyle: Enum.EasingStyle;
    easingDirection: Enum.EasingDirection;
    onComplete?: () => void;
}
/**
 * Advanced camera management system with multiple camera modes and smooth transitions
 */
export declare class CameraManager extends BaseService {
    private static instance;
    private camera;
    private config;
    private state;
    private target;
    private offset;
    private inputConnections;
    private renderConnection?;
    private activeTween?;
    private lastMousePosition;
    private isDragging;
    private savedCameraStates;
    constructor();
    static getInstance(): CameraManager;
    protected onInitialize(): Promise<Result<void, Error>>;
    protected onShutdown(): Promise<Result<void, Error>>;
    /**
     * Set camera mode
     */
    setCameraMode(mode: CameraMode, transition?: CameraTransition): void;
    /**
     * Set camera target (for follow modes)
     */
    setTarget(target: BasePart | undefined, offset?: Vector3): void;
    /**
     * Set camera position directly
     */
    setPosition(position: Vector3, lookAt?: Vector3, transition?: CameraTransition): void;
    /**
     * Set camera distance (for orbital/third-person modes)
     */
    setDistance(distance: number, transition?: CameraTransition): void;
    /**
     * Set camera angles (for orbital modes)
     */
    setAngles(angleX: number, angleY: number, transition?: CameraTransition): void;
    /**
     * Set field of view
     */
    setFieldOfView(fov: number, transition?: CameraTransition): void;
    /**
     * Save current camera state
     */
    saveCameraState(name: string): void;
    /**
     * Restore saved camera state
     */
    restoreCameraState(name: string, transition?: CameraTransition): boolean;
    /**
     * Shake the camera
     */
    shake(intensity: number, duration: number): void;
    /**
     * Smooth look at target
     */
    lookAt(target: Vector3, transition?: CameraTransition): void;
    /**
     * Get current camera state
     */
    getCameraState(): CameraState;
    /**
     * Update camera configuration
     */
    updateConfig(newConfig: Partial<CameraConfiguration>): void;
    /**
     * Enable or disable camera input
     */
    setInputEnabled(enabled: boolean): void;
    /**
     * Get camera ray from screen position
     */
    screenPointToRay(screenPosition: Vector2): Ray;
    /**
     * Convert world position to screen position
     */
    worldToScreenPoint(worldPosition: Vector3): [Vector3, boolean];
    private setupInputHandlers;
    private startRenderLoop;
    private cleanup;
    private handleMouseMovement;
    private handleMouseWheel;
    private updateCamera;
    private updateThirdPersonCamera;
    private updateFirstPersonCamera;
    private updateOrbitalCamera;
    private updateFollowCamera;
    private updateFixedCamera;
    private updateFreeCamera;
    private updateCinematicCamera;
    private performCollisionCheck;
    private applyCameraState;
    private applyModeSettings;
    private animateToState;
    private applyEasing;
    /**
     * Get a comprehensive camera report
     */
    getReport(): string;
}
export declare const Camera: {
    getInstance: () => CameraManager;
    setMode: (mode: CameraMode, transition?: CameraTransition) => void;
    setTarget: (target: BasePart | undefined, offset?: Vector3) => void;
    setPosition: (position: Vector3, lookAt?: Vector3, transition?: CameraTransition) => void;
    shake: (intensity: number, duration: number) => void;
    getReport: () => string;
};
