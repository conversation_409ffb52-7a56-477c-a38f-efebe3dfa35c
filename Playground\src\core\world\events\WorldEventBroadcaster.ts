import { Players, RunService, Lighting, SoundService } from "@rbxts/services";
import {
	WorldEventOptions,
	WorldEvent,
	EarthquakeEvent,
	TsunamiEvent,
	MeteorEvent,
	LightningStormEvent,
	EventPhase,
	WorldEventType,
} from "./interfaces/WorldEventOptions";

export class WorldEventBroadcaster {
	private static activeEvents: Map<string, WorldEvent> = new Map();
	private static eventCounter: number = 0;
	private static updateConnection?: RBXScriptConnection;
	private static eventHistory: WorldEvent[] = [];

	/**
	 * Initialize the world event system
	 */
	public static initialize(): void {
		if (this.updateConnection) return;

		this.updateConnection = RunService.Heartbeat.Connect(() => {
			this.updateActiveEvents();
		});

		print("🌍 WorldEventBroadcaster initialized");
	}

	/**
	 * Trigger a world-wide earthquake event (perfect for Whitebeard abilities)
	 */
	public static triggerEarthquake(options: EarthquakeEvent): string {
		this.eventCounter = this.eventCounter + 1;
		const eventId = `earthquake_${this.eventCounter}_${tick()}`;

		const worldEvent: WorldEvent = {
			id: eventId,
			options: options,
			startTime: tick(),
			endTime: options.duration ? tick() + options.duration : undefined,
			status: "active",
			affectedRegions: this.calculateAffectedRegions(options),
			participants: new Set(),
		};

		this.activeEvents.set(eventId, worldEvent);
		this.broadcastEventStart(worldEvent);

		// Execute earthquake phases
		this.executeEarthquakePhases(worldEvent, options);

		print(`🌍 Triggered earthquake event: ${eventId} magnitude ${options.magnitude}`);
		return eventId;
	}

	/**
	 * Trigger a tsunami event
	 */
	public static triggerTsunami(options: TsunamiEvent): string {
		this.eventCounter = this.eventCounter + 1;
		const eventId = `tsunami_${this.eventCounter}_${tick()}`;

		const worldEvent: WorldEvent = {
			id: eventId,
			options: options,
			startTime: tick(),
			endTime: options.duration ? tick() + options.duration : undefined,
			status: "active",
			affectedRegions: this.calculateAffectedRegions(options),
			participants: new Set(),
		};

		this.activeEvents.set(eventId, worldEvent);
		this.broadcastEventStart(worldEvent);
		this.executeTsunamiPhases(worldEvent, options);

		print(`🌊 Triggered tsunami event: ${eventId}`);
		return eventId;
	}

	/**
	 * Trigger a meteor impact event
	 */
	public static triggerMeteorImpact(options: MeteorEvent): string {
		this.eventCounter = this.eventCounter + 1;
		const eventId = `meteor_${this.eventCounter}_${tick()}`;

		const worldEvent: WorldEvent = {
			id: eventId,
			options: options,
			startTime: tick(),
			endTime: options.duration ? tick() + options.duration : undefined,
			status: "active",
			affectedRegions: this.calculateAffectedRegions(options),
			participants: new Set(),
		};

		this.activeEvents.set(eventId, worldEvent);
		this.broadcastEventStart(worldEvent);
		this.executeMeteorPhases(worldEvent, options);

		print(`☄️ Triggered meteor event: ${eventId}`);
		return eventId;
	}

	/**
	 * Trigger a lightning storm event
	 */
	public static triggerLightningStorm(options: LightningStormEvent): string {
		this.eventCounter = this.eventCounter + 1;
		const eventId = `lightning_${this.eventCounter}_${tick()}`;

		const worldEvent: WorldEvent = {
			id: eventId,
			options: options,
			startTime: tick(),
			endTime: options.duration ? tick() + options.duration : undefined,
			status: "active",
			affectedRegions: this.calculateAffectedRegions(options),
			participants: new Set(),
		};

		this.activeEvents.set(eventId, worldEvent);
		this.broadcastEventStart(worldEvent);
		this.executeLightningStormPhases(worldEvent, options);

		print(`⚡ Triggered lightning storm event: ${eventId}`);
		return eventId;
	}

	/**
	 * Execute earthquake event phases
	 */
	private static executeEarthquakePhases(event: WorldEvent, options: EarthquakeEvent): void {
		const phases: EventPhase[] = [
			{
				name: "initial_tremor",
				startTime: 0,
				duration: 2,
				effects: [
					{ type: "physics", data: { intensity: options.magnitude * 0.3 } },
					{ type: "audio", data: { sound: "rumble_low" } },
				],
			},
			{
				name: "main_quake",
				startTime: 2,
				duration: options.duration ? options.duration * 0.6 : 10,
				effects: [
					{ type: "physics", data: { intensity: options.magnitude } },
					{ type: "visual", data: { cracks: true, debris: true } },
					{ type: "audio", data: { sound: "earthquake_main" } },
					{ type: "environmental", data: { lighting: "shake" } },
				],
			},
			{
				name: "aftershock",
				startTime: (options.duration || 15) * 0.8,
				duration: (options.duration || 15) * 0.2,
				effects: [
					{ type: "physics", data: { intensity: options.magnitude * 0.5 } },
					{ type: "audio", data: { sound: "rumble_fade" } },
				],
			},
		];

		this.executeEventPhases(event, phases);
	}

	/**
	 * Execute tsunami event phases
	 */
	private static executeTsunamiPhases(event: WorldEvent, options: TsunamiEvent): void {
		const phases: EventPhase[] = [
			{
				name: "water_recede",
				startTime: 0,
				duration: 5,
				effects: [
					{ type: "environmental", data: { waterLevel: -10 } },
					{ type: "audio", data: { sound: "water_recede" } },
				],
			},
			{
				name: "wave_approach",
				startTime: 5,
				duration: 10,
				effects: [
					{ type: "visual", data: { waveHeight: options.waveHeight } },
					{ type: "audio", data: { sound: "tsunami_roar" } },
				],
			},
			{
				name: "impact",
				startTime: 15,
				duration: options.duration ? options.duration - 15 : 20,
				effects: [
					{ type: "physics", data: { waterForce: options.waveSpeed } },
					{ type: "environmental", data: { flooding: true } },
					{ type: "visual", data: { waterEffects: true } },
				],
			},
		];

		this.executeEventPhases(event, phases);
	}

	/**
	 * Execute meteor impact phases
	 */
	private static executeMeteorPhases(event: WorldEvent, options: MeteorEvent): void {
		const phases: EventPhase[] = [
			{
				name: "meteor_visible",
				startTime: 0,
				duration: 5,
				effects: [
					{ type: "visual", data: { meteorTrail: true, size: options.meteorSize } },
					{ type: "audio", data: { sound: "meteor_whistle" } },
					{ type: "environmental", data: { lighting: "meteor_glow" } },
				],
			},
			{
				name: "impact",
				startTime: 5,
				duration: 1,
				effects: [
					{ type: "physics", data: { explosionForce: options.explosionRadius * 100 } },
					{ type: "visual", data: { explosion: true, crater: true } },
					{ type: "audio", data: { sound: "meteor_impact" } },
					{ type: "environmental", data: { lighting: "flash" } },
				],
			},
			{
				name: "aftermath",
				startTime: 6,
				duration: (options.duration || 30) - 6,
				effects: [
					{ type: "environmental", data: { dust: true, debris: true } },
					{ type: "audio", data: { sound: "aftermath_rumble" } },
				],
			},
		];

		this.executeEventPhases(event, phases);
	}

	/**
	 * Execute lightning storm phases
	 */
	private static executeLightningStormPhases(event: WorldEvent, options: LightningStormEvent): void {
		const phases: EventPhase[] = [
			{
				name: "storm_buildup",
				startTime: 0,
				duration: 10,
				effects: [
					{ type: "environmental", data: { clouds: "dark", wind: "strong" } },
					{ type: "audio", data: { sound: "thunder_distant" } },
				],
			},
			{
				name: "active_storm",
				startTime: 10,
				duration: options.duration ? options.duration - 20 : 30,
				effects: [
					{ type: "visual", data: { lightning: true, frequency: options.strikeInterval } },
					{ type: "audio", data: { sound: "thunder_close" } },
					{ type: "environmental", data: { lighting: "storm" } },
				],
			},
			{
				name: "storm_fade",
				startTime: (options.duration || 50) - 10,
				duration: 10,
				effects: [
					{ type: "environmental", data: { clouds: "clearing" } },
					{ type: "audio", data: { sound: "thunder_fade" } },
				],
			},
		];

		this.executeEventPhases(event, phases);
	}

	/**
	 * Execute event phases with proper timing
	 */
	private static executeEventPhases(event: WorldEvent, phases: EventPhase[]): void {
		for (const phase of phases) {
			task.delay(phase.startTime, () => {
				if (this.activeEvents.has(event.id)) {
					this.executePhase(event, phase);
				}
			});
		}
	}

	/**
	 * Execute a single event phase
	 */
	private static executePhase(event: WorldEvent, phase: EventPhase): void {
		print(`🌍 Executing phase: ${phase.name} for event ${event.id}`);

		for (const effect of phase.effects) {
			const delay = effect.delay || 0;
			task.delay(delay, () => {
				this.applyEventEffect(event, effect);
			});
		}
	}

	/**
	 * Apply a specific event effect
	 */
	private static applyEventEffect(event: WorldEvent, effect: unknown): void {
		if (!effect || !typeIs(effect, "table")) return;

		const effectObj = effect as Record<string, unknown>;
		const effectType = effectObj.type;
		const effectData = effectObj.data;

		switch (effectType) {
			case "environmental":
				this.applyEnvironmentalEffect(event, effectData);
				break;
			case "visual":
				this.applyVisualEffect(event, effectData);
				break;
			case "audio":
				this.applyAudioEffect(event, effectData);
				break;
			case "physics":
				this.applyPhysicsEffect(event, effectData);
				break;
		}
	}

	/**
	 * Apply environmental effects (lighting, weather, etc.)
	 */
	private static applyEnvironmentalEffect(event: WorldEvent, data: unknown): void {
		if (!data || !typeIs(data, "table")) return;

		const dataObj = data as Record<string, unknown>;

		if (dataObj.lighting === "shake") {
			// Shake the lighting
			const originalBrightness = Lighting.Brightness;
			for (let i = 0; i < 10; i++) {
				task.delay(i * 0.1, () => {
					Lighting.Brightness = originalBrightness + (math.random() - 0.5) * 0.5;
				});
			}
			task.delay(1, () => {
				Lighting.Brightness = originalBrightness;
			});
		}

		if (dataObj.lighting === "flash") {
			const originalBrightness = Lighting.Brightness;
			Lighting.Brightness = 5;
			task.delay(0.1, () => {
				Lighting.Brightness = originalBrightness;
			});
		}
	}

	/**
	 * Apply visual effects
	 */
	private static applyVisualEffect(event: WorldEvent, data: unknown): void {
		// Implementation would create particle effects, explosions, etc.
		print(`✨ Applying visual effect: ${tostring(data)}`);
	}

	/**
	 * Apply audio effects
	 */
	private static applyAudioEffect(event: WorldEvent, data: unknown): void {
		if (!data || !typeIs(data, "table")) return;

		const dataObj = data as Record<string, unknown>;
		const sound = dataObj.sound;

		// Implementation would play sounds
		print(`🔊 Playing sound: ${tostring(sound)}`);
	}

	/**
	 * Apply physics effects
	 */
	private static applyPhysicsEffect(event: WorldEvent, data: unknown): void {
		if (!data || !typeIs(data, "table")) return;

		const dataObj = data as Record<string, unknown>;
		const intensity = dataObj.intensity;

		// Implementation would apply forces, shake objects, etc.
		print(`⚡ Applying physics effect with intensity: ${tostring(intensity)}`);
	}

	/**
	 * Calculate affected regions for an event
	 */
	private static calculateAffectedRegions(options: WorldEventOptions): Region3[] {
		const regions: Region3[] = [];

		if (options.center && options.radius) {
			const region = new Region3(
				options.center.sub(new Vector3(options.radius, options.radius, options.radius)),
				options.center.add(new Vector3(options.radius, options.radius, options.radius)),
			);
			regions.push(region);
		}

		return regions;
	}

	/**
	 * Broadcast event start to all players
	 */
	private static broadcastEventStart(event: WorldEvent): void {
		const players = this.getAffectedPlayers(event);

		for (const player of players) {
			// Send event notification to client
			// Implementation would use RemoteEvents
			print(`📢 Notifying ${player.Name} of ${event.options.eventType} event`);
		}
	}

	/**
	 * Get players affected by an event
	 */
	private static getAffectedPlayers(event: WorldEvent): Player[] {
		const options = event.options;

		if (options.affectedPlayers === "all") {
			return Players.GetPlayers();
		} else if (options.affectedPlayers === "specific" && options.specificPlayers) {
			return options.specificPlayers;
		} else if (options.affectedPlayers === "nearby" && options.center && options.radius) {
			return this.getPlayersInRadius(options.center, options.radius);
		}

		return [];
	}

	/**
	 * Get players within a radius
	 */
	private static getPlayersInRadius(center: Vector3, radius: number): Player[] {
		const playersInRadius: Player[] = [];

		for (const player of Players.GetPlayers()) {
			if (player.Character) {
				const humanoidRootPart = player.Character.FindFirstChild("HumanoidRootPart") as Part;
				if (humanoidRootPart) {
					const distance = center.sub(humanoidRootPart.Position).Magnitude;
					if (distance <= radius) {
						playersInRadius.push(player);
					}
				}
			}
		}

		return playersInRadius;
	}

	/**
	 * Update all active events
	 */
	private static updateActiveEvents(): void {
		const currentTime = tick();

		for (const [eventId, event] of this.activeEvents) {
			if (event.endTime && currentTime >= event.endTime) {
				this.completeEvent(eventId);
			}
		}
	}

	/**
	 * Complete an event
	 */
	private static completeEvent(eventId: string): void {
		const event = this.activeEvents.get(eventId);
		if (!event) return;

		event.status = "completed";
		this.eventHistory.push(event);
		this.activeEvents.delete(eventId);

		print(`🌍 Completed world event: ${eventId}`);
	}

	/**
	 * Cancel an active event
	 */
	public static cancelEvent(eventId: string): void {
		const event = this.activeEvents.get(eventId);
		if (!event) return;

		event.status = "cancelled";
		this.activeEvents.delete(eventId);

		print(`🌍 Cancelled world event: ${eventId}`);
	}

	/**
	 * Get all active events
	 */
	public static getActiveEvents(): Map<string, WorldEvent> {
		return this.activeEvents;
	}

	/**
	 * Get event history
	 */
	public static getEventHistory(): WorldEvent[] {
		return this.eventHistory;
	}

	/**
	 * Shutdown the world event system
	 */
	public static shutdown(): void {
		if (this.updateConnection) {
			this.updateConnection.Disconnect();
			this.updateConnection = undefined;
		}

		// Cancel all active events
		const eventIds: string[] = [];
		for (const [eventId] of this.activeEvents) {
			eventIds.push(eventId);
		}
		for (const eventId of eventIds) {
			this.cancelEvent(eventId);
		}

		print("🌍 WorldEventBroadcaster shutdown");
	}
}
