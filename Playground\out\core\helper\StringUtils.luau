-- Compiled with roblox-ts v3.0.0
--[[
	*
	 * String manipulation utilities for game development
	 
]]
local StringUtils
do
	StringUtils = setmetatable({}, {
		__tostring = function()
			return "StringUtils"
		end,
	})
	StringUtils.__index = StringUtils
	function StringUtils.new(...)
		local self = setmetatable({}, StringUtils)
		return self:constructor(...) or self
	end
	function StringUtils:constructor()
	end
	function StringUtils:formatTime(seconds)
		local minutes = math.floor(seconds / 60)
		local remainingSeconds = math.floor(seconds % 60)
		return `{string.format("%02d", minutes)}:{string.format("%02d", remainingSeconds)}`
	end
	function StringUtils:formatTimeHours(seconds)
		local hours = math.floor(seconds / 3600)
		local minutes = math.floor((seconds % 3600) / 60)
		local remainingSeconds = math.floor(seconds % 60)
		return `{string.format("%02d", hours)}:{string.format("%02d", minutes)}:{string.format("%02d", remainingSeconds)}`
	end
	function StringUtils:formatNumber(num)
		if num >= 1000000000 then
			return string.format("%.1fB", num / 1000000000)
		elseif num >= 1000000 then
			return string.format("%.1fM", num / 1000000)
		elseif num >= 1000 then
			return string.format("%.1fK", num / 1000)
		else
			return tostring(num)
		end
	end
	function StringUtils:formatCurrency(amount, currency)
		if currency == nil then
			currency = "$"
		end
		local formatted = self:addCommas(amount)
		return `{currency}{formatted}`
	end
	function StringUtils:addCommas(num)
		local str = tostring(math.floor(num))
		local parts = {}
		for i = #str, 1, -3 do
			local start = math.max(1, i - 2)
			local _i = i
			local part = string.sub(str, start, _i)
			table.insert(parts, 1, part)
		end
		return table.concat(parts, ",")
	end
	function StringUtils:titleCase(str)
		return (string.gsub(str, "%w+", function(word)
			return string.upper(string.sub(word, 1, 1)) .. string.lower(string.sub(word, 2))
		end))
	end
	function StringUtils:toCamelCase(str)
		return (string.gsub(str, "[-_%s]+(.)", function(letter)
			return string.upper(letter)
		end))
	end
	function StringUtils:toSnakeCase(str)
		return string.lower((string.gsub(str, "([a-z])([A-Z])", "%1_%2")))
	end
	function StringUtils:toKebabCase(str)
		return string.lower((string.gsub(str, "([a-z])([A-Z])", "%1-%2")))
	end
	function StringUtils:truncate(str, maxLength, suffix)
		if suffix == nil then
			suffix = "..."
		end
		if #str <= maxLength then
			return str
		end
		local _str = str
		local _arg1 = maxLength - #suffix
		return string.sub(_str, 1, _arg1) .. suffix
	end
	function StringUtils:pad(str, length, char, left)
		if char == nil then
			char = " "
		end
		if left == nil then
			left = true
		end
		local _char = char
		local _arg0 = math.max(0, length - #str)
		local padding = string.rep(_char, _arg0)
		return if left then padding .. str else str .. padding
	end
	function StringUtils:removeWhitespace(str)
		return (string.gsub(str, "%s+", ""))
	end
	function StringUtils:countOccurrences(str, pattern)
		local count = 0
		local pos = 1
		while true do
			local _str = str
			local _pattern = pattern
			local _pos = pos
			local found = { string.find(_str, _pattern, _pos) }
			local _value = found[1]
			if not (_value ~= 0 and _value == _value and _value) then
				break
			end
			count += 1
			pos = found[1] + 1
		end
		return count
	end
	function StringUtils:randomString(length, charset)
		if charset == nil then
			charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
		end
		local result = ""
		do
			local i = 0
			local _shouldIncrement = false
			while true do
				if _shouldIncrement then
					i += 1
				else
					_shouldIncrement = true
				end
				if not (i < length) then
					break
				end
				local randomIndex = math.random(1, #charset)
				result ..= string.sub(charset, randomIndex, randomIndex)
			end
		end
		return result
	end
	function StringUtils:generateId()
		local chars = "0123456789abcdef"
		local result = ""
		for i = 0, 31 do
			if i == 8 or i == 12 or i == 16 or i == 20 then
				result ..= "-"
			end
			local randomIndex = math.random(1, #chars)
			result ..= string.sub(chars, randomIndex, randomIndex)
		end
		return result
	end
	function StringUtils:rgbToHex(r, g, b)
		local toHex = function(n)
			local hex = string.format("%x", math.clamp(math.floor(n), 0, 255))
			return if #hex == 1 then "0" .. hex else hex
		end
		return "#" .. toHex(r) .. toHex(g) .. toHex(b)
	end
	function StringUtils:color3ToHex(color)
		return self:rgbToHex(color.R * 255, color.G * 255, color.B * 255)
	end
	function StringUtils:isValidEmail(email)
		local pattern = "^[%w%._%+%-]+@[%w%.%-]+%.%w+$"
		return (string.match(email, pattern)) ~= nil
	end
	function StringUtils:escapePattern(str)
		return (string.gsub(str, "([%^%$%(%)%%%.%[%]%*%+%-%?])", "%%%1"))
	end
	function StringUtils:split(str, delimiter)
		local parts = {}
		local pattern = `([^{self:escapePattern(delimiter)}]+)`
		for match in string.gmatch(str, pattern) do
			if typeof(match) == "string" then
				table.insert(parts, match)
			end
		end
		return parts
	end
	function StringUtils:join(parts, delimiter)
		if #parts == 0 then
			return ""
		end
		if #parts == 1 then
			return parts[1]
		end
		local result = parts[1]
		for i = 1, #parts - 1 do
			result ..= delimiter .. parts[i + 1]
		end
		return result
	end
	function StringUtils:startsWith(str, prefix)
		local _str = str
		local _arg1 = #prefix
		return string.sub(_str, 1, _arg1) == prefix
	end
	function StringUtils:endsWith(str, suffix)
		local _str = str
		local _arg0 = -#suffix
		return string.sub(_str, _arg0) == suffix
	end
	function StringUtils:formatPlayerName(player)
		local displayName = player.DisplayName
		local username = player.Name
		if displayName == username then
			return username
		else
			return `{displayName} (@{username})`
		end
	end
	function StringUtils:markdownToRichText(text)
		-- Bold: **text** -> <b>text</b>
		text = (string.gsub(text, "%*%*(.-)%*%*", "<b>%1</b>"))
		-- Italic: *text* -> <i>text</i>
		text = (string.gsub(text, "%*(.-)%*", "<i>%1</i>"))
		-- Code: `text` -> <font face="SourceCodePro">text</font>
		text = (string.gsub(text, "`(.-)`", '<font family="SourceCodePro">%1</font>'))
		return text
	end
	function StringUtils:getInitials(name, maxInitials)
		if maxInitials == nil then
			maxInitials = 2
		end
		local words = self:split(name, " ")
		local initials = ""
		do
			local i = 0
			local _shouldIncrement = false
			while true do
				if _shouldIncrement then
					i += 1
				else
					_shouldIncrement = true
				end
				if not (i < math.min(#words, maxInitials)) then
					break
				end
				local word = words[i + 1]
				if #word > 0 then
					initials ..= string.upper(string.sub(word, 1, 1))
				end
			end
		end
		return initials
	end
	function StringUtils:formatBytes(bytes)
		local units = { "B", "KB", "MB", "GB" }
		local value = bytes
		local unitIndex = 0
		while value >= 1024 and unitIndex < #units - 1 do
			value /= 1024
			unitIndex += 1
		end
		if unitIndex == 0 then
			return `{value} {units[unitIndex + 1]}`
		else
			return `{string.format("%.1f", value)} {units[unitIndex + 1]}`
		end
	end
end
return {
	StringUtils = StringUtils,
}
