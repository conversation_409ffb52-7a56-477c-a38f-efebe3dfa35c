import * as React from "@rbxts/react";
import { 
	Modal, 
	Button, 
	Label, 
	VerticalFrame, 
	HorizontalFrame,
	COLORS,
	ToastService,
	ProgressBar,
} from "../../core";
import { ElementalBattleGame, ElementalBattleState } from "../../core/game/ElementalBattleGame";

interface ElementalBattleUIProps {
	isOpen: boolean;
	onClose: () => void;
}

export function ElementalBattleUI({ isOpen, onClose }: ElementalBattleUIProps): React.ReactElement {
	const [battleGame] = React.useState(() => new ElementalBattleGame());
	const [gameState, setGameState] = React.useState<ElementalBattleState>(() => battleGame.getGameState());
	const [isLoading, setIsLoading] = React.useState(false);
	const [error, setError] = React.useState<string | undefined>(undefined);

	// Poll for state changes with throttling to improve performance
	React.useEffect(() => {
		let lastUpdate = 0;
		const UPDATE_INTERVAL = 0.1; // Update every 100ms instead of every frame

		const heartbeat = game.GetService("RunService").Heartbeat.Connect(() => {
			const now = tick();
			if (now - lastUpdate >= UPDATE_INTERVAL) {
				setGameState(battleGame.getGameState());
				lastUpdate = now;
			}
		});

		return () => heartbeat.Disconnect();
	}, [battleGame]);

	const handleStartGame = React.useCallback(async () => {
		if (isLoading) return; // Prevent multiple simultaneous operations

		try {
			setIsLoading(true);
			setError(undefined);
			print("⚔️ [ElementalBattleUI] Starting Elemental Battle...");

			// Validate player character exists
			const player = game.GetService("Players").LocalPlayer;
			if (!player.Character) {
				throw "Player character not found. Please respawn and try again.";
			}

			const result = await battleGame.start();

			if (result.isOk()) {
				ToastService.showSuccess("⚔️ Battle Started!", "Prepare for elemental combat!");
			} else {
				const errorResult = result.getError();
				const errorMessage = errorResult.message || "Unknown error occurred";
				setError(errorMessage);
				ToastService.showError("❌ Start Failed", errorMessage);
			}
		} catch (err) {
			const errorMessage = `Failed to start game: ${err}`;
			setError(errorMessage);
			ToastService.showError("❌ Error", errorMessage);
		} finally {
			setIsLoading(false);
		}
	}, [battleGame, isLoading]);

	const handleStopGame = React.useCallback(async () => {
		if (isLoading) return; // Prevent multiple simultaneous operations

		try {
			setIsLoading(true);
			setError(undefined);
			print("⚔️ [ElementalBattleUI] Stopping Elemental Battle...");

			const result = await battleGame.stop();

			if (result.isOk()) {
				ToastService.showSuccess("⚔️ Battle Ended", "Thanks for playing!");
			} else {
				const errorResult = result.getError();
				const errorMessage = errorResult.message || "Unknown error occurred";
				setError(errorMessage);
				ToastService.showError("❌ Stop Failed", errorMessage);
			}
		} catch (err) {
			const errorMessage = `Failed to stop game: ${err}`;
			setError(errorMessage);
			ToastService.showError("❌ Error", errorMessage);
		} finally {
			setIsLoading(false);
		}
	}, [battleGame, isLoading]);

	const handleClose = React.useCallback(() => {
		if (gameState.isGameActive) {
			ToastService.showWarning("⚠️ Game Active", "Stop the game before closing the panel");
			return;
		}
		onClose();
	}, [gameState.isGameActive, onClose]);

	// Calculate health percentage for progress bar with validation
	const healthPercentage = React.useMemo(() => {
		if (!gameState || gameState.maxPlayerHealth <= 0) return 0;
		const percentage = (gameState.playerHealth / gameState.maxPlayerHealth) * 100;
		return math.max(0, math.min(100, percentage)); // Clamp between 0-100
	}, [gameState.playerHealth, gameState.maxPlayerHealth]);

	const experiencePercentage = React.useMemo(() => {
		if (!gameState || gameState.experienceToNextLevel <= 0) return 0;
		const percentage = (gameState.experience / gameState.experienceToNextLevel) * 100;
		return math.max(0, math.min(100, percentage)); // Clamp between 0-100
	}, [gameState.experience, gameState.experienceToNextLevel]);

	// Validate game state to prevent rendering issues
	const isValidGameState = React.useMemo(() => {
		return gameState &&
			   typeof gameState.playerHealth === "number" &&
			   typeof gameState.maxPlayerHealth === "number" &&
			   typeof gameState.score === "number" &&
			   typeof gameState.currentWave === "number" &&
			   gameState.playerHealth >= 0 &&
			   gameState.maxPlayerHealth > 0;
	}, [gameState]);

	// Get phase display text
	const getPhaseDisplayText = () => {
		switch (gameState.gamePhase) {
			case "waiting":
				return "Ready to Start";
			case "playing":
				return `Wave ${gameState.currentWave} - ${gameState.enemiesRemaining} enemies left`;
			case "betweenWaves":
				return `Next wave in ${math.ceil(gameState.timeUntilNextWave)}s`;
			case "ended":
				return "Game Over";
			default:
				return "Unknown";
		}
	};

	return (
		<Modal
			isOpen={isOpen}
			title="⚔️ Elemental Battle Arena"
			width={500}
			height={600}
			onClose={handleClose}
		>
			<VerticalFrame spacing={16} padding={16}>
				{/* Error Display Section */}
				{error && (
					<VerticalFrame spacing={8}>
						<Label
							text="⚠️ Error"
							fontSize={16}
							bold={true}
							textColor={COLORS.error}
						/>
						<Label
							text={error}
							fontSize={12}
							textColor={COLORS.error}
							textWrapped={true}
						/>
					</VerticalFrame>
				)}

				{/* Game Status Section */}
				<VerticalFrame spacing={8}>
					<Label text="🎮 Game Status" fontSize={18} bold={true} />
					
					<HorizontalFrame spacing={12}>
						<Label
							text="Status:"
							fontSize={14}
							textColor={COLORS.text.main}
							bold={true}
						/>
						<Label
							text={getPhaseDisplayText()}
							fontSize={14}
							textColor={gameState.isGameActive ? COLORS.success : COLORS.text.secondary}
						/>
					</HorizontalFrame>

					<HorizontalFrame spacing={16}>
						<Label
							text={`🏆 Score: ${gameState.score}`}
							fontSize={14}
							textColor={COLORS.primary}
							bold={true}
						/>
						<Label
							text={`⭐ Level: ${gameState.playerLevel}`}
							fontSize={14}
							textColor={COLORS.primary}
							bold={true}
						/>
					</HorizontalFrame>
				</VerticalFrame>

				{/* Player Stats Section */}
				{gameState.isGameActive && (
					<VerticalFrame spacing={8}>
						<Label text="⚡ Player Stats" fontSize={16} bold={true} />
						
						{/* Health Bar */}
						<VerticalFrame spacing={4}>
							<HorizontalFrame spacing={8}>
								<Label
									text={`❤️ Health: ${gameState.playerHealth}/${gameState.maxPlayerHealth}`}
									fontSize={12}
								/>
								<Label
									text={`${math.floor(healthPercentage)}%`}
									fontSize={12}
									textColor={healthPercentage > 60 ? COLORS.success :
											  healthPercentage > 30 ? COLORS.warning :
											  COLORS.error}
									bold={true}
								/>
							</HorizontalFrame>

							<ProgressBar
								progress={healthPercentage / 100}
								barColor={healthPercentage > 60 ? Color3.fromHex(COLORS.success) :
										 healthPercentage > 30 ? Color3.fromHex(COLORS.warning) :
										 Color3.fromHex(COLORS.error)}
								backgroundColor={Color3.fromHex(COLORS.bg.secondary)}
								size={new UDim2(1, 0, 0, 24)}
								cornerRadius={new UDim(0, 6)}
							/>
						</VerticalFrame>

						{/* Experience Bar */}
						<VerticalFrame spacing={4}>
							<HorizontalFrame spacing={8}>
								<Label
									text={`✨ Experience: ${gameState.experience}/${gameState.experienceToNextLevel}`}
									fontSize={12}
								/>
								<Label
									text={`${math.floor(experiencePercentage)}%`}
									fontSize={12}
									textColor={COLORS.primary}
									bold={true}
								/>
							</HorizontalFrame>

							<ProgressBar
								progress={experiencePercentage / 100}
								barColor={Color3.fromHex(COLORS.primary)}
								backgroundColor={Color3.fromHex(COLORS.bg.secondary)}
								size={new UDim2(1, 0, 0, 24)}
								cornerRadius={new UDim(0, 6)}
							/>
						</VerticalFrame>
					</VerticalFrame>
				)}

				{/* Battle Info Section */}
				{gameState.isGameActive && (
					<VerticalFrame spacing={8}>
						<Label text="⚔️ Battle Info" fontSize={16} bold={true} />
						
						<Label
							text={`Wave: ${gameState.currentWave} | Enemies: ${gameState.enemiesRemaining}/${gameState.enemiesInWave}`}
							fontSize={14}
						/>

						{gameState.gamePhase === "betweenWaves" && (
							<Label
								text={`⏰ Next wave in ${math.ceil(gameState.timeUntilNextWave)} seconds`}
								fontSize={14}
								textColor={COLORS.primary}
							/>
						)}
					</VerticalFrame>
				)}

				{/* Abilities Section */}
				{gameState.isGameActive && (
					<VerticalFrame spacing={8}>
						<Label text="🔥 Unlocked Abilities" fontSize={16} bold={true} />
						
						<VerticalFrame spacing={4}>
							{gameState.abilitiesUnlocked.map((abilityId, index) => {
								const abilityNames: Record<string, string> = {
									"QUAKE_PUNCH": "👊 Quake Punch",
									"FIRE_FIST": "🔥 Fire Fist",
									"ICE_AGE": "❄️ Ice Age",
									"THREE_SWORD_STYLE": "⚔️ Three Sword Style",
									"ROOM": "🔵 Room",
								};
								
								return (
									<Label
										key={index}
										text={abilityNames[abilityId] || abilityId}
										fontSize={12}
										textColor={COLORS.success}
									/>
								);
							})}
						</VerticalFrame>
					</VerticalFrame>
				)}

				{/* Game Controls */}
				<VerticalFrame spacing={8}>
					<Label text="🎮 Controls" fontSize={16} bold={true} />
					
					{!gameState.isGameActive ? (
						<>
							<Button
								text={isLoading ? "⏳ Starting..." : "⚔️ Start Battle"}
								variant="primary"
								onClick={handleStartGame}
								size={new UDim2(1, 0, 0, 40)}
								disabled={isLoading || !isValidGameState}
							/>

							<Label
								text={isLoading ? "� Initializing battle arena..." : "�💡 Use abilities (1-5 keys) to defeat waves of enemies! Level up to unlock new abilities!"}
								fontSize={12}
								textColor={COLORS.text.secondary}
								textWrapped={true}
							/>
						</>
					) : (
						<>
							<Button
								text={isLoading ? "⏳ Stopping..." : "🛑 Stop Battle"}
								variant="danger"
								onClick={handleStopGame}
								size={new UDim2(1, 0, 0, 40)}
								disabled={isLoading}
							/>

							<Label
								text="⌨️ Press 1-5 to use abilities. Defeat all enemies to advance to the next wave!"
								fontSize={12}
								textColor={COLORS.text.secondary}
								textWrapped={true}
							/>
						</>
					)}

					{gameState.gamePhase === "ended" && gameState.playerHealth <= 0 && (
						<VerticalFrame spacing={8}>
							<Label
								text="💀 Game Over!"
								fontSize={18}
								textColor={COLORS.error}
								bold={true}
							/>
							<Label
								text={`Final Score: ${gameState.score} | Waves: ${gameState.currentWave - 1} | Level: ${gameState.playerLevel}`}
								fontSize={14}
								textWrapped={true}
							/>
						</VerticalFrame>
					)}
				</VerticalFrame>
			</VerticalFrame>
		</Modal>
	);
}