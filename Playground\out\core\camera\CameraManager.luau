-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local _services = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "services")
local Workspace = _services.Workspace
local RunService = _services.RunService
local UserInputService = _services.UserInputService
local BaseService = TS.import(script, game:GetService("ReplicatedStorage"), "core", "foundation", "BaseService").BaseService
local Result = TS.import(script, game:GetService("ReplicatedStorage"), "core", "foundation", "types", "Result").Result
local createError = TS.import(script, game:GetService("ReplicatedStorage"), "core", "foundation", "types", "BrandedTypes").createError
local CameraMode = {
	ThirdPerson = "ThirdPerson",
	FirstPerson = "FirstPerson",
	Fixed = "Fixed",
	Cinematic = "Cinematic",
	Orbital = "Orbital",
	Free = "Free",
	Follow = "Follow",
}
--[[
	*
	 * Advanced camera management system with multiple camera modes and smooth transitions
	 
]]
local CameraManager
do
	local super = BaseService
	CameraManager = setmetatable({}, {
		__tostring = function()
			return "CameraManager"
		end,
		__index = super,
	})
	CameraManager.__index = CameraManager
	function CameraManager.new(...)
		local self = setmetatable({}, CameraManager)
		return self:constructor(...) or self
	end
	function CameraManager:constructor()
		super.constructor(self, "CameraManager")
		self.offset = Vector3.new(0, 0, 0)
		self.inputConnections = {}
		self.lastMousePosition = Vector2.new(0, 0)
		self.isDragging = false
		self.savedCameraStates = {}
		self.camera = Workspace.CurrentCamera
		self.config = {
			defaultMode = CameraMode.ThirdPerson,
			sensitivity = 0.5,
			smoothing = 0.1,
			maxDistance = 50,
			minDistance = 2,
			maxAngle = 80,
			minAngle = -80,
			enableCollision = true,
			enableSmoothing = true,
			enableInput = true,
			fieldOfView = 70,
		}
		self.state = {
			mode = self.config.defaultMode,
			position = Vector3.new(0, 10, 20),
			lookAt = Vector3.new(0, 0, 0),
			distance = 20,
			angleX = 0,
			angleY = 0,
			fieldOfView = self.config.fieldOfView,
			isTransitioning = false,
		}
	end
	function CameraManager:getInstance()
		if not CameraManager.instance then
			CameraManager.instance = CameraManager.new()
		end
		return CameraManager.instance
	end
	CameraManager.onInitialize = TS.async(function(self)
		local _exitType, _returns = TS.try(function()
			self:setupInputHandlers()
			self:startRenderLoop()
			self:setCameraMode(self.config.defaultMode)
			print("📷 Camera Manager initialized")
			return TS.TRY_RETURN, { Result:ok(nil) }
		end, function(error)
			return TS.TRY_RETURN, { Result:err(createError(`Failed to initialize CameraManager: {error}`)) }
		end)
		if _exitType then
			return unpack(_returns)
		end
	end)
	CameraManager.onShutdown = TS.async(function(self)
		self:cleanup()
		print("📷 Camera Manager shutdown")
		return Result:ok(nil)
	end)
	function CameraManager:setCameraMode(mode, transition)
		if self.state.mode == mode and not transition then
			return nil
		end
		local previousMode = self.state.mode
		self.state.mode = mode
		-- Apply mode-specific settings
		self:applyModeSettings(mode)
		if transition then
			self:animateToState(self.state, transition)
		end
		print(`📷 Camera mode changed: {previousMode} → {mode}`)
	end
	function CameraManager:setTarget(target, offset)
		self.target = target
		if offset then
			self.offset = offset
		end
	end
	function CameraManager:setPosition(position, lookAt, transition)
		local _object = table.clone(self.state)
		setmetatable(_object, nil)
		_object.position = position
		_object.lookAt = lookAt or self.state.lookAt
		local newState = _object
		if transition then
			self:animateToState(newState, transition)
		else
			self.state = newState
			self:applyCameraState()
		end
	end
	function CameraManager:setDistance(distance, transition)
		local clampedDistance = math.clamp(distance, self.config.minDistance, self.config.maxDistance)
		if transition then
			local _object = table.clone(self.state)
			setmetatable(_object, nil)
			_object.distance = clampedDistance
			local newState = _object
			self:animateToState(newState, transition)
		else
			self.state.distance = clampedDistance
		end
	end
	function CameraManager:setAngles(angleX, angleY, transition)
		local clampedAngleY = math.clamp(angleY, self.config.minAngle, self.config.maxAngle)
		if transition then
			local _object = table.clone(self.state)
			setmetatable(_object, nil)
			_object.angleX = angleX % 360
			_object.angleY = clampedAngleY
			local newState = _object
			self:animateToState(newState, transition)
		else
			self.state.angleX = angleX % 360
			self.state.angleY = clampedAngleY
		end
	end
	function CameraManager:setFieldOfView(fov, transition)
		local clampedFov = math.clamp(fov, 1, 120)
		if transition then
			local _object = table.clone(self.state)
			setmetatable(_object, nil)
			_object.fieldOfView = clampedFov
			local newState = _object
			self:animateToState(newState, transition)
		else
			self.state.fieldOfView = clampedFov
			self.camera.FieldOfView = clampedFov
		end
	end
	function CameraManager:saveCameraState(name)
		local _savedCameraStates = self.savedCameraStates
		local _exp = name
		local _object = table.clone(self.state)
		setmetatable(_object, nil)
		_savedCameraStates[_exp] = _object
		print(`📷 Camera state saved: {name}`)
	end
	function CameraManager:restoreCameraState(name, transition)
		local _savedCameraStates = self.savedCameraStates
		local _name = name
		local savedState = _savedCameraStates[_name]
		if not savedState then
			warn(`Camera state '{name}' not found`)
			return false
		end
		if transition then
			self:animateToState(savedState, transition)
		else
			local _object = table.clone(savedState)
			setmetatable(_object, nil)
			self.state = _object
			self:applyCameraState()
		end
		print(`📷 Camera state restored: {name}`)
		return true
	end
	function CameraManager:shake(intensity, duration)
		if self.activeTween then
			self.activeTween:Cancel()
		end
		local originalPosition = self.state.position
		local shakeTime = 0
		local shakeConnection
		shakeConnection = RunService.Heartbeat:Connect(function(deltaTime)
			shakeTime += deltaTime
			if shakeTime >= duration then
				shakeConnection:Disconnect()
				self.state.position = originalPosition
				return nil
			end
			-- Apply random shake offset
			local shakeAmount = intensity * (1 - shakeTime / duration)
			local shakeOffset = Vector3.new((math.random() - 0.5) * shakeAmount, (math.random() - 0.5) * shakeAmount, (math.random() - 0.5) * shakeAmount)
			self.state.position = originalPosition + shakeOffset
		end)
	end
	function CameraManager:lookAt(target, transition)
		local _object = table.clone(self.state)
		setmetatable(_object, nil)
		_object.lookAt = target
		local newState = _object
		if transition then
			self:animateToState(newState, transition)
		else
			self.state.lookAt = target
		end
	end
	function CameraManager:getCameraState()
		local _object = table.clone(self.state)
		setmetatable(_object, nil)
		return _object
	end
	function CameraManager:updateConfig(newConfig)
		local _object = table.clone(self.config)
		setmetatable(_object, nil)
		for _k, _v in newConfig do
			_object[_k] = _v
		end
		self.config = _object
		-- Reapply mode settings if needed
		self:applyModeSettings(self.state.mode)
	end
	function CameraManager:setInputEnabled(enabled)
		self.config.enableInput = enabled
		if enabled then
			self:setupInputHandlers()
		else
			self:cleanup()
		end
	end
	function CameraManager:screenPointToRay(screenPosition)
		local unitRay = self.camera:ScreenPointToRay(screenPosition.X, screenPosition.Y)
		return Ray.new(unitRay.Origin, unitRay.Direction * 1000)
	end
	function CameraManager:worldToScreenPoint(worldPosition)
		return self.camera:WorldToScreenPoint(worldPosition)
	end
	function CameraManager:setupInputHandlers()
		if not RunService:IsClient() or not self.config.enableInput then
			return nil
		end
		self:cleanup()
		-- Mouse input for camera rotation
		local inputConnection = UserInputService.InputChanged:Connect(function(input, gameProcessed)
			if gameProcessed then
				return nil
			end
			if input.UserInputType == Enum.UserInputType.MouseMovement then
				self:handleMouseMovement(input)
			elseif input.UserInputType == Enum.UserInputType.MouseWheel then
				self:handleMouseWheel(input)
			end
		end)
		local inputBeganConnection = UserInputService.InputBegan:Connect(function(input, gameProcessed)
			if gameProcessed then
				return nil
			end
			if input.UserInputType == Enum.UserInputType.MouseButton2 then
				self.isDragging = true
				self.lastMousePosition = Vector2.new(input.Position.X, input.Position.Y)
			end
		end)
		local inputEndedConnection = UserInputService.InputEnded:Connect(function(input, gameProcessed)
			if input.UserInputType == Enum.UserInputType.MouseButton2 then
				self.isDragging = false
			end
		end)
		local _exp = self.inputConnections
		-- ▼ Array.push ▼
		table.insert(_exp, inputConnection)
		table.insert(_exp, inputBeganConnection)
		table.insert(_exp, inputEndedConnection)
		-- ▲ Array.push ▲
	end
	function CameraManager:startRenderLoop()
		self.renderConnection = RunService.RenderStepped:Connect(function()
			self:updateCamera()
		end)
	end
	function CameraManager:cleanup()
		local _exp = self.inputConnections
		-- ▼ ReadonlyArray.forEach ▼
		local _callback = function(connection)
			return connection:Disconnect()
		end
		for _k, _v in _exp do
			_callback(_v, _k - 1, _exp)
		end
		-- ▲ ReadonlyArray.forEach ▲
		self.inputConnections = {}
		if self.renderConnection then
			self.renderConnection:Disconnect()
			self.renderConnection = nil
		end
		if self.activeTween then
			self.activeTween:Cancel()
			self.activeTween = nil
		end
	end
	function CameraManager:handleMouseMovement(input)
		if not self.isDragging then
			return nil
		end
		if self.state.mode ~= CameraMode.Orbital and self.state.mode ~= CameraMode.ThirdPerson then
			return nil
		end
		local currentMousePosition = Vector2.new(input.Position.X, input.Position.Y)
		local _lastMousePosition = self.lastMousePosition
		local mouseDelta = currentMousePosition - _lastMousePosition
		-- Apply sensitivity
		local deltaX = mouseDelta.X * self.config.sensitivity
		local deltaY = mouseDelta.Y * self.config.sensitivity
		-- Update angles
		self.state.angleX -= deltaX
		self.state.angleY = math.clamp(self.state.angleY - deltaY, self.config.minAngle, self.config.maxAngle)
		self.lastMousePosition = currentMousePosition
	end
	function CameraManager:handleMouseWheel(input)
		if self.state.mode ~= CameraMode.Orbital and self.state.mode ~= CameraMode.ThirdPerson then
			return nil
		end
		local zoomDelta = input.Position.Z * 2
		self.state.distance = math.clamp(self.state.distance - zoomDelta, self.config.minDistance, self.config.maxDistance)
	end
	function CameraManager:updateCamera()
		local _exp = self.state.mode
		repeat
			if _exp == (CameraMode.ThirdPerson) then
				self:updateThirdPersonCamera()
				break
			end
			if _exp == (CameraMode.FirstPerson) then
				self:updateFirstPersonCamera()
				break
			end
			if _exp == (CameraMode.Orbital) then
				self:updateOrbitalCamera()
				break
			end
			if _exp == (CameraMode.Follow) then
				self:updateFollowCamera()
				break
			end
			if _exp == (CameraMode.Fixed) then
				self:updateFixedCamera()
				break
			end
			if _exp == (CameraMode.Free) then
				self:updateFreeCamera()
				break
			end
			if _exp == (CameraMode.Cinematic) then
				self:updateCinematicCamera()
				break
			end
		until true
		self:applyCameraState()
	end
	function CameraManager:updateThirdPersonCamera()
		if not self.target then
			return nil
		end
		local _position = self.target.Position
		local _offset = self.offset
		local targetPosition = _position + _offset
		-- Calculate camera position based on angles and distance
		local radX = math.rad(self.state.angleX)
		local radY = math.rad(self.state.angleY)
		local x = math.cos(radY) * math.sin(radX) * self.state.distance
		local y = math.sin(radY) * self.state.distance
		local z = math.cos(radY) * math.cos(radX) * self.state.distance
		local cameraOffset = Vector3.new(x, y, z)
		local desiredPosition = targetPosition + cameraOffset
		-- Collision detection
		if self.config.enableCollision then
			desiredPosition = self:performCollisionCheck(targetPosition, desiredPosition)
		end
		-- Smooth camera movement
		if self.config.enableSmoothing then
			self.state.position = self.state.position:Lerp(desiredPosition, self.config.smoothing)
			self.state.lookAt = self.state.lookAt:Lerp(targetPosition, self.config.smoothing)
		else
			self.state.position = desiredPosition
			self.state.lookAt = targetPosition
		end
	end
	function CameraManager:updateFirstPersonCamera()
		if not self.target then
			return nil
		end
		local _position = self.target.Position
		local _offset = self.offset
		local targetPosition = _position + _offset
		self.state.position = targetPosition
		-- Look direction based on angles
		local radX = math.rad(self.state.angleX)
		local radY = math.rad(self.state.angleY)
		local direction = Vector3.new(math.cos(radY) * math.sin(radX), math.sin(radY), math.cos(radY) * math.cos(radX))
		local _arg0 = direction * 10
		self.state.lookAt = targetPosition + _arg0
	end
	function CameraManager:updateOrbitalCamera()
		-- Similar to third person but orbits around lookAt point
		local radX = math.rad(self.state.angleX)
		local radY = math.rad(self.state.angleY)
		local x = math.cos(radY) * math.sin(radX) * self.state.distance
		local y = math.sin(radY) * self.state.distance
		local z = math.cos(radY) * math.cos(radX) * self.state.distance
		local cameraOffset = Vector3.new(x, y, z)
		self.state.position = self.state.lookAt + cameraOffset
	end
	function CameraManager:updateFollowCamera()
		if not self.target then
			return nil
		end
		local _position = self.target.Position
		local _offset = self.offset
		local targetPosition = _position + _offset
		if self.config.enableSmoothing then
			self.state.position = self.state.position:Lerp(targetPosition, self.config.smoothing)
		else
			self.state.position = targetPosition
		end
	end
	function CameraManager:updateFixedCamera()
		-- Position and lookAt remain as set
	end
	function CameraManager:updateFreeCamera()
		-- Free camera controlled by input
		-- Implementation would depend on specific input handling
	end
	function CameraManager:updateCinematicCamera()
		-- Cinematic camera for cutscenes
		-- Typically controlled by external systems
	end
	function CameraManager:performCollisionCheck(target, desired)
		local _desired = desired
		local _target = target
		local direction = _desired - _target
		local distance = direction.Magnitude
		if distance == 0 then
			return desired
		end
		local ray = Ray.new(target, direction.Unit * distance)
		local raycastParams = RaycastParams.new()
		raycastParams.FilterType = Enum.RaycastFilterType.Blacklist
		raycastParams.FilterDescendantsInstances = {}
		local result = Workspace:Raycast(target, direction, raycastParams)
		if result then
			-- Move camera closer to avoid collision
			local hitDistance = result.Distance - 1
			local _target_1 = target
			local _unit = direction.Unit
			local _arg0 = math.max(hitDistance, self.config.minDistance)
			return _target_1 + (_unit * _arg0)
		end
		return desired
	end
	function CameraManager:applyCameraState()
		self.camera.CameraType = Enum.CameraType.Scriptable
		self.camera.CFrame = CFrame.lookAt(self.state.position, self.state.lookAt)
		self.camera.FieldOfView = self.state.fieldOfView
	end
	function CameraManager:applyModeSettings(mode)
		repeat
			if mode == (CameraMode.FirstPerson) then
				self.state.distance = 0
				break
			end
			if mode == (CameraMode.ThirdPerson) then
				self.state.distance = 20
				break
			end
			if mode == (CameraMode.Orbital) then
				self.state.distance = 30
				break
			end
		until true
	end
	function CameraManager:animateToState(targetState, transition)
		if self.activeTween then
			self.activeTween:Cancel()
		end
		self.state.isTransitioning = true
		-- Simple lerp-based animation instead of TweenService for complex objects
		local startTime = tick()
		local _object = table.clone(self.state)
		setmetatable(_object, nil)
		local startState = _object
		local animationConnection
		animationConnection = RunService.Heartbeat:Connect(function()
			local elapsed = tick() - startTime
			local progress = math.min(elapsed / transition.duration, 1)
			-- Apply easing (simplified)
			local easedProgress = self:applyEasing(progress, transition.easingStyle)
			-- Interpolate between start and target states
			self.state.position = startState.position:Lerp(targetState.position, easedProgress)
			self.state.lookAt = startState.lookAt:Lerp(targetState.lookAt, easedProgress)
			self.state.distance = startState.distance + (targetState.distance - startState.distance) * easedProgress
			self.state.angleX = startState.angleX + (targetState.angleX - startState.angleX) * easedProgress
			self.state.angleY = startState.angleY + (targetState.angleY - startState.angleY) * easedProgress
			self.state.fieldOfView = startState.fieldOfView + (targetState.fieldOfView - startState.fieldOfView) * easedProgress
			if progress >= 1 then
				animationConnection:Disconnect()
				self.state.isTransitioning = false
				local _object_1 = table.clone(targetState)
				setmetatable(_object_1, nil)
				self.state = _object_1
				if transition.onComplete then
					transition.onComplete()
				end
			end
		end)
	end
	function CameraManager:applyEasing(t, style)
		-- Simplified easing functions
		repeat
			if style == (Enum.EasingStyle.Quad) then
				return t * t
			end
			if style == (Enum.EasingStyle.Cubic) then
				return t * t * t
			end
			if style == (Enum.EasingStyle.Sine) then
				return 1 - math.cos(t * math.pi / 2)
			end
			return t
		until true
	end
	function CameraManager:getReport()
		local report = "📷 Camera Manager Report\n"
		report ..= `Mode: {self.state.mode}\n`
		report ..= `Position: ({string.format("%.1f", self.state.position.X)}, {string.format("%.1f", self.state.position.Y)}, {string.format("%.1f", self.state.position.Z)})\n`
		report ..= `Look At: ({string.format("%.1f", self.state.lookAt.X)}, {string.format("%.1f", self.state.lookAt.Y)}, {string.format("%.1f", self.state.lookAt.Z)})\n`
		report ..= `Distance: {string.format("%.1f", self.state.distance)}\n`
		report ..= `Angles: ({string.format("%.1f", self.state.angleX)}°, {string.format("%.1f", self.state.angleY)}°)\n`
		report ..= `FOV: {string.format("%.1f", self.state.fieldOfView)}°\n`
		report ..= `Target: {if self.target then "Set" else "None"}\n`
		report ..= `Input Enabled: {self.config.enableInput}\n`
		report ..= `Smoothing: {self.config.enableSmoothing}\n`
		report ..= `Collision: {self.config.enableCollision}\n`
		-- ▼ ReadonlyMap.size ▼
		local _size = 0
		for _ in self.savedCameraStates do
			_size += 1
		end
		-- ▲ ReadonlyMap.size ▲
		report ..= `Saved States: {_size}\n`
		return report
	end
end
-- Global camera management functions for easy access
local Camera = {
	getInstance = function()
		return CameraManager:getInstance()
	end,
	setMode = function(mode, transition)
		return CameraManager:getInstance():setCameraMode(mode, transition)
	end,
	setTarget = function(target, offset)
		return CameraManager:getInstance():setTarget(target, offset)
	end,
	setPosition = function(position, lookAt, transition)
		return CameraManager:getInstance():setPosition(position, lookAt, transition)
	end,
	shake = function(intensity, duration)
		return CameraManager:getInstance():shake(intensity, duration)
	end,
	getReport = function()
		return CameraManager:getInstance():getReport()
	end,
}
-- Export types for external use
return {
	CameraMode = CameraMode,
	CameraManager = CameraManager,
	Camera = Camera,
}
