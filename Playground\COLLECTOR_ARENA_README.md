## 🎮 Collector Arena Game

**A simple arena-based collection game built with the RoboxGames Core Framework**

### How to Play

1. **Start the Game**: 
   - Click the "🏟️ Game" button in the bottom-left corner
   - Click "🎮 Start Game" in the Collector Arena panel

2. **Objective**: 
   - Collect as many yellow coins as possible
   - Avoid red enemies that chase you
   - Survive for 2 minutes to win

3. **Controls**:
   - Use WASD to move around the arena
   - Movement abilities from the existing playground work in-game
   - Use Q, E, R keys for special movement (dash, launch, speed boost)

4. **Scoring**:
   - Yellow coins: +10 points each
   - Defeating enemies: +25 points each
   - The longer you survive, the higher your score

5. **Difficulty**:
   - Enemies spawn faster over time
   - Enemies move faster as difficulty increases
   - Maximum of 5 enemies and 10 coins on the arena at once

### Features Integrated

✅ **Entity Management**: Coins and enemies are managed through EntityManager
✅ **AI System**: Enemies use AIController for intelligent behavior
✅ **Effects System**: Visual effects for explosions, coin collection, and game events
✅ **Sound System**: Audio feedback for game events
✅ **Mode Switching**: Seamlessly switch between Playground and Game modes
✅ **Core Framework**: Built entirely on top of the existing core systems

### Game Modes

- **Playground Mode**: Access to all testing features and world controls
- **Collector Arena Mode**: Focused game experience with scoring and objectives

### Architecture

The game demonstrates the power of the RoboxGames Core Framework by integrating:
- State management
- Entity spawning and management
- AI behaviors
- Visual and audio effects
- UI components and layout systems
- Mode management

### Future Enhancements

- [ ] Data persistence for high scores
- [ ] Multiple arena maps
- [ ] Power-ups and special abilities
- [ ] Multiplayer support
- [ ] Achievement system