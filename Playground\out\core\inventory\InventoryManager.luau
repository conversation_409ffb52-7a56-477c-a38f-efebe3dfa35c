-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local BaseService = TS.import(script, game:GetService("ReplicatedStorage"), "core", "foundation", "BaseService").BaseService
local Result = TS.import(script, game:GetService("ReplicatedStorage"), "core", "foundation", "types", "Result").Result
local createError = TS.import(script, game:GetService("ReplicatedStorage"), "core", "foundation", "types", "BrandedTypes").createError
local _types = TS.import(script, game:GetService("ReplicatedStorage"), "core", "inventory", "types")
local ItemType = _types.ItemType
local ItemRarity = _types.ItemRarity
--[[
	*
	 * Individual inventory instance for a player or container
	 
]]
local Inventory
do
	Inventory = setmetatable({}, {
		__tostring = function()
			return "Inventory"
		end,
	})
	Inventory.__index = Inventory
	function Inventory.new(...)
		local self = setmetatable({}, Inventory)
		return self:constructor(...) or self
	end
	function Inventory:constructor(config)
		self.slots = {}
		self.transactions = {}
		self.maxTransactionHistory = 100
		self.changeCallbacks = {}
		local _object = table.clone(config)
		setmetatable(_object, nil)
		self.config = _object
		self:initializeSlots()
	end
	function Inventory:addItem(item, quantity)
		if quantity == nil then
			quantity = 1
		end
		if quantity <= 0 then
			return Result:err("Quantity must be positive")
		end
		-- Check weight limit
		if not self:canFitWeight(item.weight * quantity) then
			return Result:err("Not enough weight capacity")
		end
		-- Try to stack with existing items first
		if self.config.autoStack then
			local stackResult = self:tryStackItem(item, quantity)
			if stackResult:isOk() and stackResult:getValue() == 0 then
				self:recordTransaction("add", item.id, quantity)
				self:notifyChange()
				return Result:ok(true)
			end
			quantity = stackResult:getValue()
		end
		-- Find empty slots for remaining quantity
		local emptySlots = self:getEmptySlots()
		local slotsNeeded = math.ceil(quantity / item.maxStack)
		if #emptySlots < slotsNeeded and not self.config.allowOverflow then
			return Result:err("Not enough inventory space")
		end
		-- Add items to empty slots
		local remainingQuantity = quantity
		for _, slot in emptySlots do
			if remainingQuantity <= 0 then
				break
			end
			local slotQuantity = math.min(remainingQuantity, item.maxStack)
			local _object = {}
			local _left = "item"
			local _object_1 = table.clone(item)
			setmetatable(_object_1, nil)
			_object[_left] = _object_1
			_object.quantity = slotQuantity
			_object.slotIndex = slot.slotIndex
			_object.locked = false
			self.slots[slot.slotIndex + 1] = _object
			remainingQuantity -= slotQuantity
		end
		self:recordTransaction("add", item.id, quantity - remainingQuantity)
		self:notifyChange()
		return if remainingQuantity > 0 then Result:err(`Could only add {quantity - remainingQuantity} items due to space constraints`) else Result:ok(true)
	end
	function Inventory:removeItem(itemId, quantity)
		if quantity == nil then
			quantity = 1
		end
		if quantity <= 0 then
			return Result:err("Quantity must be positive")
		end
		local itemSlots = self:findItemSlots(itemId)
		if #itemSlots == 0 then
			return Result:err("Item not found in inventory")
		end
		-- Calculate total available quantity
		-- ▼ ReadonlyArray.reduce ▼
		local _result = 0
		local _callback = function(sum, slot)
			return sum + slot.quantity
		end
		for _i = 1, #itemSlots do
			_result = _callback(_result, itemSlots[_i], _i - 1, itemSlots)
		end
		-- ▲ ReadonlyArray.reduce ▲
		local totalAvailable = _result
		if totalAvailable < quantity then
			return Result:err(`Not enough items (available: {totalAvailable}, requested: {quantity})`)
		end
		-- Remove items from slots
		local remainingToRemove = quantity
		for _, slot in itemSlots do
			if remainingToRemove <= 0 then
				break
			end
			local removeFromSlot = math.min(remainingToRemove, slot.quantity)
			slot.quantity -= removeFromSlot
			remainingToRemove -= removeFromSlot
			-- Clear slot if empty
			if slot.quantity == 0 then
				self.slots[slot.slotIndex + 1] = {
					item = nil,
					quantity = 0,
					slotIndex = slot.slotIndex,
					locked = false,
				}
			end
		end
		self:recordTransaction("remove", itemId, quantity)
		self:notifyChange()
		return Result:ok(true)
	end
	function Inventory:moveItem(fromSlot, toSlot, quantity)
		if fromSlot == toSlot then
			return Result:err("Cannot move item to the same slot")
		end
		if not self:isValidSlotIndex(fromSlot) or not self:isValidSlotIndex(toSlot) then
			return Result:err("Invalid slot index")
		end
		local sourceSlot = self.slots[fromSlot + 1]
		local targetSlot = self.slots[toSlot + 1]
		if not sourceSlot.item then
			return Result:err("Source slot is empty")
		end
		if sourceSlot.locked then
			return Result:err("Source slot is locked")
		end
		local _condition = quantity
		if not (_condition ~= 0 and _condition == _condition and _condition) then
			_condition = sourceSlot.quantity
		end
		local moveQuantity = _condition
		if moveQuantity > sourceSlot.quantity then
			return Result:err("Not enough items in source slot")
		end
		-- If target slot is empty, simple move
		if not targetSlot.item then
			local _object = table.clone(sourceSlot.item)
			setmetatable(_object, nil)
			targetSlot.item = _object
			targetSlot.quantity = moveQuantity
			sourceSlot.quantity -= moveQuantity
			if sourceSlot.quantity == 0 then
				sourceSlot.item = nil
			end
			self:recordTransaction("move", sourceSlot.item.id, moveQuantity, fromSlot, toSlot)
			self:notifyChange()
			return Result:ok(true)
		end
		-- If target slot has the same item, try to stack
		if targetSlot.item.id == sourceSlot.item.id then
			local availableSpace = targetSlot.item.maxStack - targetSlot.quantity
			local actualMoveQuantity = math.min(moveQuantity, availableSpace)
			if actualMoveQuantity > 0 then
				targetSlot.quantity += actualMoveQuantity
				sourceSlot.quantity -= actualMoveQuantity
				if sourceSlot.quantity == 0 then
					sourceSlot.item = nil
				end
				self:recordTransaction("move", sourceSlot.item.id, actualMoveQuantity, fromSlot, toSlot)
				self:notifyChange()
				return Result:ok(true)
			end
		end
		return Result:err("Cannot move items to occupied slot with different item")
	end
	function Inventory:splitStack(slotIndex, splitQuantity)
		if not self:isValidSlotIndex(slotIndex) then
			return Result:err("Invalid slot index")
		end
		local slot = self.slots[slotIndex + 1]
		if not slot.item or slot.quantity <= 1 then
			return Result:err("Cannot split single item or empty slot")
		end
		if splitQuantity >= slot.quantity then
			return Result:err("Split quantity must be less than current quantity")
		end
		-- Find empty slot for split items
		local emptySlot = self:getEmptySlots()[1]
		if not emptySlot then
			return Result:err("No empty slot available for split")
		end
		-- Create new stack in empty slot
		local _object = table.clone(slot.item)
		setmetatable(_object, nil)
		emptySlot.item = _object
		emptySlot.quantity = splitQuantity
		slot.quantity -= splitQuantity
		self:recordTransaction("split", slot.item.id, splitQuantity, slotIndex, emptySlot.slotIndex)
		self:notifyChange()
		return Result:ok(emptySlot.slotIndex)
	end
	function Inventory:getItemCount(itemId)
		local _exp = self.slots
		-- ▼ ReadonlyArray.filter ▼
		local _newValue = {}
		local _callback = function(slot)
			local _result = slot.item
			if _result ~= nil then
				_result = _result.id
			end
			return _result == itemId
		end
		local _length = 0
		for _k, _v in _exp do
			if _callback(_v, _k - 1, _exp) == true then
				_length += 1
				_newValue[_length] = _v
			end
		end
		-- ▲ ReadonlyArray.filter ▲
		-- ▼ ReadonlyArray.reduce ▼
		local _result = 0
		local _callback_1 = function(sum, slot)
			return sum + slot.quantity
		end
		for _i = 1, #_newValue do
			_result = _callback_1(_result, _newValue[_i], _i - 1, _newValue)
		end
		-- ▲ ReadonlyArray.reduce ▲
		return _result
	end
	function Inventory:hasItem(itemId, quantity)
		if quantity == nil then
			quantity = 1
		end
		return self:getItemCount(itemId) >= quantity
	end
	function Inventory:getItemsByType(itemType)
		local _exp = self.slots
		-- ▼ ReadonlyArray.filter ▼
		local _newValue = {}
		local _callback = function(slot)
			local _result = slot.item
			if _result ~= nil then
				_result = _result.type
			end
			return _result == itemType
		end
		local _length = 0
		for _k, _v in _exp do
			if _callback(_v, _k - 1, _exp) == true then
				_length += 1
				_newValue[_length] = _v
			end
		end
		-- ▲ ReadonlyArray.filter ▲
		return _newValue
	end
	function Inventory:getItemsByRarity(rarity)
		local _exp = self.slots
		-- ▼ ReadonlyArray.filter ▼
		local _newValue = {}
		local _callback = function(slot)
			local _result = slot.item
			if _result ~= nil then
				_result = _result.rarity
			end
			return _result == rarity
		end
		local _length = 0
		for _k, _v in _exp do
			if _callback(_v, _k - 1, _exp) == true then
				_length += 1
				_newValue[_length] = _v
			end
		end
		-- ▲ ReadonlyArray.filter ▲
		return _newValue
	end
	function Inventory:searchItems(query)
		if not self.config.enableSearch then
			return {}
		end
		local lowerQuery = string.lower(query)
		local _exp = self.slots
		-- ▼ ReadonlyArray.filter ▼
		local _newValue = {}
		local _callback = function(slot)
			if not slot.item then
				return false
			end
			return (string.find(string.lower(slot.item.name), lowerQuery)) ~= nil or (string.find(string.lower(slot.item.description), lowerQuery)) ~= nil
		end
		local _length = 0
		for _k, _v in _exp do
			if _callback(_v, _k - 1, _exp) == true then
				_length += 1
				_newValue[_length] = _v
			end
		end
		-- ▲ ReadonlyArray.filter ▲
		return _newValue
	end
	function Inventory:sortInventory()
		if not self.config.autoSort then
			return nil
		end
		local _exp = self.slots
		-- ▼ ReadonlyArray.filter ▼
		local _newValue = {}
		local _callback = function(slot)
			return slot.item ~= nil
		end
		local _length = 0
		for _k, _v in _exp do
			if _callback(_v, _k - 1, _exp) == true then
				_length += 1
				_newValue[_length] = _v
			end
		end
		-- ▲ ReadonlyArray.filter ▲
		local itemSlots = _newValue
		-- Custom sort function that returns -1, 0, or 1
		local function sortInventorySlots(itemSlots)
			local _array = {}
			local _length_1 = #_array
			table.move(itemSlots, 1, #itemSlots, _length_1 + 1, _array)
			local sorted = _array
			for i = 0, #sorted - 2 do
				do
					local j = i + 1
					local _shouldIncrement = false
					while true do
						if _shouldIncrement then
							j += 1
						else
							_shouldIncrement = true
						end
						if not (j < #sorted) then
							break
						end
						local a = sorted[i + 1]
						local b = sorted[j + 1]
						if not a.item or not b.item then
							continue
						end
						local shouldSwap = false
						if a.item.type ~= b.item.type then
							shouldSwap = a.item.type > b.item.type
						elseif a.item.rarity ~= b.item.rarity then
							shouldSwap = a.item.rarity > b.item.rarity
						else
							shouldSwap = a.item.name > b.item.name
						end
						if shouldSwap then
							local temp = sorted[i + 1]
							sorted[i + 1] = sorted[j + 1]
							sorted[j + 1] = temp
						end
					end
				end
			end
			return sorted
		end
		local sortedSlots = sortInventorySlots(itemSlots)
		-- Clear all slots
		local _exp_1 = self.slots
		-- ▼ ReadonlyArray.forEach ▼
		local _callback_1 = function(slot)
			slot.item = nil
			slot.quantity = 0
		end
		for _k, _v in _exp_1 do
			_callback_1(_v, _k - 1, _exp_1)
		end
		-- ▲ ReadonlyArray.forEach ▲
		-- Refill with sorted items
		-- ▼ ReadonlyArray.forEach ▼
		local _callback_2 = function(slot, index)
			self.slots[index + 1].item = slot.item
			self.slots[index + 1].quantity = slot.quantity
		end
		for _k, _v in sortedSlots do
			_callback_2(_v, _k - 1, sortedSlots)
		end
		-- ▲ ReadonlyArray.forEach ▲
		self:notifyChange()
	end
	function Inventory:getStats()
		local stats = {
			totalItems = 0,
			totalWeight = 0,
			totalValue = 0,
			itemsByType = {},
			itemsByRarity = {},
			emptySlots = 0,
			occupancyRate = 0,
		}
		-- Initialize counters
		stats.itemsByType[ItemType.Weapon] = 0
		stats.itemsByType[ItemType.Armor] = 0
		stats.itemsByType[ItemType.Consumable] = 0
		stats.itemsByType[ItemType.Material] = 0
		stats.itemsByType[ItemType.Quest] = 0
		stats.itemsByType[ItemType.Currency] = 0
		stats.itemsByType[ItemType.Cosmetic] = 0
		stats.itemsByType[ItemType.Tool] = 0
		stats.itemsByType[ItemType.Key] = 0
		stats.itemsByType[ItemType.Collectible] = 0
		stats.itemsByRarity[ItemRarity.Common] = 0
		stats.itemsByRarity[ItemRarity.Uncommon] = 0
		stats.itemsByRarity[ItemRarity.Rare] = 0
		stats.itemsByRarity[ItemRarity.Epic] = 0
		stats.itemsByRarity[ItemRarity.Legendary] = 0
		stats.itemsByRarity[ItemRarity.Mythic] = 0
		-- Calculate stats
		local _exp = self.slots
		-- ▼ ReadonlyArray.forEach ▼
		local _callback = function(slot)
			if slot.item then
				stats.totalItems += slot.quantity
				stats.totalWeight += slot.item.weight * slot.quantity
				stats.totalValue += slot.item.value * slot.quantity
				stats.itemsByType[slot.item.type] += slot.quantity
				stats.itemsByRarity[slot.item.rarity] += slot.quantity
			else
				stats.emptySlots += 1
			end
		end
		for _k, _v in _exp do
			_callback(_v, _k - 1, _exp)
		end
		-- ▲ ReadonlyArray.forEach ▲
		stats.occupancyRate = (self.config.size - stats.emptySlots) / self.config.size
		return stats
	end
	function Inventory:getSlots()
		local _array = {}
		local _length = #_array
		local _array_1 = self.slots
		table.move(_array_1, 1, #_array_1, _length + 1, _array)
		return _array
	end
	function Inventory:getSlot(index)
		return if self:isValidSlotIndex(index) then self.slots[index + 1] else nil
	end
	function Inventory:setSlotLocked(index, locked)
		if not self:isValidSlotIndex(index) then
			return false
		end
		self.slots[index + 1].locked = locked
		self:notifyChange()
		return true
	end
	function Inventory:onChange(callback)
		local _changeCallbacks = self.changeCallbacks
		local _callback = callback
		table.insert(_changeCallbacks, _callback)
		-- Return unsubscribe function
		return function()
			local _changeCallbacks_1 = self.changeCallbacks
			local _callback_1 = callback
			local index = (table.find(_changeCallbacks_1, _callback_1) or 0) - 1
			if index >= 0 then
				table.remove(self.changeCallbacks, index + 1)
			end
		end
	end
	function Inventory:getTransactionHistory()
		local _array = {}
		local _length = #_array
		local _array_1 = self.transactions
		table.move(_array_1, 1, #_array_1, _length + 1, _array)
		return _array
	end
	function Inventory:clearTransactionHistory()
		self.transactions = {}
	end
	function Inventory:exportData()
		local _object = {
			slots = self:getSlots(),
		}
		local _left = "config"
		local _object_1 = table.clone(self.config)
		setmetatable(_object_1, nil)
		_object[_left] = _object_1
		_object.stats = self:getStats()
		return _object
	end
	function Inventory:initializeSlots()
		self.slots = {}
		do
			local i = 0
			local _shouldIncrement = false
			while true do
				if _shouldIncrement then
					i += 1
				else
					_shouldIncrement = true
				end
				if not (i < self.config.size) then
					break
				end
				local _slots = self.slots
				local _arg0 = {
					item = nil,
					quantity = 0,
					slotIndex = i,
					locked = false,
				}
				table.insert(_slots, _arg0)
			end
		end
	end
	function Inventory:tryStackItem(item, quantity)
		local existingSlots = self:findItemSlots(item.id)
		local remainingQuantity = quantity
		for _, slot in existingSlots do
			if remainingQuantity <= 0 then
				break
			end
			if not slot.item then
				continue
			end
			local availableSpace = slot.item.maxStack - slot.quantity
			local addToSlot = math.min(remainingQuantity, availableSpace)
			if addToSlot > 0 then
				slot.quantity += addToSlot
				remainingQuantity -= addToSlot
			end
		end
		return Result:ok(remainingQuantity)
	end
	function Inventory:findItemSlots(itemId)
		local _exp = self.slots
		-- ▼ ReadonlyArray.filter ▼
		local _newValue = {}
		local _callback = function(slot)
			local _result = slot.item
			if _result ~= nil then
				_result = _result.id
			end
			return _result == itemId
		end
		local _length = 0
		for _k, _v in _exp do
			if _callback(_v, _k - 1, _exp) == true then
				_length += 1
				_newValue[_length] = _v
			end
		end
		-- ▲ ReadonlyArray.filter ▲
		return _newValue
	end
	function Inventory:getEmptySlots()
		local _exp = self.slots
		-- ▼ ReadonlyArray.filter ▼
		local _newValue = {}
		local _callback = function(slot)
			return not slot.item and not slot.locked
		end
		local _length = 0
		for _k, _v in _exp do
			if _callback(_v, _k - 1, _exp) == true then
				_length += 1
				_newValue[_length] = _v
			end
		end
		-- ▲ ReadonlyArray.filter ▲
		return _newValue
	end
	function Inventory:canFitWeight(additionalWeight)
		local currentWeight = self:getStats().totalWeight
		return currentWeight + additionalWeight <= self.config.maxWeight
	end
	function Inventory:isValidSlotIndex(index)
		return index >= 0 and index < #self.slots
	end
	function Inventory:recordTransaction(transactionType, itemId, quantity, fromSlot, toSlot)
		local transaction = {
			id = `txn_{tick()}_{math.random()}`,
			type = transactionType,
			itemId = itemId,
			quantity = quantity,
			fromSlot = fromSlot,
			toSlot = toSlot,
			timestamp = tick(),
		}
		local _exp = self.transactions
		table.insert(_exp, transaction)
		-- Limit transaction history
		if #self.transactions > self.maxTransactionHistory then
			table.remove(self.transactions, 1)
		end
	end
	function Inventory:notifyChange()
		local _exp = self.changeCallbacks
		-- ▼ ReadonlyArray.forEach ▼
		local _callback = function(callback)
			TS.try(function()
				callback(self)
			end, function(error)
				warn(`Inventory change callback failed: {error}`)
			end)
		end
		for _k, _v in _exp do
			_callback(_v, _k - 1, _exp)
		end
		-- ▲ ReadonlyArray.forEach ▲
	end
end
--[[
	*
	 * Inventory Manager service for managing multiple inventories
	 
]]
local InventoryManager
do
	local super = BaseService
	InventoryManager = setmetatable({}, {
		__tostring = function()
			return "InventoryManager"
		end,
		__index = super,
	})
	InventoryManager.__index = InventoryManager
	function InventoryManager.new(...)
		local self = setmetatable({}, InventoryManager)
		return self:constructor(...) or self
	end
	function InventoryManager:constructor()
		super.constructor(self, "InventoryManager")
		self.inventories = {}
		self.itemDefinitions = {}
	end
	function InventoryManager:getInstance()
		if not InventoryManager.instance then
			InventoryManager.instance = InventoryManager.new()
		end
		return InventoryManager.instance
	end
	InventoryManager.onInitialize = TS.async(function(self)
		local _exitType, _returns = TS.try(function()
			self:loadDefaultItems()
			print("🎒 Inventory Manager initialized")
			return TS.TRY_RETURN, { Result:ok(nil) }
		end, function(error)
			return TS.TRY_RETURN, { Result:err(createError(`Failed to initialize InventoryManager: {error}`)) }
		end)
		if _exitType then
			return unpack(_returns)
		end
	end)
	InventoryManager.onShutdown = TS.async(function(self)
		table.clear(self.inventories)
		table.clear(self.itemDefinitions)
		print("🎒 Inventory Manager shutdown")
		return Result:ok(nil)
	end)
	function InventoryManager:createInventory(id, config)
		local _inventories = self.inventories
		local _id = id
		if _inventories[_id] ~= nil then
			return Result:err(`Inventory '{id}' already exists`)
		end
		local inventory = Inventory.new(config)
		local _inventories_1 = self.inventories
		local _id_1 = id
		_inventories_1[_id_1] = inventory
		print(`🎒 Created inventory: {id}`)
		return Result:ok(inventory)
	end
	function InventoryManager:getInventory(id)
		local _inventories = self.inventories
		local _id = id
		return _inventories[_id]
	end
	function InventoryManager:removeInventory(id)
		local _inventories = self.inventories
		local _id = id
		-- ▼ Map.delete ▼
		local _valueExisted = _inventories[_id] ~= nil
		_inventories[_id] = nil
		-- ▲ Map.delete ▲
		local removed = _valueExisted
		if removed then
			print(`🎒 Removed inventory: {id}`)
		end
		return removed
	end
	function InventoryManager:registerItem(item)
		local _itemDefinitions = self.itemDefinitions
		local _id = item.id
		if _itemDefinitions[_id] ~= nil then
			return Result:err(`Item '{item.id}' already registered`)
		end
		local _itemDefinitions_1 = self.itemDefinitions
		local _exp = item.id
		local _object = table.clone(item)
		setmetatable(_object, nil)
		_itemDefinitions_1[_exp] = _object
		return Result:ok(nil)
	end
	function InventoryManager:getItemDefinition(itemId)
		local _itemDefinitions = self.itemDefinitions
		local _itemId = itemId
		return _itemDefinitions[_itemId]
	end
	function InventoryManager:getAllItemDefinitions()
		local items = {}
		local _exp = self.itemDefinitions
		-- ▼ ReadonlyMap.forEach ▼
		local _callback = function(item)
			local _item = item
			table.insert(items, _item)
			return #items
		end
		for _k, _v in _exp do
			_callback(_v, _k, _exp)
		end
		-- ▲ ReadonlyMap.forEach ▲
		return items
	end
	function InventoryManager:createItem(itemId, overrides)
		local _itemDefinitions = self.itemDefinitions
		local _itemId = itemId
		local definition = _itemDefinitions[_itemId]
		if not definition then
			return nil
		end
		local _object = table.clone(definition)
		setmetatable(_object, nil)
		if overrides then
			for _k, _v in overrides do
				_object[_k] = _v
			end
		end
		return _object
	end
	function InventoryManager:getAllInventoryStats()
		local stats = {}
		local _exp = self.inventories
		-- ▼ ReadonlyMap.forEach ▼
		local _callback = function(inventory, id)
			stats[id] = inventory:getStats()
		end
		for _k, _v in _exp do
			_callback(_v, _k, _exp)
		end
		-- ▲ ReadonlyMap.forEach ▲
		return stats
	end
	function InventoryManager:getReport()
		local report = "🎒 Inventory Manager Report\n"
		-- ▼ ReadonlyMap.size ▼
		local _size = 0
		for _ in self.inventories do
			_size += 1
		end
		-- ▲ ReadonlyMap.size ▲
		report ..= `Total Inventories: {_size}\n`
		-- ▼ ReadonlyMap.size ▼
		local _size_1 = 0
		for _ in self.itemDefinitions do
			_size_1 += 1
		end
		-- ▲ ReadonlyMap.size ▲
		report ..= `Registered Items: {_size_1}\n\n`
		local _exp = self.inventories
		-- ▼ ReadonlyMap.forEach ▼
		local _callback = function(inventory, id)
			local stats = inventory:getStats()
			report ..= `📋 Inventory: {id}\n`
			report ..= `  Items: {stats.totalItems} | Empty Slots: {stats.emptySlots}\n`
			report ..= `  Weight: {string.format("%.1f", stats.totalWeight)} | Value: {stats.totalValue}\n`
			report ..= `  Occupancy: {string.format("%.1f", stats.occupancyRate * 100)}%\n\n`
		end
		for _k, _v in _exp do
			_callback(_v, _k, _exp)
		end
		-- ▲ ReadonlyMap.forEach ▲
		return report
	end
	function InventoryManager:loadDefaultItems()
		-- Load some default item definitions for testing
		local defaultItems = { {
			id = "health_potion",
			name = "Health Potion",
			description = "Restores health over time",
			type = ItemType.Consumable,
			rarity = ItemRarity.Common,
			maxStack = 10,
			value = 50,
			weight = 0.5,
		}, {
			id = "iron_sword",
			name = "Iron Sword",
			description = "A sturdy iron sword",
			type = ItemType.Weapon,
			rarity = ItemRarity.Common,
			maxStack = 1,
			value = 200,
			weight = 3.0,
		} }
		-- ▼ ReadonlyArray.forEach ▼
		local _callback = function(item)
			local _value = item.id
			if _value ~= "" and _value then
				self:registerItem(item)
			end
		end
		for _k, _v in defaultItems do
			_callback(_v, _k - 1, defaultItems)
		end
		-- ▲ ReadonlyArray.forEach ▲
	end
end
-- Global inventory management functions for easy access
local InventorySystem = {
	getInstance = function()
		return InventoryManager:getInstance()
	end,
	createInventory = function(id, config)
		return InventoryManager:getInstance():createInventory(id, config)
	end,
	getInventory = function(id)
		return InventoryManager:getInstance():getInventory(id)
	end,
	getReport = function()
		return InventoryManager:getInstance():getReport()
	end,
}
return {
	Inventory = Inventory,
	InventoryManager = InventoryManager,
	InventorySystem = InventorySystem,
}
