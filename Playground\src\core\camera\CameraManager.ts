import { Workspace, RunService, UserInputService, TweenService } from "@rbxts/services";
import { BaseService } from "../foundation/BaseService";
import { Result } from "../foundation/types/Result";
import { createError, Error } from "../foundation/types/BrandedTypes";

export enum CameraMode {
	ThirdPerson = "ThirdPerson",
	FirstPerson = "FirstPerson",
	Fixed = "Fixed",
	Cinematic = "Cinematic",
	Orbital = "Orbital",
	Free = "Free",
	Follow = "Follow",
}

export interface CameraConfiguration {
	defaultMode: CameraMode;
	sensitivity: number;
	smoothing: number;
	maxDistance: number;
	minDistance: number;
	maxAngle: number;
	minAngle: number;
	enableCollision: boolean;
	enableSmoothing: boolean;
	enableInput: boolean;
	fieldOfView: number;
}

export interface CameraState {
	mode: CameraMode;
	position: Vector3;
	lookAt: Vector3;
	distance: number;
	angleX: number;
	angleY: number;
	fieldOfView: number;
	isTransitioning: boolean;
}

export interface CameraTransition {
	duration: number;
	easingStyle: Enum.EasingStyle;
	easingDirection: Enum.EasingDirection;
	onComplete?: () => void;
}

/**
 * Advanced camera management system with multiple camera modes and smooth transitions
 */
export class CameraManager extends BaseService {
	private static instance: CameraManager;
	
	private camera: Camera;
	private config: CameraConfiguration;
	private state: CameraState;
	private target: BasePart | undefined;
	private offset: Vector3 = new Vector3(0, 0, 0);
	
	private inputConnections: RBXScriptConnection[] = [];
	private renderConnection?: RBXScriptConnection;
	private activeTween?: Tween;
	
	private lastMousePosition: Vector2 = new Vector2(0, 0);
	private isDragging = false;
	
	private savedCameraStates: Map<string, CameraState> = new Map();

	constructor() {
		super("CameraManager");
		
		this.camera = Workspace.CurrentCamera!;
		
		this.config = {
			defaultMode: CameraMode.ThirdPerson,
			sensitivity: 0.5,
			smoothing: 0.1,
			maxDistance: 50,
			minDistance: 2,
			maxAngle: 80,
			minAngle: -80,
			enableCollision: true,
			enableSmoothing: true,
			enableInput: true,
			fieldOfView: 70,
		};
		
		this.state = {
			mode: this.config.defaultMode,
			position: new Vector3(0, 10, 20),
			lookAt: new Vector3(0, 0, 0),
			distance: 20,
			angleX: 0,
			angleY: 0,
			fieldOfView: this.config.fieldOfView,
			isTransitioning: false,
		};
	}

	public static getInstance(): CameraManager {
		if (!CameraManager.instance) {
			CameraManager.instance = new CameraManager();
		}
		return CameraManager.instance;
	}

	protected async onInitialize(): Promise<Result<void, Error>> {
		try {
			this.setupInputHandlers();
			this.startRenderLoop();
			this.setCameraMode(this.config.defaultMode);
			print("📷 Camera Manager initialized");
			return Result.ok(undefined);
		} catch (error) {
			return Result.err(createError(`Failed to initialize CameraManager: ${error}`));
		}
	}

	protected async onShutdown(): Promise<Result<void, Error>> {
		this.cleanup();
		print("📷 Camera Manager shutdown");
		return Result.ok(undefined);
	}

	/**
	 * Set camera mode
	 */
	public setCameraMode(mode: CameraMode, transition?: CameraTransition): void {
		if (this.state.mode === mode && !transition) return;

		const previousMode = this.state.mode;
		this.state.mode = mode;

		// Apply mode-specific settings
		this.applyModeSettings(mode);

		if (transition) {
			this.animateToState(this.state, transition);
		}

		print(`📷 Camera mode changed: ${previousMode} → ${mode}`);
	}

	/**
	 * Set camera target (for follow modes)
	 */
	public setTarget(target: BasePart | undefined, offset?: Vector3): void {
		this.target = target;
		if (offset) {
			this.offset = offset;
		}
	}

	/**
	 * Set camera position directly
	 */
	public setPosition(position: Vector3, lookAt?: Vector3, transition?: CameraTransition): void {
		const newState: CameraState = {
			...this.state,
			position,
			lookAt: lookAt || this.state.lookAt,
		};

		if (transition) {
			this.animateToState(newState, transition);
		} else {
			this.state = newState;
			this.applyCameraState();
		}
	}

	/**
	 * Set camera distance (for orbital/third-person modes)
	 */
	public setDistance(distance: number, transition?: CameraTransition): void {
		const clampedDistance = math.clamp(distance, this.config.minDistance, this.config.maxDistance);
		
		if (transition) {
			const newState: CameraState = {
				...this.state,
				distance: clampedDistance,
			};
			this.animateToState(newState, transition);
		} else {
			this.state.distance = clampedDistance;
		}
	}

	/**
	 * Set camera angles (for orbital modes)
	 */
	public setAngles(angleX: number, angleY: number, transition?: CameraTransition): void {
		const clampedAngleY = math.clamp(angleY, this.config.minAngle, this.config.maxAngle);
		
		if (transition) {
			const newState: CameraState = {
				...this.state,
				angleX: angleX % 360,
				angleY: clampedAngleY,
			};
			this.animateToState(newState, transition);
		} else {
			this.state.angleX = angleX % 360;
			this.state.angleY = clampedAngleY;
		}
	}

	/**
	 * Set field of view
	 */
	public setFieldOfView(fov: number, transition?: CameraTransition): void {
		const clampedFov = math.clamp(fov, 1, 120);
		
		if (transition) {
			const newState: CameraState = {
				...this.state,
				fieldOfView: clampedFov,
			};
			this.animateToState(newState, transition);
		} else {
			this.state.fieldOfView = clampedFov;
			this.camera.FieldOfView = clampedFov;
		}
	}

	/**
	 * Save current camera state
	 */
	public saveCameraState(name: string): void {
		this.savedCameraStates.set(name, { ...this.state });
		print(`📷 Camera state saved: ${name}`);
	}

	/**
	 * Restore saved camera state
	 */
	public restoreCameraState(name: string, transition?: CameraTransition): boolean {
		const savedState = this.savedCameraStates.get(name);
		if (!savedState) {
			warn(`Camera state '${name}' not found`);
			return false;
		}

		if (transition) {
			this.animateToState(savedState, transition);
		} else {
			this.state = { ...savedState };
			this.applyCameraState();
		}

		print(`📷 Camera state restored: ${name}`);
		return true;
	}

	/**
	 * Shake the camera
	 */
	public shake(intensity: number, duration: number): void {
		if (this.activeTween) {
			this.activeTween.Cancel();
		}

		const originalPosition = this.state.position;
		let shakeTime = 0;

		const shakeConnection = RunService.Heartbeat.Connect((deltaTime) => {
			shakeTime += deltaTime;
			
			if (shakeTime >= duration) {
				shakeConnection.Disconnect();
				this.state.position = originalPosition;
				return;
			}

			// Apply random shake offset
			const shakeAmount = intensity * (1 - shakeTime / duration); // Decrease over time
			const shakeOffset = new Vector3(
				(math.random() - 0.5) * shakeAmount,
				(math.random() - 0.5) * shakeAmount,
				(math.random() - 0.5) * shakeAmount
			);

			this.state.position = originalPosition.add(shakeOffset);
		});
	}

	/**
	 * Smooth look at target
	 */
	public lookAt(target: Vector3, transition?: CameraTransition): void {
		const newState: CameraState = {
			...this.state,
			lookAt: target,
		};

		if (transition) {
			this.animateToState(newState, transition);
		} else {
			this.state.lookAt = target;
		}
	}

	/**
	 * Get current camera state
	 */
	public getCameraState(): CameraState {
		return { ...this.state };
	}

	/**
	 * Update camera configuration
	 */
	public updateConfig(newConfig: Partial<CameraConfiguration>): void {
		this.config = { ...this.config, ...newConfig };
		
		// Reapply mode settings if needed
		this.applyModeSettings(this.state.mode);
	}

	/**
	 * Enable or disable camera input
	 */
	public setInputEnabled(enabled: boolean): void {
		this.config.enableInput = enabled;
		
		if (enabled) {
			this.setupInputHandlers();
		} else {
			this.cleanup();
		}
	}

	/**
	 * Get camera ray from screen position
	 */
	public screenPointToRay(screenPosition: Vector2): Ray {
		const unitRay = this.camera.ScreenPointToRay(screenPosition.X, screenPosition.Y);
		return new Ray(unitRay.Origin, unitRay.Direction.mul(1000));
	}

	/**
	 * Convert world position to screen position
	 */
	public worldToScreenPoint(worldPosition: Vector3): [Vector3, boolean] {
		return this.camera.WorldToScreenPoint(worldPosition);
	}

	private setupInputHandlers(): void {
		if (!RunService.IsClient() || !this.config.enableInput) return;

		this.cleanup(); // Clean up existing connections

		// Mouse input for camera rotation
		const inputConnection = UserInputService.InputChanged.Connect((input, gameProcessed) => {
			if (gameProcessed) return;

			if (input.UserInputType === Enum.UserInputType.MouseMovement) {
				this.handleMouseMovement(input);
			} else if (input.UserInputType === Enum.UserInputType.MouseWheel) {
				this.handleMouseWheel(input);
			}
		});

		const inputBeganConnection = UserInputService.InputBegan.Connect((input, gameProcessed) => {
			if (gameProcessed) return;

			if (input.UserInputType === Enum.UserInputType.MouseButton2) {
				this.isDragging = true;
				this.lastMousePosition = new Vector2(input.Position.X, input.Position.Y);
			}
		});

		const inputEndedConnection = UserInputService.InputEnded.Connect((input, gameProcessed) => {
			if (input.UserInputType === Enum.UserInputType.MouseButton2) {
				this.isDragging = false;
			}
		});

		this.inputConnections.push(inputConnection, inputBeganConnection, inputEndedConnection);
	}

	private startRenderLoop(): void {
		this.renderConnection = RunService.RenderStepped.Connect(() => {
			this.updateCamera();
		});
	}

	private cleanup(): void {
		this.inputConnections.forEach(connection => connection.Disconnect());
		this.inputConnections = [];

		if (this.renderConnection) {
			this.renderConnection.Disconnect();
			this.renderConnection = undefined;
		}

		if (this.activeTween) {
			this.activeTween.Cancel();
			this.activeTween = undefined;
		}
	}

	private handleMouseMovement(input: InputObject): void {
		if (!this.isDragging) return;
		if (this.state.mode !== CameraMode.Orbital && this.state.mode !== CameraMode.ThirdPerson) return;

		const currentMousePosition = new Vector2(input.Position.X, input.Position.Y);
		const mouseDelta = currentMousePosition.sub(this.lastMousePosition);

		// Apply sensitivity
		const deltaX = mouseDelta.X * this.config.sensitivity;
		const deltaY = mouseDelta.Y * this.config.sensitivity;

		// Update angles
		this.state.angleX -= deltaX;
		this.state.angleY = math.clamp(
			this.state.angleY - deltaY,
			this.config.minAngle,
			this.config.maxAngle
		);

		this.lastMousePosition = currentMousePosition;
	}

	private handleMouseWheel(input: InputObject): void {
		if (this.state.mode !== CameraMode.Orbital && this.state.mode !== CameraMode.ThirdPerson) return;

		const zoomDelta = input.Position.Z * 2;
		this.state.distance = math.clamp(
			this.state.distance - zoomDelta,
			this.config.minDistance,
			this.config.maxDistance
		);
	}

	private updateCamera(): void {
		switch (this.state.mode) {
			case CameraMode.ThirdPerson:
				this.updateThirdPersonCamera();
				break;
			case CameraMode.FirstPerson:
				this.updateFirstPersonCamera();
				break;
			case CameraMode.Orbital:
				this.updateOrbitalCamera();
				break;
			case CameraMode.Follow:
				this.updateFollowCamera();
				break;
			case CameraMode.Fixed:
				this.updateFixedCamera();
				break;
			case CameraMode.Free:
				this.updateFreeCamera();
				break;
			case CameraMode.Cinematic:
				this.updateCinematicCamera();
				break;
		}

		this.applyCameraState();
	}

	private updateThirdPersonCamera(): void {
		if (!this.target) return;

		const targetPosition = this.target.Position.add(this.offset);
		
		// Calculate camera position based on angles and distance
		const radX = math.rad(this.state.angleX);
		const radY = math.rad(this.state.angleY);
		
		const x = math.cos(radY) * math.sin(radX) * this.state.distance;
		const y = math.sin(radY) * this.state.distance;
		const z = math.cos(radY) * math.cos(radX) * this.state.distance;
		
		const cameraOffset = new Vector3(x, y, z);
		let desiredPosition = targetPosition.add(cameraOffset);

		// Collision detection
		if (this.config.enableCollision) {
			desiredPosition = this.performCollisionCheck(targetPosition, desiredPosition);
		}

		// Smooth camera movement
		if (this.config.enableSmoothing) {
			this.state.position = this.state.position.Lerp(desiredPosition, this.config.smoothing);
			this.state.lookAt = this.state.lookAt.Lerp(targetPosition, this.config.smoothing);
		} else {
			this.state.position = desiredPosition;
			this.state.lookAt = targetPosition;
		}
	}

	private updateFirstPersonCamera(): void {
		if (!this.target) return;

		const targetPosition = this.target.Position.add(this.offset);
		this.state.position = targetPosition;
		
		// Look direction based on angles
		const radX = math.rad(this.state.angleX);
		const radY = math.rad(this.state.angleY);
		
		const direction = new Vector3(
			math.cos(radY) * math.sin(radX),
			math.sin(radY),
			math.cos(radY) * math.cos(radX)
		);
		
		this.state.lookAt = targetPosition.add(direction.mul(10));
	}

	private updateOrbitalCamera(): void {
		// Similar to third person but orbits around lookAt point
		const radX = math.rad(this.state.angleX);
		const radY = math.rad(this.state.angleY);
		
		const x = math.cos(radY) * math.sin(radX) * this.state.distance;
		const y = math.sin(radY) * this.state.distance;
		const z = math.cos(radY) * math.cos(radX) * this.state.distance;
		
		const cameraOffset = new Vector3(x, y, z);
		this.state.position = this.state.lookAt.add(cameraOffset);
	}

	private updateFollowCamera(): void {
		if (!this.target) return;

		const targetPosition = this.target.Position.add(this.offset);
		
		if (this.config.enableSmoothing) {
			this.state.position = this.state.position.Lerp(targetPosition, this.config.smoothing);
		} else {
			this.state.position = targetPosition;
		}
	}

	private updateFixedCamera(): void {
		// Position and lookAt remain as set
	}

	private updateFreeCamera(): void {
		// Free camera controlled by input
		// Implementation would depend on specific input handling
	}

	private updateCinematicCamera(): void {
		// Cinematic camera for cutscenes
		// Typically controlled by external systems
	}

	private performCollisionCheck(target: Vector3, desired: Vector3): Vector3 {
		const direction = desired.sub(target);
		const distance = direction.Magnitude;
		
		if (distance === 0) return desired;
		
		const ray = new Ray(target, direction.Unit.mul(distance));
		const raycastParams = new RaycastParams();
		raycastParams.FilterType = Enum.RaycastFilterType.Blacklist;
		raycastParams.FilterDescendantsInstances = []; // Add objects to ignore

		const result = Workspace.Raycast(target, direction, raycastParams);
		
		if (result) {
			// Move camera closer to avoid collision
			const hitDistance = result.Distance - 1; // Small buffer
			return target.add(direction.Unit.mul(math.max(hitDistance, this.config.minDistance)));
		}
		
		return desired;
	}

	private applyCameraState(): void {
		this.camera.CameraType = Enum.CameraType.Scriptable;
		this.camera.CFrame = CFrame.lookAt(this.state.position, this.state.lookAt);
		this.camera.FieldOfView = this.state.fieldOfView;
	}

	private applyModeSettings(mode: CameraMode): void {
		switch (mode) {
			case CameraMode.FirstPerson:
				this.state.distance = 0;
				break;
			case CameraMode.ThirdPerson:
				this.state.distance = 20;
				break;
			case CameraMode.Orbital:
				this.state.distance = 30;
				break;
		}
	}

	private animateToState(targetState: CameraState, transition: CameraTransition): void {
		if (this.activeTween) {
			this.activeTween.Cancel();
		}

		this.state.isTransitioning = true;

		// Simple lerp-based animation instead of TweenService for complex objects
		const startTime = tick();
		const startState = { ...this.state };

		const animationConnection = RunService.Heartbeat.Connect(() => {
			const elapsed = tick() - startTime;
			const progress = math.min(elapsed / transition.duration, 1);
			
			// Apply easing (simplified)
			const easedProgress = this.applyEasing(progress, transition.easingStyle);

			// Interpolate between start and target states
			this.state.position = startState.position.Lerp(targetState.position, easedProgress);
			this.state.lookAt = startState.lookAt.Lerp(targetState.lookAt, easedProgress);
			this.state.distance = startState.distance + (targetState.distance - startState.distance) * easedProgress;
			this.state.angleX = startState.angleX + (targetState.angleX - startState.angleX) * easedProgress;
			this.state.angleY = startState.angleY + (targetState.angleY - startState.angleY) * easedProgress;
			this.state.fieldOfView = startState.fieldOfView + (targetState.fieldOfView - startState.fieldOfView) * easedProgress;

			if (progress >= 1) {
				animationConnection.Disconnect();
				this.state.isTransitioning = false;
				this.state = { ...targetState };
				
				if (transition.onComplete) {
					transition.onComplete();
				}
			}
		});
	}

	private applyEasing(t: number, style: Enum.EasingStyle): number {
		// Simplified easing functions
		switch (style) {
			case Enum.EasingStyle.Quad:
				return t * t;
			case Enum.EasingStyle.Cubic:
				return t * t * t;
			case Enum.EasingStyle.Sine:
				return 1 - math.cos(t * math.pi / 2);
			default:
				return t; // Linear
		}
	}

	/**
	 * Get a comprehensive camera report
	 */
	public getReport(): string {
		let report = "📷 Camera Manager Report\n";
		report += `Mode: ${this.state.mode}\n`;
		report += `Position: (${string.format("%.1f", this.state.position.X)}, ${string.format("%.1f", this.state.position.Y)}, ${string.format("%.1f", this.state.position.Z)})\n`;
		report += `Look At: (${string.format("%.1f", this.state.lookAt.X)}, ${string.format("%.1f", this.state.lookAt.Y)}, ${string.format("%.1f", this.state.lookAt.Z)})\n`;
		report += `Distance: ${string.format("%.1f", this.state.distance)}\n`;
		report += `Angles: (${string.format("%.1f", this.state.angleX)}°, ${string.format("%.1f", this.state.angleY)}°)\n`;
		report += `FOV: ${string.format("%.1f", this.state.fieldOfView)}°\n`;
		report += `Target: ${this.target ? "Set" : "None"}\n`;
		report += `Input Enabled: ${this.config.enableInput}\n`;
		report += `Smoothing: ${this.config.enableSmoothing}\n`;
		report += `Collision: ${this.config.enableCollision}\n`;
		report += `Saved States: ${this.savedCameraStates.size()}\n`;
		
		return report;
	}
}

// Global camera management functions for easy access
export const Camera = {
	getInstance: () => CameraManager.getInstance(),
	setMode: (mode: CameraMode, transition?: CameraTransition) => 
		CameraManager.getInstance().setCameraMode(mode, transition),
	setTarget: (target: BasePart | undefined, offset?: Vector3) => 
		CameraManager.getInstance().setTarget(target, offset),
	setPosition: (position: Vector3, lookAt?: Vector3, transition?: CameraTransition) =>
		CameraManager.getInstance().setPosition(position, lookAt, transition),
	shake: (intensity: number, duration: number) => 
		CameraManager.getInstance().shake(intensity, duration),
	getReport: () => CameraManager.getInstance().getReport(),
};

// Export types for external use