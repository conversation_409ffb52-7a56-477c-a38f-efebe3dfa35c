-- Compiled with roblox-ts v3.0.0
local QuestType = {
	Main = "Main",
	Side = "Side",
	Daily = "Daily",
	Weekly = "Weekly",
	Achievement = "Achievement",
	Tutorial = "Tutorial",
	Event = "Event",
}
local QuestStatus = {
	NotStarted = "NotStarted",
	Available = "Available",
	Active = "Active",
	Completed = "Completed",
	Failed = "Failed",
	Abandoned = "Abandoned",
	Expired = "Expired",
}
local ObjectiveType = {
	KillTarget = "KillTarget",
	CollectItem = "CollectItem",
	ReachLocation = "ReachLocation",
	TalkToNPC = "TalkToNPC",
	UseItem = "UseItem",
	Craft = "Craft",
	Timer = "Timer",
	Custom = "Custom",
}
return {
	QuestType = QuestType,
	QuestStatus = QuestStatus,
	ObjectiveType = ObjectiveType,
}
