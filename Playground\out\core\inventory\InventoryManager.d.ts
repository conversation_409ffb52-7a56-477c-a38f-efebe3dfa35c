import { BaseService } from "../foundation/BaseService";
import { Result } from "../foundation/types/Result";
import { Error } from "../foundation/types/BrandedTypes";
import { ItemDefinition, InventorySlot, InventoryConfiguration, InventoryStats, ItemTransaction, ItemType, ItemRarity } from "./types";
/**
 * Individual inventory instance for a player or container
 */
export declare class Inventory {
    private slots;
    private config;
    private transactions;
    private maxTransactionHistory;
    private changeCallbacks;
    constructor(config: InventoryConfiguration);
    /**
     * Add an item to the inventory
     */
    addItem(item: ItemDefinition, quantity?: number): Result<boolean, string>;
    /**
     * Remove an item from the inventory
     */
    removeItem(itemId: string, quantity?: number): Result<boolean, string>;
    /**
     * Move an item from one slot to another
     */
    moveItem(fromSlot: number, toSlot: number, quantity?: number): Result<boolean, string>;
    /**
     * Split a stack of items
     */
    splitStack(slotIndex: number, splitQuantity: number): Result<number, string>;
    /**
     * Get item count for a specific item
     */
    getItemCount(itemId: string): number;
    /**
     * Check if inventory has enough of an item
     */
    hasItem(itemId: string, quantity?: number): boolean;
    /**
     * Get all items of a specific type
     */
    getItemsByType(itemType: ItemType): InventorySlot[];
    /**
     * Get all items of a specific rarity
     */
    getItemsByRarity(rarity: ItemRarity): InventorySlot[];
    /**
     * Search for items by name or description
     */
    searchItems(query: string): InventorySlot[];
    /**
     * Sort inventory automatically
     */
    sortInventory(): void;
    /**
     * Get inventory statistics
     */
    getStats(): InventoryStats;
    /**
     * Get all slots
     */
    getSlots(): InventorySlot[];
    /**
     * Get a specific slot
     */
    getSlot(index: number): InventorySlot | undefined;
    /**
     * Lock or unlock a slot
     */
    setSlotLocked(index: number, locked: boolean): boolean;
    /**
     * Add a change callback
     */
    onChange(callback: (inventory: Inventory) => void): () => void;
    /**
     * Get transaction history
     */
    getTransactionHistory(): ItemTransaction[];
    /**
     * Clear transaction history
     */
    clearTransactionHistory(): void;
    /**
     * Export inventory data
     */
    exportData(): {
        slots: InventorySlot[];
        config: InventoryConfiguration;
        stats: InventoryStats;
    };
    private initializeSlots;
    private tryStackItem;
    private findItemSlots;
    private getEmptySlots;
    private canFitWeight;
    private isValidSlotIndex;
    private recordTransaction;
    private notifyChange;
}
/**
 * Inventory Manager service for managing multiple inventories
 */
export declare class InventoryManager extends BaseService {
    private static instance;
    private inventories;
    private itemDefinitions;
    constructor();
    static getInstance(): InventoryManager;
    protected onInitialize(): Promise<Result<void, Error>>;
    protected onShutdown(): Promise<Result<void, Error>>;
    /**
     * Create a new inventory
     */
    createInventory(id: string, config: InventoryConfiguration): Result<Inventory, string>;
    /**
     * Get an existing inventory
     */
    getInventory(id: string): Inventory | undefined;
    /**
     * Remove an inventory
     */
    removeInventory(id: string): boolean;
    /**
     * Register an item definition
     */
    registerItem(item: ItemDefinition): Result<void, string>;
    /**
     * Get an item definition
     */
    getItemDefinition(itemId: string): ItemDefinition | undefined;
    /**
     * Get all item definitions
     */
    getAllItemDefinitions(): ItemDefinition[];
    /**
     * Create an item instance from definition
     */
    createItem(itemId: string, overrides?: Partial<ItemDefinition>): ItemDefinition | undefined;
    /**
     * Get inventory statistics for all inventories
     */
    getAllInventoryStats(): Record<string, InventoryStats>;
    /**
     * Generate a comprehensive report
     */
    getReport(): string;
    private loadDefaultItems;
}
export declare const InventorySystem: {
    getInstance: () => InventoryManager;
    createInventory: (id: string, config: InventoryConfiguration) => Result<Inventory, string>;
    getInventory: (id: string) => Inventory | undefined;
    getReport: () => string;
};
