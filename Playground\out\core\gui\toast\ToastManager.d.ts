import * as React from "@rbxts/react";
import { ToastData } from "./Toast";
interface ToastManagerProps {
}
interface ToastManagerInstance {
    addToast: (toast: Omit<ToastData, "id">) => void;
}
export declare namespace ToastService {
    function setInstance(instance: ToastManagerInstance): void;
    function showToast(toast: Omit<ToastData, "id">): void;
    function showSuccess(title: string, message?: string, duration?: number): void;
    function showError(title: string, message?: string, duration?: number): void;
    function showWarning(title: string, message?: string, duration?: number): void;
    function showInfo(title: string, message?: string, duration?: number): void;
}
export declare function ToastManager(props: ToastManagerProps): React.ReactElement;
export {};
