import * as React from "@rbxts/react";
import { COLORS, SIZES, BORDER_RADIUS } from "../../design";
import { IconButton } from "../button";
import { Label } from "../label/Label";
import { ContainerFrame } from "../frame";
import { Overlay } from "../overlay/Overlay";
import { ErrorBoundary } from "../error-boundary";

interface ModalProps {
	title: string;
	isOpen: boolean;
	onClose: () => void;
	children?: React.ReactNode;
	width?: number;
	height?: number;
}

export function Modal(props: ModalProps) {
	const [isAnimating, setIsAnimating] = React.useState(false);
	const width = props.width ?? 400; // Increased default width
	const height = props.height ?? 300; // Increased default height

	// Handle open/close animations
	React.useEffect(() => {
		if (props.isOpen) {
			setIsAnimating(true);
		}
	}, [props.isOpen]);

	const handleClose = () => {
		setIsAnimating(false);
		// Delay actual close to allow animation
		task.delay(0.2, () => {
			props.onClose();
		});
	};

	if (!props.isOpen && !isAnimating) return <></>;

	const modalScale = isAnimating && props.isOpen ? 1 : 0.9;
	const modalTransparency = isAnimating && props.isOpen ? 0 : 0.3;

	return (
		<Overlay onBackdropClick={handleClose} backgroundColor={Color3.fromRGB(0, 0, 0)}>
			{/* Modal container with click blocking and proper centered sizing */}
			<textbutton
				Text=""
				BackgroundColor3={Color3.fromHex(COLORS.bg.base)}
				Size={new UDim2(0, width, 0, height)} // Fixed size instead of scaled
				Position={new UDim2(0.5, 0, 0.5, 0)}
				AnchorPoint={new Vector2(0.5, 0.5)}
				ZIndex={12}
				AutoButtonColor={false}
				BorderSizePixel={0}
				BackgroundTransparency={modalTransparency}
				Event={{
					Activated: () => {
						// Stop propagation by doing nothing - this prevents backdrop click
					},
				}}
			>
				<uicorner CornerRadius={new UDim(0, BORDER_RADIUS.md)} />
				<uistroke Color={Color3.fromHex(COLORS.border.l2)} Thickness={1} />

				{/* Drop shadow effect */}
				<frame
					Size={new UDim2(1, 4, 1, 4)}
					Position={new UDim2(0, 2, 0, 2)}
					AnchorPoint={new Vector2(0, 0)}
					BackgroundColor3={Color3.fromRGB(0, 0, 0)}
					BackgroundTransparency={0.6}
					BorderSizePixel={0}
					ZIndex={-1}
				>
					<uicorner CornerRadius={new UDim(0, BORDER_RADIUS.md)} />
				</frame>

				{/* Streamlined content container with minimal spacing */}
				<ContainerFrame
					backgroundTransparency={1}
					size={new UDim2(1, 0, 1, 0)}
					padding={4} // Reduced padding from 8 to 4
					borderThickness={0}
					autoSize={Enum.AutomaticSize.None}
				>
					{/* Simple title and close button in top-right corner */}
					<Label
						text={props.title}
						fontSize={SIZES.fontSize + 2}
						bold={true}
						position={new UDim2(0, 0, 0, 0)}
						size={new UDim2(1, -40, 0, 20)} // Reduced height from 24 to 20
						alignment={Enum.TextXAlignment.Left}
					/>

					<IconButton
						icon="✕"
						onClick={handleClose}
						size={new UDim2(0, 20, 0, 20)} // Smaller close button
						position={new UDim2(1, -24, 0, 0)}
					/>

					{/* Content area with minimal spacing */}
					<ContainerFrame
						backgroundTransparency={1}
						size={new UDim2(1, 0, 1, -24)} // Account for title area
						position={new UDim2(0, 0, 0, 24)} // Start below title
						padding={0} // No additional padding
						borderThickness={0}
						autoSize={Enum.AutomaticSize.None}
					>
						<ErrorBoundary children={props.children} />
					</ContainerFrame>
				</ContainerFrame>
			</textbutton>
		</Overlay>
	);
}
