-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local React = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "react")
local ActionBarDemo = TS.import(script, script.Parent.Parent, "gui", "ActionBarDemo").ActionBarDemo
local BottomLeftGrid = TS.import(script, script.Parent.Parent, "gui", "BottomLeftGrid").BottomLeftGrid
local ErrorBoundary = TS.import(script, game:GetService("ReplicatedStorage"), "core").ErrorBoundary
--[[
	*
	 * Simple component test to verify that components can be instantiated without crashing
	 * This helps catch constructor and initialization errors early
	 
]]
local function ComponentTest()
	print("🧪 ComponentTest: Starting component test...")
	local testResults, setTestResults = React.useState({})
	React.useEffect(function()
		local results = {}
		TS.try(function()
			-- Test 1: ActionBarDemo instantiation
			print("🧪 Testing ActionBarDemo instantiation...")
			table.insert(results, "✅ ActionBarDemo: Can be imported")
		end, function(error)
			local _arg0 = `❌ ActionBarDemo: Import failed - {error}`
			table.insert(results, _arg0)
		end)
		TS.try(function()
			-- Test 2: BottomLeftGrid instantiation
			print("🧪 Testing BottomLeftGrid instantiation...")
			table.insert(results, "✅ BottomLeftGrid: Can be imported")
		end, function(error)
			local _arg0 = `❌ BottomLeftGrid: Import failed - {error}`
			table.insert(results, _arg0)
		end)
		TS.try(function()
			-- Test 3: ErrorBoundary instantiation
			print("🧪 Testing ErrorBoundary instantiation...")
			table.insert(results, "✅ ErrorBoundary: Can be imported")
		end, function(error)
			local _arg0 = `❌ ErrorBoundary: Import failed - {error}`
			table.insert(results, _arg0)
		end)
		setTestResults(results)
		print("🧪 ComponentTest: All instantiation tests completed")
	end, {})
	local _exp = React.createElement("textlabel", {
		Text = "Component Test Results",
		Size = UDim2.new(0, 300, 0, 30),
		Position = UDim2.new(0.5, 0, 0, 50),
		AnchorPoint = Vector2.new(0.5, 0),
		BackgroundTransparency = 1,
		TextColor3 = Color3.fromRGB(255, 255, 255),
		TextSize = 16,
		Font = Enum.Font.SourceSansBold,
		TextXAlignment = Enum.TextXAlignment.Center,
	})
	local _exp_1 = React.createElement("uilistlayout", {
		SortOrder = Enum.SortOrder.LayoutOrder,
		FillDirection = Enum.FillDirection.Vertical,
		Padding = UDim.new(0, 2),
	})
	-- ▼ ReadonlyArray.map ▼
	local _newValue = table.create(#testResults)
	local _callback = function(result, index)
		local _attributes = {
			key = index,
			Text = result,
			Size = UDim2.new(1, -10, 0, 20),
			BackgroundTransparency = 1,
		}
		local _value = (string.find(result, "✅"))
		_attributes.TextColor3 = if _value ~= 0 and _value == _value and _value then Color3.fromRGB(0, 255, 0) else Color3.fromRGB(255, 0, 0)
		_attributes.TextSize = 12
		_attributes.Font = Enum.Font.SourceSans
		_attributes.TextXAlignment = Enum.TextXAlignment.Left
		_attributes.LayoutOrder = index
		return React.createElement("textlabel", _attributes)
	end
	for _k, _v in testResults do
		_newValue[_k] = _callback(_v, _k - 1, testResults)
	end
	-- ▲ ReadonlyArray.map ▲
	return React.createElement(ErrorBoundary, {
		onError = function(err, errorId)
			warn(`[ComponentTest] Error in test component: {err} (ID: {errorId})`)
		end,
	}, React.createElement("frame", {
		BackgroundTransparency = 1,
		Size = UDim2.new(1, 0, 1, 0),
		Position = UDim2.new(0, 0, 0, 0),
	}, _exp, React.createElement("scrollingframe", {
		Size = UDim2.new(0, 400, 0, 200),
		Position = UDim2.new(0.5, 0, 0, 100),
		AnchorPoint = Vector2.new(0.5, 0),
		BackgroundColor3 = Color3.fromRGB(40, 40, 40),
		BackgroundTransparency = 0.2,
		BorderSizePixel = 0,
		ScrollBarThickness = 8,
		CanvasSize = UDim2.new(0, 0, 0, #testResults * 25),
	}, _exp_1, _newValue), React.createElement(ErrorBoundary, {
		onError = function(err, errorId)
			warn(`[ComponentTest] Error in ActionBarDemo test: {err} (ID: {errorId})`)
		end,
	}, React.createElement(ActionBarDemo, {
		layoutOrder = 1,
	})), React.createElement(ErrorBoundary, {
		onError = function(err, errorId)
			warn(`[ComponentTest] Error in BottomLeftGrid test: {err} (ID: {errorId})`)
		end,
	}, React.createElement(BottomLeftGrid, {
		onTestClick = function()
			return print("Test click")
		end,
		onHelloClick = function()
			return print("Hello click")
		end,
	}))))
end
return {
	ComponentTest = ComponentTest,
}
