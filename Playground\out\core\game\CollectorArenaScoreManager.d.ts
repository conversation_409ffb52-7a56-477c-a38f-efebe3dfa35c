export interface GameScore {
    playerName: string;
    score: number;
    coinsCollected: number;
    enemiesDefeated: number;
    gameTime: number;
    timestamp: number;
}
export declare class CollectorArenaScoreManager {
    private static instance;
    private localScores;
    private personalBest?;
    private constructor();
    static getInstance(): CollectorArenaScoreManager;
    saveScore(gameScore: GameScore): Promise<boolean>;
    getPersonalBest(): Promise<GameScore | undefined>;
    getRecentGames(): Promise<GameScore[]>;
}
