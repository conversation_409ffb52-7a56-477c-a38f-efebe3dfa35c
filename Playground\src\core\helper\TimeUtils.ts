/**
 * Time and timing utilities for game development
 */
export class TimeUtils {
	private static timers = new Map<string, { callback: () => void; interval: number; lastRun: number }>();
	private static frameCallbacks = new Set<() => void>();
	private static gameStartTime = tick();

	/**
	 * Get current game time in seconds since game start
	 */
	static getGameTime(): number {
		return tick() - this.gameStartTime;
	}

	/**
	 * Get current Unix timestamp
	 */
	static getUnixTime(): number {
		return os.time();
	}

	/**
	 * Get formatted current date/time
	 */
	static getCurrentDateTime(): string {
		return os.date("%Y-%m-%d %H:%M:%S");
	}

	/**
	 * Get formatted current date
	 */
	static getCurrentDate(): string {
		return os.date("%Y-%m-%d");
	}

	/**
	 * Get formatted current time
	 */
	static getCurrentTime(): string {
		return os.date("%H:%M:%S");
	}

	/**
	 * Convert seconds to human readable duration
	 */
	static formatDuration(seconds: number): string {
		const days = math.floor(seconds / 86400);
		const hours = math.floor((seconds % 86400) / 3600);
		const minutes = math.floor((seconds % 3600) / 60);
		const secs = math.floor(seconds % 60);

		const parts: string[] = [];
		if (days > 0) parts.push(`${days}d`);
		if (hours > 0) parts.push(`${hours}h`);
		if (minutes > 0) parts.push(`${minutes}m`);
		if (secs > 0 || parts.size() === 0) parts.push(`${secs}s`);

		return parts.join(" ");
	}

	/**
	 * Format duration for display (e.g., "2 hours 30 minutes")
	 */
	static formatDurationLong(seconds: number): string {
		const days = math.floor(seconds / 86400);
		const hours = math.floor((seconds % 86400) / 3600);
		const minutes = math.floor((seconds % 3600) / 60);
		const secs = math.floor(seconds % 60);

		const parts: string[] = [];
		if (days > 0) parts.push(`${days} ${days === 1 ? "day" : "days"}`);
		if (hours > 0) parts.push(`${hours} ${hours === 1 ? "hour" : "hours"}`);
		if (minutes > 0) parts.push(`${minutes} ${minutes === 1 ? "minute" : "minutes"}`);
		if (secs > 0 && parts.size() === 0) parts.push(`${secs} ${secs === 1 ? "second" : "seconds"}`);

		if (parts.size() === 0) return "0 seconds";
		if (parts.size() === 1) return parts[0];
		if (parts.size() === 2) return `${parts[0]} and ${parts[1]}`;
		
		// More than 2 parts: join with commas and "and"
		const lastPart = parts.pop()!;
		return `${parts.join(", ")} and ${lastPart}`;
	}

	/**
	 * Check if enough time has passed since last check
	 */
	static hasElapsed(key: string, requiredInterval: number): boolean {
		const now = tick();
		const lastTime = this.timers.get(key)?.lastRun || 0;
		
		if (now - lastTime >= requiredInterval) {
			this.timers.set(key, { 
				callback: () => {}, 
				interval: requiredInterval, 
				lastRun: now 
			});
			return true;
		}
		
		return false;
	}

	/**
	 * Simple interval timer (manual update required)
	 */
	static setInterval(key: string, callback: () => void, interval: number): void {
		this.timers.set(key, {
			callback,
			interval,
			lastRun: tick()
		});
	}

	/**
	 * Clear interval timer
	 */
	static clearInterval(key: string): void {
		this.timers.delete(key);
	}

	/**
	 * Update all interval timers (call this in your main loop)
	 */
	static updateTimers(): void {
		const now = tick();
		
		for (const [key, timer] of this.timers) {
			if (now - timer.lastRun >= timer.interval) {
				timer.callback();
				timer.lastRun = now;
			}
		}
	}

	/**
	 * Register a callback to run every frame
	 */
	static onFrame(callback: () => void): void {
		this.frameCallbacks.add(callback);
	}

	/**
	 * Remove frame callback
	 */
	static offFrame(callback: () => void): void {
		this.frameCallbacks.delete(callback);
	}

	/**
	 * Call all frame callbacks (call this in RunService.Heartbeat)
	 */
	static updateFrame(): void {
		for (const callback of this.frameCallbacks) {
			try {
				callback();
			} catch (error) {
				warn(`Frame callback error: ${error}`);
			}
		}
	}

	/**
	 * Create a countdown timer
	 */
	static createCountdown(duration: number, onTick?: (remaining: number) => void, onComplete?: () => void): {
		getRemaining: () => number;
		isComplete: () => boolean;
		cancel: () => void;
	} {
		const startTime = tick();
		let cancelled = false;

		const countdown = {
			getRemaining: () => {
				if (cancelled) return 0;
				const elapsed = tick() - startTime;
				return math.max(0, duration - elapsed);
			},
			isComplete: () => {
				return countdown.getRemaining() <= 0 && !cancelled;
			},
			cancel: () => {
				cancelled = true;
			}
		};

		// Set up tick callback if provided
		if (onTick) {
			const tickKey = `countdown_${tostring(countdown)}`;
			this.setInterval(tickKey, () => {
				if (cancelled) {
					this.clearInterval(tickKey);
					return;
				}

				const remaining = countdown.getRemaining();
				onTick(remaining);

				if (remaining <= 0) {
					this.clearInterval(tickKey);
					if (onComplete) {
						onComplete();
					}
				}
			}, 0.1); // Update every 100ms
		}

		return countdown;
	}

	/**
	 * Create a stopwatch
	 */
	static createStopwatch(): {
		start: () => void;
		stop: () => void;
		reset: () => void;
		getElapsed: () => number;
		isRunning: () => boolean;
	} {
		let startTime = 0;
		let totalElapsed = 0;
		let running = false;

		return {
			start: () => {
				if (!running) {
					startTime = tick();
					running = true;
				}
			},
			stop: () => {
				if (running) {
					totalElapsed += tick() - startTime;
					running = false;
				}
			},
			reset: () => {
				totalElapsed = 0;
				running = false;
			},
			getElapsed: () => {
				if (running) {
					return totalElapsed + (tick() - startTime);
				}
				return totalElapsed;
			},
			isRunning: () => running
		};
	}

	/**
	 * Calculate FPS based on frame time
	 */
	static calculateFPS(deltaTime: number): number {
		return deltaTime > 0 ? math.floor(1 / deltaTime) : 0;
	}

	/**
	 * Create an FPS counter
	 */
	static createFPSCounter(): {
		update: (deltaTime: number) => void;
		getFPS: () => number;
		getAverageFPS: () => number;
	} {
		const frameTimes: number[] = [];
		const maxSamples = 60; // Keep last 60 frame times
		let currentFPS = 0;

		return {
			update: (deltaTime: number) => {
				frameTimes.push(deltaTime);
				if (frameTimes.size() > maxSamples) {
					frameTimes.shift();
				}
				currentFPS = this.calculateFPS(deltaTime);
			},
			getFPS: () => currentFPS,
			getAverageFPS: () => {
				if (frameTimes.size() === 0) return 0;
				const avgDelta = frameTimes.reduce((sum, time) => sum + time, 0) / frameTimes.size();
				return this.calculateFPS(avgDelta);
			}
		};
	}

	/**
	 * Wait for a condition to be true (with timeout)
	 */
	static async waitFor(condition: () => boolean, timeout = 10, checkInterval = 0.1): Promise<boolean> {
		const startTime = tick();
		
		while (tick() - startTime < timeout) {
			if (condition()) {
				return true;
			}
			task.wait(checkInterval);
		}
		
		return false;
	}

	/**
	 * Throttle function calls (limit frequency)
	 */
	static throttle<T extends unknown[]>(
		key: string, 
		func: (...args: T) => void, 
		limit: number
	): (...args: T) => void {
		let lastCall = 0;

		return (...args: T) => {
			const now = tick();
			if (now - lastCall >= limit) {
				lastCall = now;
				func(...args);
			}
		};
	}

	/**
	 * Debounce function calls (delay until no calls for specified time)
	 */
	static debounce<T extends unknown[]>(
		key: string,
		func: (...args: T) => void,
		delay: number
	): (...args: T) => void {
		let timeoutThread: thread | undefined;

		return (...args: T) => {
			if (timeoutThread !== undefined) {
				task.cancel(timeoutThread);
			}

			timeoutThread = task.delay(delay, () => {
				func(...args);
				timeoutThread = undefined;
			});
		};
	}

	/**
	 * Get time until next hour/day/etc
	 */
	static getTimeUntilNext(unit: "minute" | "hour" | "day"): number {
		const now = os.time();
		const date = os.date("*t", now) as {
			year: number;
			month: number;
			day: number;
			hour: number;
			min: number;
			sec: number;
		};

		let nextTime: number;

		switch (unit) {
			case "minute":
				nextTime = os.time({
					year: date.year,
					month: date.month,
					day: date.day,
					hour: date.hour,
					min: date.min + 1,
					sec: 0
				});
				break;
			case "hour":
				nextTime = os.time({
					year: date.year,
					month: date.month,
					day: date.day,
					hour: date.hour + 1,
					min: 0,
					sec: 0
				});
				break;
			case "day":
				nextTime = os.time({
					year: date.year,
					month: date.month,
					day: date.day + 1,
					hour: 0,
					min: 0,
					sec: 0
				});
				break;
		}

		return nextTime - now;
	}

	/**
	 * Clean up all timers and callbacks
	 */
	static cleanup(): void {
		this.timers.clear();
		this.frameCallbacks.clear();
	}
}