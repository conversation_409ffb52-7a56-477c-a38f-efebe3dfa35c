import { IErrorHandlingService } from "../interfaces/IErrorHandlingService";
import { BaseService } from "../../foundation/BaseService";
import { ErrorEntry, ErrorSeverity, ErrorCategory, ErrorContext } from "../types/ErrorTypes";
import { Result } from "../../foundation/types/Result";
import { Error, createError } from "../../foundation/types/RobloxError";

export class ErrorHandlingService extends BaseService implements IErrorHandlingService {
	private errors = new Map<string, ErrorEntry>();
	private errorIdCounter: number;

	constructor() {
		super("ErrorHandlingService");
		this.errorIdCounter = 0;
	}

	protected async onInitialize(): Promise<Result<void, Error>> {
		print("🚨 ErrorHandlingService: Initializing...");
		return Result.ok(undefined);
	}

	protected async onShutdown(): Promise<Result<void, Error>> {
		this.errors.clear();
		return Result.ok(undefined);
	}

	public logApplicationError(
		message: string,
		severity: ErrorSeverity,
		category: ErrorCategory,
		context?: ErrorContext,
	): string {
		this.errorIdCounter = this.errorIdCounter + 1;
		const errorId = `error_${this.errorIdCounter}`;

		const errorEntry: ErrorEntry = {
			id: errorId,
			message,
			severity,
			category,
			timestamp: tick(),
			context,
			resolved: false,
		};

		this.errors.set(errorId, errorEntry);

		// Log to console based on severity
		switch (severity) {
			case ErrorSeverity.CRITICAL:
				error(`[CRITICAL] ${category}: ${message}`);
				break;
			case ErrorSeverity.HIGH:
				warn(`[HIGH] ${category}: ${message}`);
				break;
			case ErrorSeverity.MEDIUM:
				warn(`[MEDIUM] ${category}: ${message}`);
				break;
			case ErrorSeverity.LOW:
				print(`[LOW] ${category}: ${message}`);
				break;
		}

		return errorId;
	}

	public getErrors(severity?: ErrorSeverity, category?: ErrorCategory): ErrorEntry[] {
		const errorList: ErrorEntry[] = [];

		for (const [, errorEntry] of this.errors) {
			if (severity && errorEntry.severity !== severity) continue;
			if (category && errorEntry.category !== category) continue;
			errorList.push(errorEntry);
		}

		return errorList;
	}

	public resolveError(errorId: string, resolution: string): Result<void, Error> {
		const errorEntry = this.errors.get(errorId);
		if (!errorEntry) {
			return Result.err(createError(`Error with ID '${errorId}' not found`));
		}

		errorEntry.resolved = true;
		errorEntry.resolution = resolution;

		print(`✅ Error resolved: ${errorId} - ${resolution}`);
		return Result.ok(undefined);
	}

	public clearResolvedErrors(): void {
		for (const [id, errorEntry] of this.errors) {
			if (errorEntry.resolved) {
				this.errors.delete(id);
			}
		}
	}

	public getErrorCount(severity?: ErrorSeverity): number {
		if (!severity) {
			return this.errors.size();
		}

		let count = 0;
		for (const [, errorEntry] of this.errors) {
			if (errorEntry.severity === severity) {
				count++;
			}
		}
		return count;
	}

	// Convenience methods for common error logging
	public logNetworkError(message: string, context?: ErrorContext): string {
		return this.logApplicationError(message, ErrorSeverity.HIGH, ErrorCategory.NETWORK, context);
	}

	public logValidationError(message: string, context?: ErrorContext): string {
		return this.logApplicationError(message, ErrorSeverity.MEDIUM, ErrorCategory.VALIDATION, context);
	}

	public logSystemError(message: string, context?: ErrorContext): string {
		return this.logApplicationError(message, ErrorSeverity.CRITICAL, ErrorCategory.SYSTEM, context);
	}

	public logUserInputError(message: string, context?: ErrorContext): string {
		return this.logApplicationError(message, ErrorSeverity.LOW, ErrorCategory.USER_INPUT, context);
	}
}
