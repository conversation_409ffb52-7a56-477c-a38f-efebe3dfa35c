-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local Result = TS.import(script, game:GetService("ReplicatedStorage"), "core", "foundation", "types", "Result").Result
local createError = TS.import(script, game:GetService("ReplicatedStorage"), "core", "foundation", "types", "RobloxError").createError
local GameMode = {
	Playground = "Playground",
	CollectorArena = "CollectorArena",
}
local GameModeManager
do
	GameModeManager = setmetatable({}, {
		__tostring = function()
			return "GameModeManager"
		end,
	})
	GameModeManager.__index = GameModeManager
	function GameModeManager.new(...)
		local self = setmetatable({}, GameModeManager)
		return self:constructor(...) or self
	end
	function GameModeManager:constructor()
		-- Simple state management without StateManager for now
		self.gameControllers = {}
		self.currentMode = GameMode.Playground
		self.isGameActive = false
		self.canSwitchMode = true
		-- Simple state management without StateManager for now
	end
	function GameModeManager:getInstance()
		if not GameModeManager.instance then
			GameModeManager.instance = GameModeManager.new()
		end
		return GameModeManager.instance
	end
	function GameModeManager:registerGameController(mode, controller)
		local _gameControllers = self.gameControllers
		local _mode = mode
		local _controller = controller
		_gameControllers[_mode] = _controller
		print(`🎮 [GameModeManager] Registered controller for {mode}: {controller:getName()}`)
	end
	GameModeManager.switchToMode = TS.async(function(self, mode)
		if not self.canSwitchMode then
			return Result:err(createError("Cannot switch game mode while a game is active"))
		end
		if self.currentMode == mode then
			return Result:ok(nil)
		end
		local _exitType, _returns = TS.try(function()
			-- Stop current game if active
			if self.currentController and self.isGameActive then
				local stopResult = TS.await(self.currentController:stop())
				if stopResult:isError() then
					return TS.TRY_RETURN, { Result:err(createError(`Failed to stop current game: {stopResult:getError().message}`)) }
				end
			end
			-- Cleanup current controller
			if self.currentController then
				local cleanupResult = TS.await(self.currentController:cleanup())
				if cleanupResult:isError() then
					warn(`⚠️ [GameModeManager] Failed to cleanup current controller: {cleanupResult:getError().message}`)
				end
			end
			-- Switch to new controller
			local _gameControllers = self.gameControllers
			local _mode = mode
			local newController = _gameControllers[_mode]
			if not newController then
				return TS.TRY_RETURN, { Result:err(createError(`No controller registered for game mode: {mode}`)) }
			end
			self.currentController = newController
			self.currentMode = mode
			self.isGameActive = false
			self.canSwitchMode = true
			print(`🎮 [GameModeManager] Switched to {mode} mode`)
			return TS.TRY_RETURN, { Result:ok(nil) }
		end, function(error)
			return TS.TRY_RETURN, { Result:err(createError(`Failed to switch game mode: {error}`)) }
		end)
		if _exitType then
			return unpack(_returns)
		end
	end)
	GameModeManager.startCurrentGame = TS.async(function(self)
		if self.isGameActive then
			return Result:err(createError("A game is already active"))
		end
		if not self.currentController then
			return Result:err(createError("No game controller is active"))
		end
		local _exitType, _returns = TS.try(function()
			self.canSwitchMode = false
			local startResult = TS.await(self.currentController:start())
			if startResult:isError() then
				-- Restore ability to switch modes if start failed
				self.canSwitchMode = true
				return TS.TRY_RETURN, { Result:err(createError(`Failed to start game: {startResult:getError().message}`)) }
			end
			self.isGameActive = true
			self.canSwitchMode = false
			print(`🎮 [GameModeManager] Started {self.currentMode} game`)
			return TS.TRY_RETURN, { Result:ok(nil) }
		end, function(error)
			-- Restore ability to switch modes if error occurred
			self.canSwitchMode = true
			return TS.TRY_RETURN, { Result:err(createError(`Failed to start game: {error}`)) }
		end)
		if _exitType then
			return unpack(_returns)
		end
	end)
	GameModeManager.stopCurrentGame = TS.async(function(self)
		if not self.isGameActive then
			return Result:err(createError("No game is currently active"))
		end
		if not self.currentController then
			return Result:err(createError("No game controller is active"))
		end
		local _exitType, _returns = TS.try(function()
			local stopResult = TS.await(self.currentController:stop())
			if stopResult:isError() then
				return TS.TRY_RETURN, { Result:err(createError(`Failed to stop game: {stopResult:getError().message}`)) }
			end
			self.isGameActive = false
			self.canSwitchMode = true
			print(`🎮 [GameModeManager] Stopped {self.currentMode} game`)
			return TS.TRY_RETURN, { Result:ok(nil) }
		end, function(error)
			return TS.TRY_RETURN, { Result:err(createError(`Failed to stop game: {error}`)) }
		end)
		if _exitType then
			return unpack(_returns)
		end
	end)
	function GameModeManager:getCurrentMode()
		return self.currentMode
	end
	function GameModeManager:isGameActiveState()
		return self.isGameActive
	end
	function GameModeManager:canSwitchModeState()
		return self.canSwitchMode
	end
	function GameModeManager:getAvailableModes()
		local modes = {}
		for mode in self.gameControllers do
			table.insert(modes, mode)
		end
		return modes
	end
end
return {
	GameMode = GameMode,
	GameModeManager = GameModeManager,
}
