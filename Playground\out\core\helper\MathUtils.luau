-- Compiled with roblox-ts v3.0.0
--[[
	*
	 * Advanced mathematical utilities for game development
	 
]]
local MathUtils
do
	MathUtils = setmetatable({}, {
		__tostring = function()
			return "MathUtils"
		end,
	})
	MathUtils.__index = MathUtils
	function MathUtils.new(...)
		local self = setmetatable({}, MathUtils)
		return self:constructor(...) or self
	end
	function MathUtils:constructor()
	end
	function MathUtils:lerp(a, b, t)
		return a + (b - a) * t
	end
	function MathUtils:lerpVector3(a, b, t)
		return Vector3.new(self:lerp(a.X, b.X, t), self:lerp(a.Y, b.Y, t), self:lerp(a.Z, b.Z, t))
	end
	function MathUtils:clamp(value, min, max)
		return math.max(min, math.min(max, value))
	end
	function MathUtils:smoothStep(t)
		return t * t * (3 - 2 * t)
	end
	function MathUtils:smootherStep(t)
		return t * t * t * (t * (t * 6 - 15) + 10)
	end
	function MathUtils:map(value, inMin, inMax, outMin, outMax)
		return outMin + (outMax - outMin) * ((value - inMin) / (inMax - inMin))
	end
	function MathUtils:distance(a, b)
		local _a = a
		local _b = b
		return (_a - _b).Magnitude
	end
	function MathUtils:distanceSquared(a, b)
		local _a = a
		local _b = b
		local diff = _a - _b
		return diff.X * diff.X + diff.Y * diff.Y + diff.Z * diff.Z
	end
	function MathUtils:isInSphere(point, center, radius)
		return self:distanceSquared(point, center) <= radius * radius
	end
	function MathUtils:isInBox(point, boxCenter, boxSize)
		local halfSize = boxSize / 2
		local _point = point
		local _boxCenter = boxCenter
		local diff = _point - _boxCenter
		return math.abs(diff.X) <= halfSize.X and math.abs(diff.Y) <= halfSize.Y and math.abs(diff.Z) <= halfSize.Z
	end
	function MathUtils:randomPointInSphere(center, radius)
		-- Use rejection sampling for uniform distribution
		local point
		repeat
			do
				point = Vector3.new(math.random(-radius, radius), math.random(-radius, radius), math.random(-radius, radius))
			end
		until not (point.Magnitude > radius)
		local _center = center
		local _point = point
		return _center + _point
	end
	function MathUtils:randomPointOnSphere(center, radius)
		-- Use spherical coordinates for uniform distribution
		local theta = math.random() * math.pi * 2
		local phi = math.acos(2 * math.random() - 1)
		local x = radius * math.sin(phi) * math.cos(theta)
		local y = radius * math.sin(phi) * math.sin(theta)
		local z = radius * math.cos(phi)
		local _center = center
		local _vector3 = Vector3.new(x, y, z)
		return _center + _vector3
	end
	function MathUtils:deg2rad(degrees)
		return degrees * (math.pi / 180)
	end
	function MathUtils:rad2deg(radians)
		return radians * (180 / math.pi)
	end
	function MathUtils:angleBetween(a, b)
		local dot = a.Unit:Dot(b.Unit)
		return math.acos(math.clamp(dot, -1, 1))
	end
	function MathUtils:rotateAroundY(vector, angleRadians)
		local cos = math.cos(angleRadians)
		local sin = math.sin(angleRadians)
		return Vector3.new(vector.X * cos - vector.Z * sin, vector.Y, vector.X * sin + vector.Z * cos)
	end
	function MathUtils:round(value, decimals)
		if decimals == nil then
			decimals = 0
		end
		local multiplier = math.pow(10, decimals)
		return math.floor(value * multiplier + 0.5) / multiplier
	end
	function MathUtils:approximately(a, b, epsilon)
		if epsilon == nil then
			epsilon = 0.001
		end
		return math.abs(a - b) < epsilon
	end
	function MathUtils:wrapAngle(angle)
		while angle > math.pi do
			angle -= 2 * math.pi
		end
		while angle < -math.pi do
			angle += 2 * math.pi
		end
		return angle
	end
	function MathUtils:angularDistance(from, to)
		local diff = self:wrapAngle(to - from)
		return diff
	end
end
return {
	MathUtils = MathUtils,
}
