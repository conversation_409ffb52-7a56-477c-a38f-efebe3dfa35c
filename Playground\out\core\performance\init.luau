-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local exports = {}
local _PerformanceMonitor = TS.import(script, game:GetService("ReplicatedStorage"), "core", "performance", "PerformanceMonitor")
exports.PerformanceMonitor = _PerformanceMonitor.PerformanceMonitor
exports.Performance = _PerformanceMonitor.Performance
local _ObjectPoolManager = TS.import(script, game:GetService("ReplicatedStorage"), "core", "performance", "ObjectPoolManager")
exports.ObjectPoolManager = _ObjectPoolManager.ObjectPoolManager
exports.ObjectPooling = _ObjectPoolManager.ObjectPooling
exports.PoolableBase = _ObjectPoolManager.PoolableBase
return exports
