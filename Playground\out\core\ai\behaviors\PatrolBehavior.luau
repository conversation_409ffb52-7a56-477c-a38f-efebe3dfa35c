-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local PositionHelper = TS.import(script, game:GetService("ReplicatedStorage"), "core", "helper", "PositionHelper").PositionHelper
local PatrolBehavior
do
	PatrolBehavior = setmetatable({}, {
		__tostring = function()
			return "PatrolBehavior"
		end,
	})
	PatrolBehavior.__index = PatrolBehavior
	function PatrolBehavior.new(...)
		local self = setmetatable({}, PatrolBehavior)
		return self:constructor(...) or self
	end
	function PatrolBehavior:constructor()
		self.name = "Patrol"
		self.priority = 3
	end
	function PatrolBehavior:canExecute(context)
		-- Only patrol when no target is present
		return not context.target
	end
	function PatrolBehavior:execute(context)
		local patrolPoints = (context.blackboard.patrolPoints) or {}
		local _condition = (context.blackboard.currentPatrolIndex)
		if not (_condition ~= 0 and _condition == _condition and _condition) then
			_condition = 0
		end
		local currentPatrolIndex = _condition
		local _condition_1 = (context.blackboard.patrolPauseTime)
		if not (_condition_1 ~= 0 and _condition_1 == _condition_1 and _condition_1) then
			_condition_1 = 0
		end
		local patrolPauseTime = _condition_1
		local isPatrolPaused = (context.blackboard.isPatrolPaused) or false
		local patrolPointsGenerated = (context.blackboard.patrolPointsGenerated) or false
		-- Generate patrol points if none exist and not already generated
		if #patrolPoints == 0 and not patrolPointsGenerated then
			self:generateSmartPatrolPoints(context)
			context.blackboard.patrolPointsGenerated = true
			return {
				success = true,
				completed = false,
			}
		end
		-- Handle patrol pause at waypoints
		if isPatrolPaused then
			context.blackboard.patrolPauseTime = patrolPauseTime + context.deltaTime
			-- Look around while paused
			self:lookAround(context)
			if patrolPauseTime > 2 then
				context.blackboard.isPatrolPaused = false
				context.blackboard.patrolPauseTime = 0
				context.blackboard.currentPatrolIndex = (currentPatrolIndex + 1) % #patrolPoints
				print(`🚶 {context.entityId} resuming patrol to point {context.blackboard.currentPatrolIndex}`)
			end
			return {
				success = true,
				completed = false,
			}
		end
		local targetPoint = patrolPoints[currentPatrolIndex + 1]
		local distance = (context.position - targetPoint).Magnitude
		if distance <= 3 then
			-- Reached patrol point - start pause
			context.blackboard.isPatrolPaused = true
			context.blackboard.patrolPauseTime = 0
			print(`🚶 {context.entityId} reached patrol point {currentPatrolIndex}, pausing`)
		else
			-- Move towards current patrol point
			self:moveTowards(context, targetPoint)
			-- Face movement direction
			PositionHelper:lookAt(context.entity, targetPoint)
		end
		-- Update patrol statistics
		local _condition_2 = context.blackboard.totalPatrolTime
		if not (_condition_2 ~= 0 and _condition_2 == _condition_2 and _condition_2) then
			_condition_2 = 0
		end
		local totalPatrolTime = _condition_2 + context.deltaTime
		context.blackboard.totalPatrolTime = totalPatrolTime
		return {
			success = true,
			completed = false,
		}
	end
	function PatrolBehavior:onEnter(context)
		print(`🚶 {context.entityId} starting patrol`)
		context.blackboard.patrolStartTime = tick()
		-- Reset patrol state
		context.blackboard.isPatrolPaused = false
		context.blackboard.patrolPauseTime = 0
		context.blackboard.lookDirection = 0
		context.blackboard.patrolPointsGenerated = false
	end
	function PatrolBehavior:onExit(context)
		local _exp = tick()
		local _condition = context.blackboard.patrolStartTime
		if not (_condition ~= 0 and _condition == _condition and _condition) then
			_condition = tick()
		end
		local patrolTime = _exp - _condition
		print(`🚶 {context.entityId} ended patrol after {math.floor(patrolTime)}s`)
	end
	function PatrolBehavior:generateSmartPatrolPoints(context)
		local patrolPoints = {}
		local basePosition = context.position
		local _condition = context.blackboard.patrolRadius
		if not (_condition ~= 0 and _condition == _condition and _condition) then
			_condition = 25
		end
		local patrolRadius = _condition
		local numPoints = 6
		-- Generate points in a more natural pattern
		for i = 0, numPoints - 1 do
			-- Use slightly randomized angles for more natural movement
			local baseAngle = (i / numPoints) * math.pi * 2
			local angleVariation = (math.random() - 0.5) * 0.5
			local angle = baseAngle + angleVariation
			-- Vary the radius for each point
			local radiusVariation = 0.5 + math.random() * 0.8
			local actualRadius = patrolRadius * radiusVariation
			local x = basePosition.X + math.cos(angle) * actualRadius
			local z = basePosition.Z + math.sin(angle) * actualRadius
			-- Try to find a valid position (basic terrain adaptation)
			local candidatePosition = Vector3.new(x, basePosition.Y, z)
			local validPosition = self:findValidPatrolPosition(candidatePosition, basePosition)
			table.insert(patrolPoints, validPosition)
		end
		-- Randomize the starting point
		local startIndex = math.random(0, numPoints - 1)
		context.blackboard.patrolPoints = patrolPoints
		context.blackboard.currentPatrolIndex = startIndex
		context.blackboard.originalPatrolCenter = basePosition
		print(`🚶 Generated {numPoints} patrol points around {basePosition}, starting at point {startIndex}`)
	end
	function PatrolBehavior:findValidPatrolPosition(candidatePosition, fallbackPosition)
		-- Simple validation - in a real implementation, this would check for terrain, obstacles, etc.
		-- For now, just ensure the Y coordinate stays reasonable
		local _ = game:GetService("Workspace")
		local _candidatePosition = candidatePosition
		local _vector3 = Vector3.new(0, 10, 0)
		local raycastResult = _:Raycast(_candidatePosition + _vector3, Vector3.new(0, -20, 0))
		if raycastResult then
			return Vector3.new(candidatePosition.X, raycastResult.Position.Y + 3, candidatePosition.Z)
		end
		return candidatePosition
	end
	function PatrolBehavior:moveTowards(context, targetPosition)
		local clearTarget = PositionHelper:findClearPath(context.position, targetPosition, { context.entity })
		if context.entity:IsA("Model") and context.entity.PrimaryPart then
			local humanoid = context.entity:FindFirstChild("Humanoid")
			if humanoid then
				-- Adjust patrol speed - slower than following or combat
				local _condition = context.blackboard.moveSpeed
				if not (_condition ~= 0 and _condition == _condition and _condition) then
					_condition = 16
				end
				local baseSpeed = _condition
				humanoid.WalkSpeed = baseSpeed * 0.6
				humanoid:MoveTo(clearTarget)
			else
				self:smoothMoveTo(context.entity.PrimaryPart, clearTarget, context.deltaTime)
			end
		elseif context.entity:IsA("BasePart") then
			self:smoothMoveTo(context.entity, clearTarget, context.deltaTime)
		end
	end
	function PatrolBehavior:lookAround(context)
		-- Simulate looking around behavior while paused
		local _condition = (context.blackboard.lookDirection)
		if not (_condition ~= 0 and _condition == _condition and _condition) then
			_condition = 0
		end
		local lookDirection = _condition
		local lookSpeed = 2
		local newLookDirection = lookDirection + lookSpeed * context.deltaTime
		-- Create a look target in front of the entity
		local lookDistance = 10
		local _position = context.position
		local _vector3 = Vector3.new(math.cos(newLookDirection) * lookDistance, 0, math.sin(newLookDirection) * lookDistance)
		local lookTarget = _position + _vector3
		PositionHelper:lookAt(context.entity, lookTarget)
		context.blackboard.lookDirection = newLookDirection
	end
	function PatrolBehavior:smoothMoveTo(part, targetPosition, deltaTime)
		local currentPosition = part.Position
		local direction = targetPosition - currentPosition
		local distance = direction.Magnitude
		if distance > 0.1 then
			local moveSpeed = 12
			local maxMove = moveSpeed * deltaTime
			local moveDistance = math.min(maxMove, distance)
			local _arg0 = direction.Unit * moveDistance
			local newPosition = currentPosition + _arg0
			local lookDirection = direction.Unit
			local newCFrame = CFrame.lookAt(newPosition, newPosition + lookDirection)
			part.CFrame = newCFrame
		end
	end
end
return {
	PatrolBehavior = PatrolBehavior,
}
