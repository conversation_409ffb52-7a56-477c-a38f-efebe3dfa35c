-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local React = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "react")
local _design = TS.import(script, game:GetService("ReplicatedStorage"), "core", "design")
local COLORS = _design.COLORS
local SIZES = _design.SIZES
local BORDER_RADIUS = _design.BORDER_RADIUS
local IconButton = TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "button").IconButton
local Label = TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "label", "Label").Label
local ContainerFrame = TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "frame").ContainerFrame
local Overlay = TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "overlay", "Overlay").Overlay
local ErrorBoundary = TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "error-boundary").ErrorBoundary
local function Modal(props)
	local isAnimating, setIsAnimating = React.useState(false)
	local _condition = props.width
	if _condition == nil then
		_condition = 400
	end
	local width = _condition
	local _condition_1 = props.height
	if _condition_1 == nil then
		_condition_1 = 300
	end
	local height = _condition_1
	-- Handle open/close animations
	React.useEffect(function()
		if props.isOpen then
			setIsAnimating(true)
		end
	end, { props.isOpen })
	local handleClose = function()
		setIsAnimating(false)
		-- Delay actual close to allow animation
		task.delay(0.2, function()
			props.onClose()
		end)
	end
	if not props.isOpen and not isAnimating then
		return React.createElement(React.Fragment)
	end
	local modalScale = if isAnimating and props.isOpen then 1 else 0.9
	local modalTransparency = if isAnimating and props.isOpen then 0 else 0.3
	return React.createElement(Overlay, {
		onBackdropClick = handleClose,
		backgroundColor = Color3.fromRGB(0, 0, 0),
	}, React.createElement("textbutton", {
		Text = "",
		BackgroundColor3 = Color3.fromHex(COLORS.bg.base),
		Size = UDim2.new(0, width, 0, height),
		Position = UDim2.new(0.5, 0, 0.5, 0),
		AnchorPoint = Vector2.new(0.5, 0.5),
		ZIndex = 12,
		AutoButtonColor = false,
		BorderSizePixel = 0,
		BackgroundTransparency = modalTransparency,
		Event = {
			Activated = function()
				-- Stop propagation by doing nothing - this prevents backdrop click
			end,
		},
	}, React.createElement("uicorner", {
		CornerRadius = UDim.new(0, BORDER_RADIUS.md),
	}), React.createElement("uistroke", {
		Color = Color3.fromHex(COLORS.border.l2),
		Thickness = 1,
	}), React.createElement("frame", {
		Size = UDim2.new(1, 4, 1, 4),
		Position = UDim2.new(0, 2, 0, 2),
		AnchorPoint = Vector2.new(0, 0),
		BackgroundColor3 = Color3.fromRGB(0, 0, 0),
		BackgroundTransparency = 0.6,
		BorderSizePixel = 0,
		ZIndex = -1,
	}, React.createElement("uicorner", {
		CornerRadius = UDim.new(0, BORDER_RADIUS.md),
	})), React.createElement(ContainerFrame, {
		backgroundTransparency = 1,
		size = UDim2.new(1, 0, 1, 0),
		padding = 4,
		borderThickness = 0,
		autoSize = Enum.AutomaticSize.None,
	}, React.createElement(Label, {
		text = props.title,
		fontSize = SIZES.fontSize + 2,
		bold = true,
		position = UDim2.new(0, 0, 0, 0),
		size = UDim2.new(1, -40, 0, 20),
		alignment = Enum.TextXAlignment.Left,
	}), React.createElement(IconButton, {
		icon = "✕",
		onClick = handleClose,
		size = UDim2.new(0, 20, 0, 20),
		position = UDim2.new(1, -24, 0, 0),
	}), React.createElement(ContainerFrame, {
		backgroundTransparency = 1,
		size = UDim2.new(1, 0, 1, -24),
		position = UDim2.new(0, 0, 0, 24),
		padding = 0,
		borderThickness = 0,
		autoSize = Enum.AutomaticSize.None,
	}, React.createElement(ErrorBoundary, {
		children = props.children,
	})))))
end
return {
	Modal = Modal,
}
