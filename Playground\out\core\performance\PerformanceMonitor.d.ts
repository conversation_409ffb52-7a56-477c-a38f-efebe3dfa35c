import { Result } from "../foundation/types/Result";
import { Error } from "../foundation/types/BrandedTypes";
import { BaseService } from "../foundation/BaseService";
interface PerformanceMetrics {
    frameTime: number;
    fps: number;
    memoryUsage: number;
    heartbeatFrequency: number;
    renderStepTime: number;
    averageFrameTime: number;
    peakMemoryUsage: number;
    gcCount: number;
}
interface PerformanceAlert {
    type: "fps" | "memory" | "frametime";
    severity: "warning" | "critical";
    message: string;
    value: number;
    threshold: number;
    timestamp: number;
}
interface PerformanceThresholds {
    minFps: number;
    maxFrameTime: number;
    maxMemoryUsage: number;
    warningMemoryUsage: number;
}
interface ProfilerEntry {
    name: string;
    startTime: number;
    endTime?: number;
    duration?: number;
    category?: string;
}
/**
 * Performance monitoring system for tracking frame rates, memory usage, and profiling
 * Provides real-time performance metrics and alerts for optimization
 */
export declare class PerformanceMonitor extends BaseService {
    private static instance;
    private isMonitoring;
    private heartbeatConnection?;
    private renderStepConnection?;
    private frameHistory;
    private memoryHistory;
    private readonly maxHistorySize;
    private activeProfiles;
    private completedProfiles;
    private maxProfileHistory;
    private metrics;
    private thresholds;
    private alerts;
    private maxAlertHistory;
    private alertCallbacks;
    private lastGCCount;
    private gcInterval;
    private lastGCTime;
    constructor();
    static getInstance(): PerformanceMonitor;
    protected onInitialize(): Promise<Result<void, Error>>;
    protected onShutdown(): Promise<Result<void, Error>>;
    /**
     * Start performance monitoring
     */
    startMonitoring(): void;
    /**
     * Stop performance monitoring
     */
    stopMonitoring(): void;
    /**
     * Get current performance metrics
     */
    getMetrics(): PerformanceMetrics;
    /**
     * Get performance alerts
     */
    getAlerts(): PerformanceAlert[];
    /**
     * Clear performance alerts
     */
    clearAlerts(): void;
    /**
     * Set performance thresholds for alerts
     */
    setThresholds(thresholds: Partial<PerformanceThresholds>): void;
    /**
     * Add callback for performance alerts
     */
    onAlert(callback: (alert: PerformanceAlert) => void): () => void;
    /**
     * Start profiling a code section
     */
    startProfile(name: string, category?: string): void;
    /**
     * End profiling a code section
     */
    endProfile(name: string): number | undefined;
    /**
     * Profile a function execution
     */
    profile<T>(name: string, fn: () => T, category?: string): T;
    /**
     * Profile an async function execution
     */
    profileAsync<T>(name: string, fn: () => Promise<T>, category?: string): Promise<T>;
    /**
     * Get profiling results
     */
    getProfiles(category?: string): ProfilerEntry[];
    /**
     * Get profiling statistics
     */
    getProfileStats(name?: string, category?: string): {
        count: number;
        totalTime: number;
        averageTime: number;
        minTime: number;
        maxTime: number;
    } | undefined;
    /**
     * Clear profiling history
     */
    clearProfiles(): void;
    /**
     * Get memory usage in MB
     */
    getMemoryUsage(): number;
    /**
     * Force garbage collection (if available)
     */
    forceGC(): void;
    private updateFrameMetrics;
    private updateMemoryMetrics;
    private checkThresholds;
    private addAlert;
    /**
     * Get a performance report
     */
    getReport(): string;
}
export declare const Performance: {
    getInstance: () => PerformanceMonitor;
    start: () => void;
    stop: () => void;
    getMetrics: () => PerformanceMetrics;
    profile: <T>(name: string, fn: () => T, category?: string) => T;
    profileAsync: <T>(name: string, fn: () => Promise<T>, category?: string) => Promise<T>;
    getReport: () => string;
};
export {};
