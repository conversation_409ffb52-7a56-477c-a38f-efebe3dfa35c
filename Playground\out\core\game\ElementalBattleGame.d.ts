import { GameController } from "./GameModeManager";
import { Result } from "../foundation/types/Result";
import { Error } from "../foundation/types/RobloxError";
export interface ElementalBattleState {
    playerHealth: number;
    maxPlayerHealth: number;
    currentWave: number;
    enemiesInWave: number;
    enemiesRemaining: number;
    score: number;
    isGameActive: boolean;
    gamePhase: "waiting" | "playing" | "betweenWaves" | "ended";
    timeUntilNextWave: number;
    abilitiesUnlocked: string[];
    playerLevel: number;
    experience: number;
    experienceToNextLevel: number;
}
export interface ElementalBattleConfig {
    arenaSize: number;
    arenaCenter: Vector3;
    basePlayerHealth: number;
    baseEnemiesPerWave: number;
    waveMultiplier: number;
    enemyHealthMultiplier: number;
    timeBetweenWaves: number;
    scorePerEnemyKill: number;
    experiencePerEnemyKill: number;
    experiencePerLevel: number;
}
export interface Enemy {
    entityId: string;
    health: number;
    maxHealth: number;
    enemyType: "basic" | "fast" | "heavy" | "elemental";
    element?: "fire" | "ice" | "earth";
    position: Vector3;
    isAlive: boolean;
}
export declare class ElementalBattleGame implements GameController {
    private entityManager;
    private aiController;
    private gameLoop?;
    private waveTimer?;
    private config;
    private activeEnemies;
    private player;
    private startTime;
    private waveStartTime;
    private gameState;
    constructor(config?: Partial<ElementalBattleConfig>);
    getName(): string;
    getGameState(): ElementalBattleState;
    start(): Promise<Result<void, Error>>;
    stop(): Promise<Result<void, Error>>;
    isActive(): boolean;
    private createArena;
    private startNextWave;
    private spawnWaveEnemies;
    private spawnEnemy;
    private configureEnemyAI;
    private startGameLoop;
    private updateEnemies;
    private onEnemyKilled;
    private onWaveCompleted;
    private levelUp;
    private unlockAbilities;
    damagePlayer(damage: number): void;
    private onPlayerDeath;
    cleanup(): Promise<Result<void, Error>>;
}
