import * as React from "@rbxts/react";
import { COLORS, SIZES } from "../../design";
import { Modal } from "../modal/Modal";
import { Button } from "../button";
import { IconButton } from "../button";
import { ListItemButton } from "../button";
import { Label } from "../label/Label";
import { ContainerFrame, VerticalFrame, HorizontalFrame } from "../frame";
import { ScrollingFrame } from "../frame";
import { Slider } from "../slider";
import { Input as TextInput } from "../input/Input";

interface ComponentShowcaseProps {
	isOpen: boolean;
	onClose: () => void;
}

export function ComponentShowcase(props: ComponentShowcaseProps) {
	const [sliderValue, setSliderValue] = React.useState(0.5);
	const [textValue, setTextValue] = React.useState("Sample text");
	const [counter, setCounter] = React.useState(0);

	if (!props.isOpen) return <></>;

	return (
		<Modal
			title="🎨 Complete GUI Component Showcase"
			isOpen={props.isOpen}
			onClose={props.onClose}
			width={700}
			height={600}
		>
			<ScrollingFrame
				size={new UDim2(1, 0, 1, 0)}
				backgroundTransparency={1}
				borderThickness={0}
				scrollingDirection={Enum.ScrollingDirection.Y}
				automaticCanvasSize={Enum.AutomaticSize.Y}
			>
				<VerticalFrame spacing={SIZES.padding * 2} padding={SIZES.padding}>
					{/* Buttons Section */}
					<VerticalFrame spacing={SIZES.padding} padding={0}>
						<Label text="🔘 Buttons" fontSize={SIZES.fontSize + 2} bold={true} />
						
						<HorizontalFrame spacing={SIZES.padding} padding={0}>
							<Button
								text="Primary Button"
								onClick={() => {
									print("Primary button clicked!");
									setCounter(counter + 1);
								}}
								LayoutOrder={1}
							/>
							
							<Button
								text="Secondary"
								variant="secondary"
								onClick={() => print("Secondary button clicked!")}
								LayoutOrder={2}
							/>
							
							<Button
								text="Danger"
								variant="danger"
								onClick={() => print("Danger button clicked!")}
								LayoutOrder={3}
							/>
						</HorizontalFrame>

						<HorizontalFrame spacing={SIZES.padding} padding={0}>
							<IconButton
								icon="⚙️"
								onClick={() => print("Settings icon clicked!")}
								layoutOrder={1}
							/>
							
							<IconButton
								icon="❤️"
								onClick={() => print("Heart icon clicked!")}
								layoutOrder={2}
							/>
							
							<IconButton
								icon="🔍"
								onClick={() => print("Search icon clicked!")}
								layoutOrder={3}
							/>
						</HorizontalFrame>

						<VerticalFrame spacing={4} padding={0}>
							<Label text="List Item Buttons:" fontSize={SIZES.fontSize} bold={true} />
							<ListItemButton onClick={() => print("Profile clicked!")}>
								<HorizontalFrame spacing={8} padding={8}>
									<Label text="👤" fontSize={SIZES.fontSize} layoutOrder={1} />
									<Label text="Profile Settings" fontSize={SIZES.fontSize} layoutOrder={2} />
								</HorizontalFrame>
							</ListItemButton>
							<ListItemButton onClick={() => print("Notifications clicked!")}>
								<HorizontalFrame spacing={8} padding={8}>
									<Label text="🔔" fontSize={SIZES.fontSize} layoutOrder={1} />
									<Label text="Notifications" fontSize={SIZES.fontSize} layoutOrder={2} />
								</HorizontalFrame>
							</ListItemButton>
							<ListItemButton onClick={() => print("Privacy clicked!")}>
								<HorizontalFrame spacing={8} padding={8}>
									<Label text="🔒" fontSize={SIZES.fontSize} layoutOrder={1} />
									<Label text="Privacy & Security" fontSize={SIZES.fontSize} layoutOrder={2} />
								</HorizontalFrame>
							</ListItemButton>
						</VerticalFrame>
					</VerticalFrame>

					{/* Labels Section */}
					<VerticalFrame spacing={SIZES.padding} padding={0}>
						<Label text="📝 Labels & Text" fontSize={SIZES.fontSize + 2} bold={true} />
						
						<Label text="This is a normal label" fontSize={SIZES.fontSize} />
						<Label text="This is a bold label" fontSize={SIZES.fontSize} bold={true} />
						<Label text="This is a large label" fontSize={SIZES.fontSize + 4} />
						<Label text="This is a colored label" fontSize={SIZES.fontSize} textColor={COLORS.primary} />
						<Label 
							text="This is a wrapped label with a lot of text that should wrap to multiple lines when it gets too long for the container width."
							fontSize={SIZES.fontSize}
							textWrapped={true}
						/>
					</VerticalFrame>

					{/* Input Section */}
					<VerticalFrame spacing={SIZES.padding} padding={0}>
						<Label text="📝 Text Input" fontSize={SIZES.fontSize + 2} bold={true} />
						
						<TextInput
							placeholder="Enter some text..."
							value={textValue}
							onChange={(newValue: string) => setTextValue(newValue)}
							size={new UDim2(1, 0, 0, 36)}
						/>
						
						<Label text={`Current input: "${textValue}"`} fontSize={SIZES.fontSize - 2} textColor={COLORS.text.secondary} />
					</VerticalFrame>

					{/* Slider Section */}
					<VerticalFrame spacing={SIZES.padding} padding={0}>
						<Label text="🎚️ Sliders" fontSize={SIZES.fontSize + 2} bold={true} />
						
						<Slider
							value={sliderValue}
							onChange={(newValue) => setSliderValue(newValue)}
							min={0}
							max={1}
							step={0.01}
							size={new UDim2(1, 0, 0, 30)}
						/>
						
						<Label 
							text={`Slider value: ${math.floor(sliderValue * 100)}%`} 
							fontSize={SIZES.fontSize - 2} 
							textColor={COLORS.text.secondary} 
						/>
					</VerticalFrame>

					{/* Frames Section */}
					<VerticalFrame spacing={SIZES.padding} padding={0}>
						<Label text="📦 Frames & Containers" fontSize={SIZES.fontSize + 2} bold={true} />
						
						<ContainerFrame
							backgroundColor={COLORS.bg.secondary}
							backgroundTransparency={0}
							size={new UDim2(1, 0, 0, 80)}
							padding={SIZES.padding}
							borderThickness={1}
						>
							<Label text="This is inside a ContainerFrame" fontSize={SIZES.fontSize} />
						</ContainerFrame>

						<HorizontalFrame 
							backgroundColor={COLORS.bg.surface}
							backgroundTransparency={0}
							size={new UDim2(1, 0, 0, 60)}
							spacing={SIZES.padding}
							padding={SIZES.padding}
						>
							<Label text="Horizontal" layoutOrder={1} />
							<Label text="Layout" layoutOrder={2} />
							<Label text="Frame" layoutOrder={3} />
						</HorizontalFrame>

						<VerticalFrame spacing={8} padding={12}>
							<Label text="Grid-like Layout:" fontSize={SIZES.fontSize} bold={true} />
							<HorizontalFrame spacing={8} padding={0}>
								<ContainerFrame
									backgroundColor={COLORS.bg.surface}
									backgroundTransparency={0}
									size={new UDim2(0, 80, 0, 60)}
									padding={8}
									borderThickness={1}
									layoutOrder={1}
								>
									<Label 
										text="Item 1" 
										fontSize={SIZES.fontSize - 2}
										alignment={Enum.TextXAlignment.Center}
									/>
								</ContainerFrame>
								<ContainerFrame
									backgroundColor={COLORS.bg.surface}
									backgroundTransparency={0}
									size={new UDim2(0, 80, 0, 60)}
									padding={8}
									borderThickness={1}
									layoutOrder={2}
								>
									<Label 
										text="Item 2" 
										fontSize={SIZES.fontSize - 2}
										alignment={Enum.TextXAlignment.Center}
									/>
								</ContainerFrame>
								<ContainerFrame
									backgroundColor={COLORS.bg.surface}
									backgroundTransparency={0}
									size={new UDim2(0, 80, 0, 60)}
									padding={8}
									borderThickness={1}
									layoutOrder={3}
								>
									<Label 
										text="Item 3" 
										fontSize={SIZES.fontSize - 2}
										alignment={Enum.TextXAlignment.Center}
									/>
								</ContainerFrame>
							</HorizontalFrame>
						</VerticalFrame>
					</VerticalFrame>

					{/* Interactive Counter Section */}
					<VerticalFrame spacing={SIZES.padding} padding={0}>
						<Label text="🔢 Interactive Counter" fontSize={SIZES.fontSize + 2} bold={true} />
						
						<ContainerFrame
							backgroundColor={COLORS.bg.surface}
							backgroundTransparency={0}
							size={new UDim2(1, 0, 0, 100)}
							padding={SIZES.padding}
							borderThickness={1}
						>
							<VerticalFrame spacing={SIZES.padding} padding={0}>
								<Label 
									text={`Button clicks: ${counter}`} 
									fontSize={SIZES.fontSize + 2} 
									alignment={Enum.TextXAlignment.Center}
									bold={true}
								/>
								
								<HorizontalFrame spacing={SIZES.padding} padding={0} horizontalAlignment={Enum.HorizontalAlignment.Center}>
									<Button
										text="+"
										onClick={() => setCounter(counter + 1)}
										size={new UDim2(0, 40, 0, 30)}
										LayoutOrder={1}
									/>
									
									<Button
										text="Reset"
										variant="secondary"
										onClick={() => setCounter(0)}
										LayoutOrder={2}
									/>
									
									<Button
										text="-"
										variant="danger"
										onClick={() => setCounter(math.max(0, counter - 1))}
										size={new UDim2(0, 40, 0, 30)}
										LayoutOrder={3}
									/>
								</HorizontalFrame>
							</VerticalFrame>
						</ContainerFrame>
					</VerticalFrame>

					{/* Image Section - Using simple frame instead */}
					<VerticalFrame spacing={SIZES.padding} padding={0}>
						<Label text="🖼️ Images & Icons" fontSize={SIZES.fontSize + 2} bold={true} />
						
						<HorizontalFrame spacing={SIZES.padding} padding={0}>
							<frame
								Size={new UDim2(0, 64, 0, 64)}
								BackgroundColor3={Color3.fromHex(COLORS.bg.surface)}
								BorderSizePixel={1}
								BorderColor3={Color3.fromHex(COLORS.border.base)}
								LayoutOrder={1}
							>
								<uicorner CornerRadius={new UDim(0, 8)} />
								<Label 
									text="🖼️" 
									fontSize={32}
									alignment={Enum.TextXAlignment.Center}
									position={new UDim2(0.5, 0, 0.5, 0)}
									anchorPoint={new Vector2(0.5, 0.5)}
								/>
							</frame>
							
							<frame
								Size={new UDim2(0, 64, 0, 64)}
								BackgroundColor3={Color3.fromHex(COLORS.primary)}
								BorderSizePixel={0}
								LayoutOrder={2}
							>
								<uicorner CornerRadius={new UDim(0, 32)} />
								<Label 
									text="⭐" 
									fontSize={24}
									alignment={Enum.TextXAlignment.Center}
									position={new UDim2(0.5, 0, 0.5, 0)}
									anchorPoint={new Vector2(0.5, 0.5)}
									textColor="#FFFFFF"
								/>
							</frame>
						</HorizontalFrame>
					</VerticalFrame>

					{/* Color Showcase */}
					<VerticalFrame spacing={SIZES.padding} padding={0}>
						<Label text="🎨 Color Palette" fontSize={SIZES.fontSize + 2} bold={true} />
						
						<HorizontalFrame spacing={SIZES.padding / 2} padding={0}>
							<frame
								Size={new UDim2(0, 40, 0, 40)}
								BackgroundColor3={Color3.fromHex(COLORS.primary)}
								BorderSizePixel={0}
								LayoutOrder={1}
							>
								<uicorner CornerRadius={new UDim(0, 4)} />
							</frame>
							
							<frame
								Size={new UDim2(0, 40, 0, 40)}
								BackgroundColor3={Color3.fromHex(COLORS.success)}
								BorderSizePixel={0}
								LayoutOrder={2}
							>
								<uicorner CornerRadius={new UDim(0, 4)} />
							</frame>
							
							<frame
								Size={new UDim2(0, 40, 0, 40)}
								BackgroundColor3={Color3.fromHex(COLORS.warning)}
								BorderSizePixel={0}
								LayoutOrder={3}
							>
								<uicorner CornerRadius={new UDim(0, 4)} />
							</frame>
							
							<frame
								Size={new UDim2(0, 40, 0, 40)}
								BackgroundColor3={Color3.fromHex(COLORS.error)}
								BorderSizePixel={0}
								LayoutOrder={4}
							>
								<uicorner CornerRadius={new UDim(0, 4)} />
							</frame>
						</HorizontalFrame>
						
						<Label 
							text="Primary • Success • Warning • Error" 
							fontSize={SIZES.fontSize - 2} 
							textColor={COLORS.text.secondary}
							alignment={Enum.TextXAlignment.Center}
						/>
					</VerticalFrame>

					{/* Documentation */}
					<VerticalFrame spacing={SIZES.padding} padding={0}>
						<Label text="📖 Component Documentation" fontSize={SIZES.fontSize + 2} bold={true} />
						
						<ContainerFrame
							backgroundColor={COLORS.bg.secondary}
							backgroundTransparency={0}
							size={new UDim2(1, 0, 0, 240)}
							padding={SIZES.padding}
							borderThickness={1}
						>
							<VerticalFrame spacing={SIZES.padding / 2} padding={0}>
								<Label 
									text="Available Components:" 
									fontSize={SIZES.fontSize} 
									bold={true}
								/>
								<Label text="• Button, IconButton, ListItemButton - Interactive buttons with variants" fontSize={SIZES.fontSize - 2} />
								<Label text="• Label - Text display with customization options" fontSize={SIZES.fontSize - 2} />
								<Label text="• TextInput - Text input field with validation" fontSize={SIZES.fontSize - 2} />
								<Label text="• DropdownButton - Dropdown selection with options" fontSize={SIZES.fontSize - 2} />
								<Label text="• Slider - Value selection with min/max/step" fontSize={SIZES.fontSize - 2} />
								<Label text="• LoadingIndicator - Animated loading spinner" fontSize={SIZES.fontSize - 2} />
								<Label text="• Image - Image display with transparency support" fontSize={SIZES.fontSize - 2} />
								<Label text="• ListView - Selectable list of items" fontSize={SIZES.fontSize - 2} />
								<Label text="• Grid - Multi-column grid layout" fontSize={SIZES.fontSize - 2} />
								<Label text="• ContainerFrame, VerticalFrame, HorizontalFrame - Layout containers" fontSize={SIZES.fontSize - 2} />
								<Label text="• Modal - Overlay dialog component (streamlined)" fontSize={SIZES.fontSize - 2} />
								<Label text="• ScrollingFrame - Scrollable content container" fontSize={SIZES.fontSize - 2} />
								<Label text="• Toast - Notification system" fontSize={SIZES.fontSize - 2} />
								<Label text="• ActionBar, AbilitySlot - Game-specific UI components" fontSize={SIZES.fontSize - 2} />
								<Label text="• AutoDockFrame - Responsive layout component" fontSize={SIZES.fontSize - 2} />
								<Label text="• FormField - Form input wrapper with validation" fontSize={SIZES.fontSize - 2} />
								<Label text="• SplashScreen - Loading/intro screen component" fontSize={SIZES.fontSize - 2} />
							</VerticalFrame>
						</ContainerFrame>
					</VerticalFrame>

					{/* Usage Notes */}
					<VerticalFrame spacing={SIZES.padding} padding={0}>
						<Label text="💡 Usage Notes" fontSize={SIZES.fontSize + 2} bold={true} />
						
						<ContainerFrame
							backgroundColor={COLORS.bg.secondary}
							backgroundTransparency={0}
							size={new UDim2(1, 0, 0, 120)}
							padding={SIZES.padding}
							borderThickness={1}
						>
							<VerticalFrame spacing={SIZES.padding / 2} padding={0}>
								<Label text="✅ Modal now has streamlined design with minimal spacing" fontSize={SIZES.fontSize - 2} textColor={COLORS.success} />
								<Label text="✅ All weather effects use invisible parts (no more sky cubes)" fontSize={SIZES.fontSize - 2} textColor={COLORS.success} />
								<Label text="✅ Improved AI behaviors with smarter pathfinding" fontSize={SIZES.fontSize - 2} textColor={COLORS.success} />
								<Label text="✅ Enhanced weather sounds with proper audio IDs" fontSize={SIZES.fontSize - 2} textColor={COLORS.success} />
								<Label text="✅ Complete component showcase with all available elements" fontSize={SIZES.fontSize - 2} textColor={COLORS.success} />
								<Label text="🎯 All components follow consistent design patterns" fontSize={SIZES.fontSize - 2} textColor={COLORS.text.secondary} />
							</VerticalFrame>
						</ContainerFrame>
					</VerticalFrame>
				</VerticalFrame>
			</ScrollingFrame>
		</Modal>
	);
}