-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local React = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "react")
local _design = TS.import(script, game:GetService("ReplicatedStorage"), "core", "design")
local COLORS = _design.COLORS
local SIZES = _design.SIZES
local Modal = TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "modal", "Modal").Modal
local Button = TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "button").Button
local IconButton = TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "button").IconButton
local ListItemButton = TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "button").ListItemButton
local Label = TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "label", "Label").Label
local _frame = TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "frame")
local ContainerFrame = _frame.ContainerFrame
local VerticalFrame = _frame.VerticalFrame
local HorizontalFrame = _frame.HorizontalFrame
local ScrollingFrame = TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "frame").ScrollingFrame
local Slider = TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "slider").Slider
local TextInput = TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "input", "Input").Input
local function ComponentShowcase(props)
	local sliderValue, setSliderValue = React.useState(0.5)
	local textValue, setTextValue = React.useState("Sample text")
	local counter, setCounter = React.useState(0)
	if not props.isOpen then
		return React.createElement(React.Fragment)
	end
	return React.createElement(Modal, {
		title = "🎨 Complete GUI Component Showcase",
		isOpen = props.isOpen,
		onClose = props.onClose,
		width = 700,
		height = 600,
	}, React.createElement(ScrollingFrame, {
		size = UDim2.new(1, 0, 1, 0),
		backgroundTransparency = 1,
		borderThickness = 0,
		scrollingDirection = Enum.ScrollingDirection.Y,
		automaticCanvasSize = Enum.AutomaticSize.Y,
	}, React.createElement(VerticalFrame, {
		spacing = SIZES.padding * 2,
		padding = SIZES.padding,
	}, React.createElement(VerticalFrame, {
		spacing = SIZES.padding,
		padding = 0,
	}, React.createElement(Label, {
		text = "🔘 Buttons",
		fontSize = SIZES.fontSize + 2,
		bold = true,
	}), React.createElement(HorizontalFrame, {
		spacing = SIZES.padding,
		padding = 0,
	}, React.createElement(Button, {
		text = "Primary Button",
		onClick = function()
			print("Primary button clicked!")
			setCounter(counter + 1)
		end,
		LayoutOrder = 1,
	}), React.createElement(Button, {
		text = "Secondary",
		variant = "secondary",
		onClick = function()
			return print("Secondary button clicked!")
		end,
		LayoutOrder = 2,
	}), React.createElement(Button, {
		text = "Danger",
		variant = "danger",
		onClick = function()
			return print("Danger button clicked!")
		end,
		LayoutOrder = 3,
	})), React.createElement(HorizontalFrame, {
		spacing = SIZES.padding,
		padding = 0,
	}, React.createElement(IconButton, {
		icon = "⚙️",
		onClick = function()
			return print("Settings icon clicked!")
		end,
		layoutOrder = 1,
	}), React.createElement(IconButton, {
		icon = "❤️",
		onClick = function()
			return print("Heart icon clicked!")
		end,
		layoutOrder = 2,
	}), React.createElement(IconButton, {
		icon = "🔍",
		onClick = function()
			return print("Search icon clicked!")
		end,
		layoutOrder = 3,
	})), React.createElement(VerticalFrame, {
		spacing = 4,
		padding = 0,
	}, React.createElement(Label, {
		text = "List Item Buttons:",
		fontSize = SIZES.fontSize,
		bold = true,
	}), React.createElement(ListItemButton, {
		onClick = function()
			return print("Profile clicked!")
		end,
	}, React.createElement(HorizontalFrame, {
		spacing = 8,
		padding = 8,
	}, React.createElement(Label, {
		text = "👤",
		fontSize = SIZES.fontSize,
		layoutOrder = 1,
	}), React.createElement(Label, {
		text = "Profile Settings",
		fontSize = SIZES.fontSize,
		layoutOrder = 2,
	}))), React.createElement(ListItemButton, {
		onClick = function()
			return print("Notifications clicked!")
		end,
	}, React.createElement(HorizontalFrame, {
		spacing = 8,
		padding = 8,
	}, React.createElement(Label, {
		text = "🔔",
		fontSize = SIZES.fontSize,
		layoutOrder = 1,
	}), React.createElement(Label, {
		text = "Notifications",
		fontSize = SIZES.fontSize,
		layoutOrder = 2,
	}))), React.createElement(ListItemButton, {
		onClick = function()
			return print("Privacy clicked!")
		end,
	}, React.createElement(HorizontalFrame, {
		spacing = 8,
		padding = 8,
	}, React.createElement(Label, {
		text = "🔒",
		fontSize = SIZES.fontSize,
		layoutOrder = 1,
	}), React.createElement(Label, {
		text = "Privacy & Security",
		fontSize = SIZES.fontSize,
		layoutOrder = 2,
	}))))), React.createElement(VerticalFrame, {
		spacing = SIZES.padding,
		padding = 0,
	}, React.createElement(Label, {
		text = "📝 Labels & Text",
		fontSize = SIZES.fontSize + 2,
		bold = true,
	}), React.createElement(Label, {
		text = "This is a normal label",
		fontSize = SIZES.fontSize,
	}), React.createElement(Label, {
		text = "This is a bold label",
		fontSize = SIZES.fontSize,
		bold = true,
	}), React.createElement(Label, {
		text = "This is a large label",
		fontSize = SIZES.fontSize + 4,
	}), React.createElement(Label, {
		text = "This is a colored label",
		fontSize = SIZES.fontSize,
		textColor = COLORS.primary,
	}), React.createElement(Label, {
		text = "This is a wrapped label with a lot of text that should wrap to multiple lines when it gets too long for the container width.",
		fontSize = SIZES.fontSize,
		textWrapped = true,
	})), React.createElement(VerticalFrame, {
		spacing = SIZES.padding,
		padding = 0,
	}, React.createElement(Label, {
		text = "📝 Text Input",
		fontSize = SIZES.fontSize + 2,
		bold = true,
	}), React.createElement(TextInput, {
		placeholder = "Enter some text...",
		value = textValue,
		onChange = function(newValue)
			return setTextValue(newValue)
		end,
		size = UDim2.new(1, 0, 0, 36),
	}), React.createElement(Label, {
		text = `Current input: "{textValue}"`,
		fontSize = SIZES.fontSize - 2,
		textColor = COLORS.text.secondary,
	})), React.createElement(VerticalFrame, {
		spacing = SIZES.padding,
		padding = 0,
	}, React.createElement(Label, {
		text = "🎚️ Sliders",
		fontSize = SIZES.fontSize + 2,
		bold = true,
	}), React.createElement(Slider, {
		value = sliderValue,
		onChange = function(newValue)
			return setSliderValue(newValue)
		end,
		min = 0,
		max = 1,
		step = 0.01,
		size = UDim2.new(1, 0, 0, 30),
	}), React.createElement(Label, {
		text = `Slider value: {math.floor(sliderValue * 100)}%`,
		fontSize = SIZES.fontSize - 2,
		textColor = COLORS.text.secondary,
	})), React.createElement(VerticalFrame, {
		spacing = SIZES.padding,
		padding = 0,
	}, React.createElement(Label, {
		text = "📦 Frames & Containers",
		fontSize = SIZES.fontSize + 2,
		bold = true,
	}), React.createElement(ContainerFrame, {
		backgroundColor = COLORS.bg.secondary,
		backgroundTransparency = 0,
		size = UDim2.new(1, 0, 0, 80),
		padding = SIZES.padding,
		borderThickness = 1,
	}, React.createElement(Label, {
		text = "This is inside a ContainerFrame",
		fontSize = SIZES.fontSize,
	})), React.createElement(HorizontalFrame, {
		backgroundColor = COLORS.bg.surface,
		backgroundTransparency = 0,
		size = UDim2.new(1, 0, 0, 60),
		spacing = SIZES.padding,
		padding = SIZES.padding,
	}, React.createElement(Label, {
		text = "Horizontal",
		layoutOrder = 1,
	}), React.createElement(Label, {
		text = "Layout",
		layoutOrder = 2,
	}), React.createElement(Label, {
		text = "Frame",
		layoutOrder = 3,
	})), React.createElement(VerticalFrame, {
		spacing = 8,
		padding = 12,
	}, React.createElement(Label, {
		text = "Grid-like Layout:",
		fontSize = SIZES.fontSize,
		bold = true,
	}), React.createElement(HorizontalFrame, {
		spacing = 8,
		padding = 0,
	}, React.createElement(ContainerFrame, {
		backgroundColor = COLORS.bg.surface,
		backgroundTransparency = 0,
		size = UDim2.new(0, 80, 0, 60),
		padding = 8,
		borderThickness = 1,
		layoutOrder = 1,
	}, React.createElement(Label, {
		text = "Item 1",
		fontSize = SIZES.fontSize - 2,
		alignment = Enum.TextXAlignment.Center,
	})), React.createElement(ContainerFrame, {
		backgroundColor = COLORS.bg.surface,
		backgroundTransparency = 0,
		size = UDim2.new(0, 80, 0, 60),
		padding = 8,
		borderThickness = 1,
		layoutOrder = 2,
	}, React.createElement(Label, {
		text = "Item 2",
		fontSize = SIZES.fontSize - 2,
		alignment = Enum.TextXAlignment.Center,
	})), React.createElement(ContainerFrame, {
		backgroundColor = COLORS.bg.surface,
		backgroundTransparency = 0,
		size = UDim2.new(0, 80, 0, 60),
		padding = 8,
		borderThickness = 1,
		layoutOrder = 3,
	}, React.createElement(Label, {
		text = "Item 3",
		fontSize = SIZES.fontSize - 2,
		alignment = Enum.TextXAlignment.Center,
	}))))), React.createElement(VerticalFrame, {
		spacing = SIZES.padding,
		padding = 0,
	}, React.createElement(Label, {
		text = "🔢 Interactive Counter",
		fontSize = SIZES.fontSize + 2,
		bold = true,
	}), React.createElement(ContainerFrame, {
		backgroundColor = COLORS.bg.surface,
		backgroundTransparency = 0,
		size = UDim2.new(1, 0, 0, 100),
		padding = SIZES.padding,
		borderThickness = 1,
	}, React.createElement(VerticalFrame, {
		spacing = SIZES.padding,
		padding = 0,
	}, React.createElement(Label, {
		text = `Button clicks: {counter}`,
		fontSize = SIZES.fontSize + 2,
		alignment = Enum.TextXAlignment.Center,
		bold = true,
	}), React.createElement(HorizontalFrame, {
		spacing = SIZES.padding,
		padding = 0,
		horizontalAlignment = Enum.HorizontalAlignment.Center,
	}, React.createElement(Button, {
		text = "+",
		onClick = function()
			return setCounter(counter + 1)
		end,
		size = UDim2.new(0, 40, 0, 30),
		LayoutOrder = 1,
	}), React.createElement(Button, {
		text = "Reset",
		variant = "secondary",
		onClick = function()
			return setCounter(0)
		end,
		LayoutOrder = 2,
	}), React.createElement(Button, {
		text = "-",
		variant = "danger",
		onClick = function()
			return setCounter(math.max(0, counter - 1))
		end,
		size = UDim2.new(0, 40, 0, 30),
		LayoutOrder = 3,
	}))))), React.createElement(VerticalFrame, {
		spacing = SIZES.padding,
		padding = 0,
	}, React.createElement(Label, {
		text = "🖼️ Images & Icons",
		fontSize = SIZES.fontSize + 2,
		bold = true,
	}), React.createElement(HorizontalFrame, {
		spacing = SIZES.padding,
		padding = 0,
	}, React.createElement("frame", {
		Size = UDim2.new(0, 64, 0, 64),
		BackgroundColor3 = Color3.fromHex(COLORS.bg.surface),
		BorderSizePixel = 1,
		BorderColor3 = Color3.fromHex(COLORS.border.base),
		LayoutOrder = 1,
	}, React.createElement("uicorner", {
		CornerRadius = UDim.new(0, 8),
	}), React.createElement(Label, {
		text = "🖼️",
		fontSize = 32,
		alignment = Enum.TextXAlignment.Center,
		position = UDim2.new(0.5, 0, 0.5, 0),
		anchorPoint = Vector2.new(0.5, 0.5),
	})), React.createElement("frame", {
		Size = UDim2.new(0, 64, 0, 64),
		BackgroundColor3 = Color3.fromHex(COLORS.primary),
		BorderSizePixel = 0,
		LayoutOrder = 2,
	}, React.createElement("uicorner", {
		CornerRadius = UDim.new(0, 32),
	}), React.createElement(Label, {
		text = "⭐",
		fontSize = 24,
		alignment = Enum.TextXAlignment.Center,
		position = UDim2.new(0.5, 0, 0.5, 0),
		anchorPoint = Vector2.new(0.5, 0.5),
		textColor = "#FFFFFF",
	})))), React.createElement(VerticalFrame, {
		spacing = SIZES.padding,
		padding = 0,
	}, React.createElement(Label, {
		text = "🎨 Color Palette",
		fontSize = SIZES.fontSize + 2,
		bold = true,
	}), React.createElement(HorizontalFrame, {
		spacing = SIZES.padding / 2,
		padding = 0,
	}, React.createElement("frame", {
		Size = UDim2.new(0, 40, 0, 40),
		BackgroundColor3 = Color3.fromHex(COLORS.primary),
		BorderSizePixel = 0,
		LayoutOrder = 1,
	}, React.createElement("uicorner", {
		CornerRadius = UDim.new(0, 4),
	})), React.createElement("frame", {
		Size = UDim2.new(0, 40, 0, 40),
		BackgroundColor3 = Color3.fromHex(COLORS.success),
		BorderSizePixel = 0,
		LayoutOrder = 2,
	}, React.createElement("uicorner", {
		CornerRadius = UDim.new(0, 4),
	})), React.createElement("frame", {
		Size = UDim2.new(0, 40, 0, 40),
		BackgroundColor3 = Color3.fromHex(COLORS.warning),
		BorderSizePixel = 0,
		LayoutOrder = 3,
	}, React.createElement("uicorner", {
		CornerRadius = UDim.new(0, 4),
	})), React.createElement("frame", {
		Size = UDim2.new(0, 40, 0, 40),
		BackgroundColor3 = Color3.fromHex(COLORS.error),
		BorderSizePixel = 0,
		LayoutOrder = 4,
	}, React.createElement("uicorner", {
		CornerRadius = UDim.new(0, 4),
	}))), React.createElement(Label, {
		text = "Primary • Success • Warning • Error",
		fontSize = SIZES.fontSize - 2,
		textColor = COLORS.text.secondary,
		alignment = Enum.TextXAlignment.Center,
	})), React.createElement(VerticalFrame, {
		spacing = SIZES.padding,
		padding = 0,
	}, React.createElement(Label, {
		text = "📖 Component Documentation",
		fontSize = SIZES.fontSize + 2,
		bold = true,
	}), React.createElement(ContainerFrame, {
		backgroundColor = COLORS.bg.secondary,
		backgroundTransparency = 0,
		size = UDim2.new(1, 0, 0, 240),
		padding = SIZES.padding,
		borderThickness = 1,
	}, React.createElement(VerticalFrame, {
		spacing = SIZES.padding / 2,
		padding = 0,
	}, React.createElement(Label, {
		text = "Available Components:",
		fontSize = SIZES.fontSize,
		bold = true,
	}), React.createElement(Label, {
		text = "• Button, IconButton, ListItemButton - Interactive buttons with variants",
		fontSize = SIZES.fontSize - 2,
	}), React.createElement(Label, {
		text = "• Label - Text display with customization options",
		fontSize = SIZES.fontSize - 2,
	}), React.createElement(Label, {
		text = "• TextInput - Text input field with validation",
		fontSize = SIZES.fontSize - 2,
	}), React.createElement(Label, {
		text = "• DropdownButton - Dropdown selection with options",
		fontSize = SIZES.fontSize - 2,
	}), React.createElement(Label, {
		text = "• Slider - Value selection with min/max/step",
		fontSize = SIZES.fontSize - 2,
	}), React.createElement(Label, {
		text = "• LoadingIndicator - Animated loading spinner",
		fontSize = SIZES.fontSize - 2,
	}), React.createElement(Label, {
		text = "• Image - Image display with transparency support",
		fontSize = SIZES.fontSize - 2,
	}), React.createElement(Label, {
		text = "• ListView - Selectable list of items",
		fontSize = SIZES.fontSize - 2,
	}), React.createElement(Label, {
		text = "• Grid - Multi-column grid layout",
		fontSize = SIZES.fontSize - 2,
	}), React.createElement(Label, {
		text = "• ContainerFrame, VerticalFrame, HorizontalFrame - Layout containers",
		fontSize = SIZES.fontSize - 2,
	}), React.createElement(Label, {
		text = "• Modal - Overlay dialog component (streamlined)",
		fontSize = SIZES.fontSize - 2,
	}), React.createElement(Label, {
		text = "• ScrollingFrame - Scrollable content container",
		fontSize = SIZES.fontSize - 2,
	}), React.createElement(Label, {
		text = "• Toast - Notification system",
		fontSize = SIZES.fontSize - 2,
	}), React.createElement(Label, {
		text = "• ActionBar, AbilitySlot - Game-specific UI components",
		fontSize = SIZES.fontSize - 2,
	}), React.createElement(Label, {
		text = "• AutoDockFrame - Responsive layout component",
		fontSize = SIZES.fontSize - 2,
	}), React.createElement(Label, {
		text = "• FormField - Form input wrapper with validation",
		fontSize = SIZES.fontSize - 2,
	}), React.createElement(Label, {
		text = "• SplashScreen - Loading/intro screen component",
		fontSize = SIZES.fontSize - 2,
	})))), React.createElement(VerticalFrame, {
		spacing = SIZES.padding,
		padding = 0,
	}, React.createElement(Label, {
		text = "💡 Usage Notes",
		fontSize = SIZES.fontSize + 2,
		bold = true,
	}), React.createElement(ContainerFrame, {
		backgroundColor = COLORS.bg.secondary,
		backgroundTransparency = 0,
		size = UDim2.new(1, 0, 0, 120),
		padding = SIZES.padding,
		borderThickness = 1,
	}, React.createElement(VerticalFrame, {
		spacing = SIZES.padding / 2,
		padding = 0,
	}, React.createElement(Label, {
		text = "✅ Modal now has streamlined design with minimal spacing",
		fontSize = SIZES.fontSize - 2,
		textColor = COLORS.success,
	}), React.createElement(Label, {
		text = "✅ All weather effects use invisible parts (no more sky cubes)",
		fontSize = SIZES.fontSize - 2,
		textColor = COLORS.success,
	}), React.createElement(Label, {
		text = "✅ Improved AI behaviors with smarter pathfinding",
		fontSize = SIZES.fontSize - 2,
		textColor = COLORS.success,
	}), React.createElement(Label, {
		text = "✅ Enhanced weather sounds with proper audio IDs",
		fontSize = SIZES.fontSize - 2,
		textColor = COLORS.success,
	}), React.createElement(Label, {
		text = "✅ Complete component showcase with all available elements",
		fontSize = SIZES.fontSize - 2,
		textColor = COLORS.success,
	}), React.createElement(Label, {
		text = "🎯 All components follow consistent design patterns",
		fontSize = SIZES.fontSize - 2,
		textColor = COLORS.text.secondary,
	})))))))
end
return {
	ComponentShowcase = ComponentShowcase,
}
