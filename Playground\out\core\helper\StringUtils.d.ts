/**
 * String manipulation utilities for game development
 */
export declare class StringUtils {
    /**
     * Format time in seconds to MM:SS format
     */
    static formatTime(seconds: number): string;
    /**
     * Format time in seconds to HH:MM:SS format
     */
    static formatTimeHours(seconds: number): string;
    /**
     * Format large numbers with K, M, B suffixes
     */
    static formatNumber(num: number): string;
    /**
     * Format currency with proper commas
     */
    static formatCurrency(amount: number, currency?: string): string;
    /**
     * Add commas to large numbers
     */
    static addCommas(num: number): string;
    /**
     * Capitalize first letter of each word
     */
    static titleCase(str: string): string;
    /**
     * Convert string to camelCase
     */
    static toCamelCase(str: string): string;
    /**
     * Convert camelCase to snake_case
     */
    static toSnakeCase(str: string): string;
    /**
     * Convert string to kebab-case
     */
    static toKebabCase(str: string): string;
    /**
     * Truncate string with ellipsis
     */
    static truncate(str: string, maxLength: number, suffix?: string): string;
    /**
     * Pad string to specified length
     */
    static pad(str: string, length: number, char?: string, left?: boolean): string;
    /**
     * Remove all whitespace from string
     */
    static removeWhitespace(str: string): string;
    /**
     * Count occurrences of substring
     */
    static countOccurrences(str: string, pattern: string): number;
    /**
     * Generate random string of specified length
     */
    static randomString(length: number, charset?: string): string;
    /**
     * Generate UUID-like string
     */
    static generateId(): string;
    /**
     * Convert RGB to hex color
     */
    static rgbToHex(r: number, g: number, b: number): string;
    /**
     * Convert Color3 to hex string
     */
    static color3ToHex(color: Color3): string;
    /**
     * Check if string is valid email format
     */
    static isValidEmail(email: string): boolean;
    /**
     * Escape special characters for pattern matching
     */
    static escapePattern(str: string): string;
    /**
     * Split string by delimiter
     */
    static split(str: string, delimiter: string): string[];
    /**
     * Join array of strings with delimiter
     */
    static join(parts: string[], delimiter: string): string;
    /**
     * Check if string starts with prefix
     */
    static startsWith(str: string, prefix: string): boolean;
    /**
     * Check if string ends with suffix
     */
    static endsWith(str: string, suffix: string): boolean;
    /**
     * Format player name with display formatting
     */
    static formatPlayerName(player: Player): string;
    /**
     * Convert markdown-style formatting to rich text
     */
    static markdownToRichText(text: string): string;
    /**
     * Generate initials from name
     */
    static getInitials(name: string, maxInitials?: number): string;
    /**
     * Format bytes to human readable format
     */
    static formatBytes(bytes: number): string;
}
