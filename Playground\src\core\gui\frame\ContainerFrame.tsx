import * as React from "@rbxts/react";
import { COLORS, SIZES, BORDER_RADIUS } from "../../design";
import { ContainerFrameProps } from "./types/ContainerFrameProps";
import { ResponsiveManager } from "../layout/ResponsiveManager";

export function ContainerFrame(props: ContainerFrameProps): React.ReactElement {
	const backgroundColor = props.backgroundColor ?? COLORS.bg.base;
	const backgroundTransparency = props.backgroundTransparency ?? 0;
	const cornerRadius = props.cornerRadius ?? BORDER_RADIUS.md;
	const borderColor = props.borderColor ?? COLORS.border.l2;
	const borderThickness = props.borderThickness ?? 1;
	const borderTransparency = props.borderTransparency ?? 0;

	// Responsive manager for dynamic calculations
	const responsiveManager = ResponsiveManager.getInstance();

	// Calculate responsive padding
	const basePadding = props.padding ?? SIZES.padding * 0.6; // Reduced default padding
	const padding = props.responsiveMargin ? responsiveManager.getResponsiveMargin(basePadding) : basePadding;

	// Smart sizing: if fitContent is true or no size specified, use AutomaticSize
	const fitContent = props.fitContent ?? props.size === undefined;
	const size = props.size ?? (fitContent ? new UDim2(1, 0, 0, 0) : new UDim2(1, 0, 1, 0));
	const autoSize = props.autoSize ?? (fitContent ? Enum.AutomaticSize.XY : Enum.AutomaticSize.None);

	return (
		<frame
			BackgroundColor3={Color3.fromHex(backgroundColor)}
			BackgroundTransparency={backgroundTransparency}
			Size={size}
			Position={props.position}
			AnchorPoint={props.anchorPoint}
			LayoutOrder={props.layoutOrder}
			BorderSizePixel={0}
			ZIndex={props.zIndex}
			AutomaticSize={autoSize}
			ClipsDescendants={true} // Prevent content from overflowing the frame
		>
			<uicorner CornerRadius={new UDim(0, cornerRadius)} />

			<uistroke
				Color={Color3.fromHex(borderColor)}
				Thickness={borderThickness}
				Transparency={borderTransparency}
			/>

			<uipadding
				PaddingTop={new UDim(0, padding)}
				PaddingBottom={new UDim(0, padding)}
				PaddingLeft={new UDim(0, padding)}
				PaddingRight={new UDim(0, padding)}
			/>

			{/* Add size constraints if specified */}
			{(props.minSize || props.maxSize) && (
				<uisizeconstraint
					MinSize={
						props.minSize
							? new Vector2(
									props.minSize.X.Scale * 1920 + props.minSize.X.Offset,
									props.minSize.Y.Scale * 1080 + props.minSize.Y.Offset,
								)
							: undefined
					}
					MaxSize={
						props.maxSize
							? new Vector2(
									props.maxSize.X.Scale * 1920 + props.maxSize.X.Offset,
									props.maxSize.Y.Scale * 1080 + props.maxSize.Y.Offset,
								)
							: undefined
					}
				/>
			)}

			{props.children}
		</frame>
	);
}
