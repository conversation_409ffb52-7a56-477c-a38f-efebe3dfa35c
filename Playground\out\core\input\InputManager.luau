-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local _services = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "services")
local UserInputService = _services.UserInputService
local ContextActionService = _services.ContextActionService
local RunService = _services.RunService
local BaseService = TS.import(script, game:GetService("ReplicatedStorage"), "core", "foundation", "BaseService").BaseService
local Result = TS.import(script, game:GetService("ReplicatedStorage"), "core", "foundation", "types", "Result").Result
local createError = TS.import(script, game:GetService("ReplicatedStorage"), "core", "foundation", "types", "BrandedTypes").createError
local InputType = {
	KeyPress = "KeyPress",
	KeyRelease = "KeyRelease",
	KeyHold = "KeyHold",
	MouseClick = "MouseClick",
	MouseMove = "MouseMove",
	TouchStart = "TouchStart",
	TouchEnd = "TouchEnd",
	GamepadInput = "GamepadInput",
}
local InputContext = {
	Global = "Global",
	UI = "UI",
	Movement = "Movement",
	Combat = "Combat",
	Camera = "Camera",
	Custom = "Custom",
}
--[[
	*
	 * Comprehensive input management system supporting keyboard, mouse, touch, and gamepad
	 * Provides customizable input bindings, contexts, and input state tracking
	 
]]
local InputManager
do
	local super = BaseService
	InputManager = setmetatable({}, {
		__tostring = function()
			return "InputManager"
		end,
		__index = super,
	})
	InputManager.__index = InputManager
	function InputManager.new(...)
		local self = setmetatable({}, InputManager)
		return self:constructor(...) or self
	end
	function InputManager:constructor()
		super.constructor(self, "InputManager")
		self.bindings = {}
		self.contextStacks = {}
		self.activeContext = InputContext.Global
		self.config = {
			enableMouse = true,
			enableKeyboard = true,
			enableTouch = true,
			enableGamepad = true,
			mouseSensitivity = 1.0,
			gamepadDeadzone = 0.1,
			touchSensitivity = 1.0,
			holdThreshold = 0.5,
		}
		self.inputState = {
			keysPressed = {},
			mousePosition = Vector2.new(0, 0),
			mouseDelta = Vector2.new(0, 0),
			gamepadSticks = {},
			touchPositions = {},
			isInputEnabled = true,
		}
		self.stats = {
			totalInputs = 0,
			keyInputs = 0,
			mouseInputs = 0,
			touchInputs = 0,
			gamepadInputs = 0,
			averageInputsPerSecond = 0,
			lastInputTime = 0,
		}
		self.holdTimers = {}
		self.inputConnections = {}
		self.lastStatsUpdate = 0
		-- Initialize context stacks
		local _contextStacks = self.contextStacks
		local _global = InputContext.Global
		_contextStacks[_global] = {}
		local _contextStacks_1 = self.contextStacks
		local _uI = InputContext.UI
		_contextStacks_1[_uI] = {}
		local _contextStacks_2 = self.contextStacks
		local _movement = InputContext.Movement
		_contextStacks_2[_movement] = {}
		local _contextStacks_3 = self.contextStacks
		local _combat = InputContext.Combat
		_contextStacks_3[_combat] = {}
		local _contextStacks_4 = self.contextStacks
		local _camera = InputContext.Camera
		_contextStacks_4[_camera] = {}
		local _contextStacks_5 = self.contextStacks
		local _custom = InputContext.Custom
		_contextStacks_5[_custom] = {}
	end
	function InputManager:getInstance()
		if not InputManager.instance then
			InputManager.instance = InputManager.new()
		end
		return InputManager.instance
	end
	InputManager.onInitialize = TS.async(function(self)
		local _exitType, _returns = TS.try(function()
			self:setupInputConnections()
			self:setupDefaultBindings()
			print("🎮 Input Manager initialized")
			return TS.TRY_RETURN, { Result:ok(nil) }
		end, function(error)
			return TS.TRY_RETURN, { Result:err(createError(`Failed to initialize InputManager: {error}`)) }
		end)
		if _exitType then
			return unpack(_returns)
		end
	end)
	InputManager.onShutdown = TS.async(function(self)
		self:cleanup()
		print("🎮 Input Manager shutdown")
		return Result:ok(nil)
	end)
	function InputManager:registerBinding(binding)
		local _bindings = self.bindings
		local _id = binding.id
		if _bindings[_id] ~= nil then
			return Result:err(createError(`Input binding '{binding.id}' already exists`))
		end
		local _object = table.clone(binding)
		setmetatable(_object, nil)
		_object.enabled = true
		local _left = "priority"
		local _condition = binding.priority
		if not (_condition ~= 0 and _condition == _condition and _condition) then
			_condition = 0
		end
		_object[_left] = _condition
		local fullBinding = _object
		local _bindings_1 = self.bindings
		local _id_1 = binding.id
		_bindings_1[_id_1] = fullBinding
		-- Register with ContextActionService for mobile support
		if #binding.keys > 0 or binding.gamepadButtons or binding.touchGestures then
			self:registerContextAction(fullBinding)
		end
		return Result:ok(nil)
	end
	function InputManager:unregisterBinding(id)
		local _bindings = self.bindings
		local _id = id
		local binding = _bindings[_id]
		if not binding then
			return false
		end
		local _bindings_1 = self.bindings
		local _id_1 = id
		_bindings_1[_id_1] = nil
		ContextActionService:UnbindAction(id)
		return true
	end
	function InputManager:setBindingEnabled(id, enabled)
		local _bindings = self.bindings
		local _id = id
		local binding = _bindings[_id]
		if not binding then
			return false
		end
		binding.enabled = enabled
		if enabled then
			self:registerContextAction(binding)
		else
			ContextActionService:UnbindAction(id)
		end
		return true
	end
	function InputManager:setContext(context)
		if self.activeContext ~= context then
			self.activeContext = context
			self:updateContextBindings()
		end
	end
	function InputManager:pushContext(context, id)
		local _contextStacks = self.contextStacks
		local _context = context
		local stack = _contextStacks[_context]
		local _condition = stack
		if _condition then
			local _id = id
			_condition = not (table.find(stack, _id) ~= nil)
		end
		if _condition then
			local _id = id
			table.insert(stack, _id)
			self:setContext(context)
		end
	end
	function InputManager:popContext(context, id)
		local _contextStacks = self.contextStacks
		local _context = context
		local stack = _contextStacks[_context]
		if stack then
			local _id = id
			local index = (table.find(stack, _id) or 0) - 1
			if index >= 0 then
				table.remove(stack, index + 1)
				-- Restore previous context if stack is empty
				if #stack == 0 then
					self:setContext(InputContext.Global)
				end
			end
		end
	end
	function InputManager:getInputState()
		local _object = table.clone(self.inputState)
		setmetatable(_object, nil)
		return _object
	end
	function InputManager:isKeyPressed(key)
		local _keysPressed = self.inputState.keysPressed
		local _key = key
		return _keysPressed[_key] ~= nil
	end
	function InputManager:isAnyKeyPressed(keys)
		-- ▼ ReadonlyArray.some ▼
		local _result = false
		local _callback = function(key)
			local _keysPressed = self.inputState.keysPressed
			local _key = key
			return _keysPressed[_key] ~= nil
		end
		for _k, _v in keys do
			if _callback(_v, _k - 1, keys) then
				_result = true
				break
			end
		end
		-- ▲ ReadonlyArray.some ▲
		return _result
	end
	function InputManager:getMousePosition()
		return self.inputState.mousePosition
	end
	function InputManager:getMouseDelta()
		return self.inputState.mouseDelta
	end
	function InputManager:getGamepadStick(stick)
		return self.inputState.gamepadSticks[stick] or Vector3.new(0, 0, 0)
	end
	function InputManager:setInputEnabled(enabled)
		self.inputState.isInputEnabled = enabled
		if enabled then
			self:setupInputConnections()
		else
			self:cleanup()
		end
	end
	function InputManager:updateConfig(newConfig)
		local _object = table.clone(self.config)
		setmetatable(_object, nil)
		for _k, _v in newConfig do
			_object[_k] = _v
		end
		self.config = _object
	end
	function InputManager:getStats()
		local _object = table.clone(self.stats)
		setmetatable(_object, nil)
		return _object
	end
	function InputManager:resetStats()
		self.stats = {
			totalInputs = 0,
			keyInputs = 0,
			mouseInputs = 0,
			touchInputs = 0,
			gamepadInputs = 0,
			averageInputsPerSecond = 0,
			lastInputTime = 0,
		}
	end
	function InputManager:setupInputConnections()
		if not RunService:IsClient() then
			return nil
		end
		self:cleanup()
		-- Keyboard and mouse input
		if self.config.enableKeyboard or self.config.enableMouse then
			local connection1 = UserInputService.InputBegan:Connect(function(input, gameProcessed)
				if gameProcessed or not self.inputState.isInputEnabled then
					return nil
				end
				self:handleInputBegan(input)
			end)
			local connection2 = UserInputService.InputEnded:Connect(function(input, gameProcessed)
				if gameProcessed or not self.inputState.isInputEnabled then
					return nil
				end
				self:handleInputEnded(input)
			end)
			local connection3 = UserInputService.InputChanged:Connect(function(input, gameProcessed)
				if gameProcessed or not self.inputState.isInputEnabled then
					return nil
				end
				self:handleInputChanged(input)
			end)
			local _exp = self.inputConnections
			-- ▼ Array.push ▼
			table.insert(_exp, connection1)
			table.insert(_exp, connection2)
			table.insert(_exp, connection3)
			-- ▲ Array.push ▲
		end
		-- Touch input
		if self.config.enableTouch then
			local touchConnection1 = UserInputService.TouchStarted:Connect(function(touch, gameProcessed)
				if gameProcessed or not self.inputState.isInputEnabled then
					return nil
				end
				self:handleTouchStarted(touch)
			end)
			local touchConnection2 = UserInputService.TouchEnded:Connect(function(touch, gameProcessed)
				if gameProcessed or not self.inputState.isInputEnabled then
					return nil
				end
				self:handleTouchEnded(touch)
			end)
			local _exp = self.inputConnections
			-- ▼ Array.push ▼
			table.insert(_exp, touchConnection1)
			table.insert(_exp, touchConnection2)
			-- ▲ Array.push ▲
		end
		-- Hold detection
		local holdConnection = RunService.Heartbeat:Connect(function()
			self:updateHoldDetection()
			self:updateStats()
		end)
		local _exp = self.inputConnections
		table.insert(_exp, holdConnection)
	end
	function InputManager:cleanup()
		local _exp = self.inputConnections
		-- ▼ ReadonlyArray.forEach ▼
		local _callback = function(connection)
			return connection:Disconnect()
		end
		for _k, _v in _exp do
			_callback(_v, _k - 1, _exp)
		end
		-- ▲ ReadonlyArray.forEach ▲
		self.inputConnections = {}
		-- Unbind all context actions
		local _exp_1 = self.bindings
		-- ▼ ReadonlyMap.forEach ▼
		local _callback_1 = function(_, id)
			ContextActionService:UnbindAction(id)
		end
		for _k, _v in _exp_1 do
			_callback_1(_v, _k, _exp_1)
		end
		-- ▲ ReadonlyMap.forEach ▲
	end
	function InputManager:handleInputBegan(input)
		self:updateInputStats("key")
		if input.UserInputType == Enum.UserInputType.Keyboard then
			local _keysPressed = self.inputState.keysPressed
			local _keyCode = input.KeyCode
			_keysPressed[_keyCode] = true
			local _holdTimers = self.holdTimers
			local _keyCode_1 = input.KeyCode
			local _arg1 = tick()
			_holdTimers[_keyCode_1] = _arg1
		elseif input.UserInputType == Enum.UserInputType.MouseButton1 or input.UserInputType == Enum.UserInputType.MouseButton2 then
			self:updateInputStats("mouse")
		end
	end
	function InputManager:handleInputEnded(input)
		if input.UserInputType == Enum.UserInputType.Keyboard then
			local _keysPressed = self.inputState.keysPressed
			local _keyCode = input.KeyCode
			_keysPressed[_keyCode] = nil
			local _holdTimers = self.holdTimers
			local _keyCode_1 = input.KeyCode
			_holdTimers[_keyCode_1] = nil
		end
	end
	function InputManager:handleInputChanged(input)
		if input.UserInputType == Enum.UserInputType.MouseMovement then
			local newPosition = Vector2.new(input.Position.X, input.Position.Y)
			local _mousePosition = self.inputState.mousePosition
			self.inputState.mouseDelta = newPosition - _mousePosition
			self.inputState.mousePosition = newPosition
			self:updateInputStats("mouse")
		elseif input.UserInputType == Enum.UserInputType.Gamepad1 then
			self:updateGamepadState(input)
			self:updateInputStats("gamepad")
		end
	end
	function InputManager:handleTouchStarted(touch)
		self.inputState.touchPositions[touch.KeyCode.Value] = Vector2.new(touch.Position.X, touch.Position.Y)
		self:updateInputStats("touch")
	end
	function InputManager:handleTouchEnded(touch)
		self.inputState.touchPositions[touch.KeyCode.Value] = nil
	end
	function InputManager:updateGamepadState(input)
		if input.KeyCode == Enum.KeyCode.Thumbstick1 then
			local position = input.Position
			if position.Magnitude > self.config.gamepadDeadzone then
				self.inputState.gamepadSticks.LeftStick = position
			else
				self.inputState.gamepadSticks.LeftStick = Vector3.new(0, 0, 0)
			end
		elseif input.KeyCode == Enum.KeyCode.Thumbstick2 then
			local position = input.Position
			if position.Magnitude > self.config.gamepadDeadzone then
				self.inputState.gamepadSticks.RightStick = position
			else
				self.inputState.gamepadSticks.RightStick = Vector3.new(0, 0, 0)
			end
		end
	end
	function InputManager:updateHoldDetection()
		local currentTime = tick()
		local _exp = self.holdTimers
		-- ▼ ReadonlyMap.forEach ▼
		local _callback = function(startTime, key)
			if currentTime - startTime >= self.config.holdThreshold then
				-- Trigger hold events for bindings that care about holds
				self:triggerHoldBindings(key)
			end
		end
		for _k, _v in _exp do
			_callback(_v, _k, _exp)
		end
		-- ▲ ReadonlyMap.forEach ▲
	end
	function InputManager:triggerHoldBindings(key)
		local _exp = self.bindings
		-- ▼ ReadonlyMap.forEach ▼
		local _callback = function(binding)
			local _condition = binding.enabled
			if _condition then
				local _keys = binding.keys
				local _key = key
				_condition = table.find(_keys, _key) ~= nil
			end
			if _condition then
				-- Create a synthetic input object for hold events
				local syntheticInput = {
					KeyCode = key,
					UserInputType = Enum.UserInputType.Keyboard,
				}
				binding.callback(binding.id, Enum.UserInputState.Change, syntheticInput)
			end
		end
		for _k, _v in _exp do
			_callback(_v, _k, _exp)
		end
		-- ▲ ReadonlyMap.forEach ▲
	end
	function InputManager:updateInputStats(inputType)
		self.stats.totalInputs += 1
		self.stats.lastInputTime = tick()
		if inputType == "key" then
			self.stats.keyInputs += 1
		elseif inputType == "mouse" then
			self.stats.mouseInputs += 1
		elseif inputType == "touch" then
			self.stats.touchInputs += 1
		elseif inputType == "gamepad" then
			self.stats.gamepadInputs += 1
		end
	end
	function InputManager:updateStats()
		local currentTime = tick()
		if currentTime - self.lastStatsUpdate >= 1 then
			self.stats.averageInputsPerSecond = self.stats.totalInputs / (currentTime - self.lastStatsUpdate)
			self.lastStatsUpdate = currentTime
		end
	end
	function InputManager:registerContextAction(binding)
		local _exp = binding.keys
		-- ▼ ReadonlyArray.map ▼
		local _newValue = table.create(#_exp)
		local _callback = function(key)
			return key.Name
		end
		for _k, _v in _exp do
			_newValue[_k] = _callback(_v, _k - 1, _exp)
		end
		-- ▲ ReadonlyArray.map ▲
		local keys = table.concat(_newValue, ", ")
		ContextActionService:BindAction(binding.id, function(actionName, state, input)
			if binding.enabled and self:shouldProcessBinding(binding) then
				binding.callback(actionName, state, input)
			end
		end, false, unpack(binding.keys))
	end
	function InputManager:shouldProcessBinding(binding)
		return binding.context == self.activeContext or binding.context == InputContext.Global
	end
	function InputManager:updateContextBindings()
		-- Re-register all bindings to respect new context
		local _exp = self.bindings
		-- ▼ ReadonlyMap.forEach ▼
		local _callback = function(binding)
			ContextActionService:UnbindAction(binding.id)
			if binding.enabled and self:shouldProcessBinding(binding) then
				self:registerContextAction(binding)
			end
		end
		for _k, _v in _exp do
			_callback(_v, _k, _exp)
		end
		-- ▲ ReadonlyMap.forEach ▲
	end
	function InputManager:setupDefaultBindings()
		-- Example default bindings - can be customized per game
		self:registerBinding({
			id = "movement_forward",
			context = InputContext.Movement,
			keys = { Enum.KeyCode.W, Enum.KeyCode.Up },
			description = "Move forward",
			callback = function()
				-- Default movement will be handled by movement system
			end,
		})
		self:registerBinding({
			id = "movement_backward",
			context = InputContext.Movement,
			keys = { Enum.KeyCode.S, Enum.KeyCode.Down },
			description = "Move backward",
			callback = function()
				-- Default movement will be handled by movement system
			end,
		})
		self:registerBinding({
			id = "movement_left",
			context = InputContext.Movement,
			keys = { Enum.KeyCode.A, Enum.KeyCode.Left },
			description = "Move left",
			callback = function()
				-- Default movement will be handled by movement system
			end,
		})
		self:registerBinding({
			id = "movement_right",
			context = InputContext.Movement,
			keys = { Enum.KeyCode.D, Enum.KeyCode.Right },
			description = "Move right",
			callback = function()
				-- Default movement will be handled by movement system
			end,
		})
		self:registerBinding({
			id = "movement_jump",
			context = InputContext.Movement,
			keys = { Enum.KeyCode.Space },
			description = "Jump",
			callback = function()
				-- Default jump will be handled by movement system
			end,
		})
	end
	function InputManager:getTouchPointCount(touchPositions)
		-- Simple count by iterating
		local count = 0
		local keys = touchPositions
		if keys[0] ~= nil then
			count += 1
		end
		if keys[1] ~= nil then
			count += 1
		end
		if keys[2] ~= nil then
			count += 1
		end
		if keys[3] ~= nil then
			count += 1
		end
		if keys[4] ~= nil then
			count += 1
		end
		-- Support up to 5 touch points (typical for mobile)
		return count
	end
	function InputManager:getReport()
		local stats = self:getStats()
		local state = self:getInputState()
		local report = "🎮 Input Manager Report\n"
		report ..= `Active Context: {self.activeContext}\n`
		report ..= `Input Enabled: {state.isInputEnabled}\n`
		-- ▼ ReadonlyMap.size ▼
		local _size = 0
		for _ in self.bindings do
			_size += 1
		end
		-- ▲ ReadonlyMap.size ▲
		report ..= `Total Bindings: {_size}\n\n`
		report ..= "📊 Input Statistics:\n"
		report ..= `Total Inputs: {stats.totalInputs}\n`
		report ..= `Key: {stats.keyInputs} | Mouse: {stats.mouseInputs} | Touch: {stats.touchInputs} | Gamepad: {stats.gamepadInputs}\n`
		report ..= `Average IPS: {string.format("%.1f", stats.averageInputsPerSecond)}\n\n`
		report ..= "🎯 Current State:\n"
		-- ▼ ReadonlySet.size ▼
		local _size_1 = 0
		for _ in state.keysPressed do
			_size_1 += 1
		end
		-- ▲ ReadonlySet.size ▲
		report ..= `Keys Pressed: {_size_1}\n`
		report ..= `Mouse Position: ({string.format("%.0f", state.mousePosition.X)}, {string.format("%.0f", state.mousePosition.Y)})\n`
		report ..= `Touch Points: {self:getTouchPointCount(state.touchPositions)}\n`
		return report
	end
end
-- Global input management functions for easy access
local Input = {
	getInstance = function()
		return InputManager:getInstance()
	end,
	registerBinding = function(binding)
		return InputManager:getInstance():registerBinding(binding)
	end,
	setContext = function(context)
		return InputManager:getInstance():setContext(context)
	end,
	isKeyPressed = function(key)
		return InputManager:getInstance():isKeyPressed(key)
	end,
	getMousePosition = function()
		return InputManager:getInstance():getMousePosition()
	end,
	getReport = function()
		return InputManager:getInstance():getReport()
	end,
}
-- Export types for external use
return {
	InputType = InputType,
	InputContext = InputContext,
	InputManager = InputManager,
	Input = Input,
}
