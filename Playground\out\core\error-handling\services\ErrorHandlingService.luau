-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local BaseService = TS.import(script, game:GetService("ReplicatedStorage"), "core", "foundation", "BaseService").BaseService
local _ErrorTypes = TS.import(script, game:GetService("ReplicatedStorage"), "core", "error-handling", "types", "ErrorTypes")
local ErrorSeverity = _ErrorTypes.ErrorSeverity
local ErrorCategory = _ErrorTypes.ErrorCategory
local Result = TS.import(script, game:GetService("ReplicatedStorage"), "core", "foundation", "types", "Result").Result
local createError = TS.import(script, game:GetService("ReplicatedStorage"), "core", "foundation", "types", "RobloxError").createError
local ErrorHandlingService
do
	local super = BaseService
	ErrorHandlingService = setmetatable({}, {
		__tostring = function()
			return "ErrorHandlingService"
		end,
		__index = super,
	})
	ErrorHandlingService.__index = ErrorHandlingService
	function ErrorHandlingService.new(...)
		local self = setmetatable({}, ErrorHandlingService)
		return self:constructor(...) or self
	end
	function ErrorHandlingService:constructor()
		super.constructor(self, "ErrorHandlingService")
		self.errors = {}
		self.errorIdCounter = 0
	end
	ErrorHandlingService.onInitialize = TS.async(function(self)
		print("🚨 ErrorHandlingService: Initializing...")
		return Result:ok(nil)
	end)
	ErrorHandlingService.onShutdown = TS.async(function(self)
		table.clear(self.errors)
		return Result:ok(nil)
	end)
	function ErrorHandlingService:logApplicationError(message, severity, category, context)
		self.errorIdCounter = self.errorIdCounter + 1
		local errorId = `error_{self.errorIdCounter}`
		local errorEntry = {
			id = errorId,
			message = message,
			severity = severity,
			category = category,
			timestamp = tick(),
			context = context,
			resolved = false,
		}
		self.errors[errorId] = errorEntry
		-- Log to console based on severity
		repeat
			if severity == (ErrorSeverity.CRITICAL) then
				error(`[CRITICAL] {category}: {message}`)
				break
			end
			if severity == (ErrorSeverity.HIGH) then
				warn(`[HIGH] {category}: {message}`)
				break
			end
			if severity == (ErrorSeverity.MEDIUM) then
				warn(`[MEDIUM] {category}: {message}`)
				break
			end
			if severity == (ErrorSeverity.LOW) then
				print(`[LOW] {category}: {message}`)
				break
			end
		until true
		return errorId
	end
	function ErrorHandlingService:getErrors(severity, category)
		local errorList = {}
		for _, errorEntry in self.errors do
			if severity and errorEntry.severity ~= severity then
				continue
			end
			if category and errorEntry.category ~= category then
				continue
			end
			table.insert(errorList, errorEntry)
		end
		return errorList
	end
	function ErrorHandlingService:resolveError(errorId, resolution)
		local _errors = self.errors
		local _errorId = errorId
		local errorEntry = _errors[_errorId]
		if not errorEntry then
			return Result:err(createError(`Error with ID '{errorId}' not found`))
		end
		errorEntry.resolved = true
		errorEntry.resolution = resolution
		print(`✅ Error resolved: {errorId} - {resolution}`)
		return Result:ok(nil)
	end
	function ErrorHandlingService:clearResolvedErrors()
		for id, errorEntry in self.errors do
			if errorEntry.resolved then
				self.errors[id] = nil
			end
		end
	end
	function ErrorHandlingService:getErrorCount(severity)
		if not severity then
			-- ▼ ReadonlyMap.size ▼
			local _size = 0
			for _ in self.errors do
				_size += 1
			end
			-- ▲ ReadonlyMap.size ▲
			return _size
		end
		local count = 0
		for _, errorEntry in self.errors do
			if errorEntry.severity == severity then
				count += 1
			end
		end
		return count
	end
	function ErrorHandlingService:logNetworkError(message, context)
		return self:logApplicationError(message, ErrorSeverity.HIGH, ErrorCategory.NETWORK, context)
	end
	function ErrorHandlingService:logValidationError(message, context)
		return self:logApplicationError(message, ErrorSeverity.MEDIUM, ErrorCategory.VALIDATION, context)
	end
	function ErrorHandlingService:logSystemError(message, context)
		return self:logApplicationError(message, ErrorSeverity.CRITICAL, ErrorCategory.SYSTEM, context)
	end
	function ErrorHandlingService:logUserInputError(message, context)
		return self:logApplicationError(message, ErrorSeverity.LOW, ErrorCategory.USER_INPUT, context)
	end
end
return {
	ErrorHandlingService = ErrorHandlingService,
}
