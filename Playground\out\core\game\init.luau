-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitFor<PERSON>hild("RuntimeLib"))
local exports = {}
local _GameModeManager = TS.import(script, game:GetService("ReplicatedStorage"), "core", "game", "GameModeManager")
exports.GameModeManager = _GameModeManager.GameModeManager
exports.GameMode = _GameModeManager.GameMode
exports.CollectorArenaGame = TS.import(script, game:GetService("ReplicatedStorage"), "core", "game", "CollectorArenaGame").CollectorArenaGame
exports.ElementalBattleGame = TS.import(script, game:GetService("ReplicatedStorage"), "core", "game", "ElementalBattleGame").ElementalBattleGame
exports.PlaygroundController = TS.import(script, game:GetService("ReplicatedStorage"), "core", "game", "PlaygroundController").PlaygroundController
exports.CollectorArenaScoreManager = TS.import(script, game:GetService("ReplicatedStorage"), "core", "game", "CollectorArenaScoreManager").CollectorArenaScoreManager
return exports
