import { BaseService } from "../foundation/BaseService";
import { Result } from "../foundation/types/Result";
import { createError, Error } from "../foundation/types/BrandedTypes";
import {
	QuestDefinition,
	QuestProgress,
	QuestStatus,
	QuestEvent,
	QuestConfiguration,
	QuestObjective,
	ObjectiveType,
	QuestType,
} from "./types";

/**
 * Individual quest instance with progress tracking
 */
export class Quest {
	private definition: QuestDefinition;
	private progress: QuestProgress;
	private eventCallbacks: ((event: QuestEvent) => void)[] = [];

	constructor(definition: QuestDefinition) {
		this.definition = { ...definition };
		this.progress = {
			questId: definition.id,
			status: QuestStatus.NotStarted,
			objectives: new Map(),
			timesCompleted: 0,
		};

		// Initialize objectives
		this.definition.objectives.forEach(obj => {
			this.progress.objectives.set(obj.id, { ...obj });
		});
	}

	/**
	 * Start the quest
	 */
	public start(): Result<boolean, string> {
		if (this.progress.status !== QuestStatus.NotStarted && this.progress.status !== QuestStatus.Available) {
			return Result.err("Quest is already started or completed");
		}

		this.progress.status = QuestStatus.Active;
		this.progress.startTime = tick();

		// Set expiration time if applicable
		if (this.definition.expirationHours) {
			this.progress.expirationTime = tick() + (this.definition.expirationHours * 3600);
		}

		this.fireEvent({
			type: "started",
			questId: this.definition.id,
			timestamp: tick(),
		});

		return Result.ok(true);
	}

	/**
	 * Complete the quest
	 */
	public complete(force: boolean = false): Result<boolean, string> {
		if (this.progress.status !== QuestStatus.Active && !force) {
			return Result.err("Quest is not active");
		}

		if (!this.areRequiredObjectivesCompleted() && !force) {
			return Result.err("Not all required objectives are completed");
		}

		this.progress.status = QuestStatus.Completed;
		this.progress.completionTime = tick();
		this.progress.timesCompleted++;
		this.progress.lastCompletionTime = tick();

		this.fireEvent({
			type: "completed",
			questId: this.definition.id,
			timestamp: tick(),
		});

		return Result.ok(true);
	}

	/**
	 * Fail the quest
	 */
	public fail(reason?: string): Result<boolean, string> {
		if (this.progress.status !== QuestStatus.Active) {
			return Result.err("Quest is not active");
		}

		this.progress.status = QuestStatus.Failed;

		this.fireEvent({
			type: "failed",
			questId: this.definition.id,
			timestamp: tick(),
		});

		return Result.ok(true);
	}

	/**
	 * Abandon the quest
	 */
	public abandon(): Result<boolean, string> {
		if (this.progress.status !== QuestStatus.Active) {
			return Result.err("Quest is not active");
		}

		this.progress.status = QuestStatus.Abandoned;

		this.fireEvent({
			type: "abandoned",
			questId: this.definition.id,
			timestamp: tick(),
		});

		return Result.ok(true);
	}

	/**
	 * Update objective progress
	 */
	public updateObjective(objectiveId: string, increment: number = 1): Result<boolean, string> {
		if (this.progress.status !== QuestStatus.Active) {
			return Result.err("Quest is not active");
		}

		const objective = this.progress.objectives.get(objectiveId);
		if (!objective) {
			return Result.err(`Objective ${objectiveId} not found`);
		}

		if (objective.isCompleted) {
			return Result.ok(false); // Already completed
		}

		// Check prerequisites
		if (objective.requirements?.prerequisites) {
			const prerequisitesMet = objective.requirements.prerequisites.every(prereqId => {
				const prereq = this.progress.objectives.get(prereqId);
				return prereq?.isCompleted || false;
			});

			if (!prerequisitesMet) {
				return Result.err("Objective prerequisites not met");
			}
		}

		// Update count
		objective.currentCount = math.min(objective.currentCount + increment, objective.targetCount);

		// Check if completed
		if (objective.currentCount >= objective.targetCount) {
			objective.isCompleted = true;

			this.fireEvent({
				type: "objective_completed",
				questId: this.definition.id,
				objectiveId: objectiveId,
				timestamp: tick(),
			});

			// Auto-complete quest if enabled and all objectives are done
			if (this.definition.autoComplete && this.areRequiredObjectivesCompleted()) {
				this.complete();
			}
		}

		return Result.ok(true);
	}

	/**
	 * Set objective progress directly
	 */
	public setObjectiveProgress(objectiveId: string, count: number): Result<boolean, string> {
		if (this.progress.status !== QuestStatus.Active) {
			return Result.err("Quest is not active");
		}

		const objective = this.progress.objectives.get(objectiveId);
		if (!objective) {
			return Result.err(`Objective ${objectiveId} not found`);
		}

		const previousCount = objective.currentCount;
		objective.currentCount = math.min(math.max(count, 0), objective.targetCount);

		// Check if completed
		if (objective.currentCount >= objective.targetCount && !objective.isCompleted) {
			objective.isCompleted = true;

			this.fireEvent({
				type: "objective_completed",
				questId: this.definition.id,
				objectiveId: objectiveId,
				timestamp: tick(),
			});

			// Auto-complete quest if enabled and all objectives are done
			if (this.definition.autoComplete && this.areRequiredObjectivesCompleted()) {
				this.complete();
			}
		}

		return Result.ok(objective.currentCount !== previousCount);
	}

	/**
	 * Check if quest has expired
	 */
	public checkExpiration(): boolean {
		if (this.progress.expirationTime && tick() > this.progress.expirationTime) {
			if (this.progress.status === QuestStatus.Active) {
				this.progress.status = QuestStatus.Expired;

				this.fireEvent({
					type: "expired",
					questId: this.definition.id,
					timestamp: tick(),
				});
			}
			return true;
		}
		return false;
	}

	/**
	 * Check if quest can be repeated
	 */
	public canRepeat(): boolean {
		if (!this.definition.isRepeatable) return false;
		if (this.progress.status !== QuestStatus.Completed) return false;

		if (this.definition.cooldownHours && this.progress.lastCompletionTime) {
			const cooldownEnd = this.progress.lastCompletionTime + (this.definition.cooldownHours * 3600);
			return tick() >= cooldownEnd;
		}

		return true;
	}

	/**
	 * Reset quest for repetition
	 */
	public reset(): Result<boolean, string> {
		if (!this.canRepeat()) {
			return Result.err("Quest cannot be repeated yet");
		}

		this.progress.status = QuestStatus.Available;
		this.progress.startTime = undefined;
		this.progress.completionTime = undefined;
		this.progress.expirationTime = undefined;

		// Reset objectives
		this.definition.objectives.forEach(obj => {
			const resetObj = { ...obj };
			resetObj.currentCount = 0;
			resetObj.isCompleted = false;
			this.progress.objectives.set(obj.id, resetObj);
		});

		return Result.ok(true);
	}

	/**
	 * Get quest definition
	 */
	public getDefinition(): QuestDefinition {
		return { ...this.definition };
	}

	/**
	 * Get quest progress
	 */
	public getProgress(): QuestProgress {
		return {
			...this.progress,
			objectives: this.cloneObjectivesMap(this.progress.objectives),
		};
	}

	private cloneObjectivesMap(originalMap: Map<string, QuestObjective>): Map<string, QuestObjective> {
		const newMap = new Map<string, QuestObjective>();
		originalMap.forEach((value, key) => {
			newMap.set(key, { ...value });
		});
		return newMap;
	}

	private objectivesMapToArray(objectivesMap: Map<string, QuestObjective>): QuestObjective[] {
		const objectives: QuestObjective[] = [];
		objectivesMap.forEach(objective => objectives.push(objective));
		return objectives;
	}

	/**
	 * Get completion percentage
	 */
	public getCompletionPercentage(): number {
		const objectives = this.objectivesMapToArray(this.progress.objectives);
		if (objectives.size() === 0) return 0;

		const totalProgress = objectives.reduce((sum, obj) => {
			return sum + (obj.currentCount / obj.targetCount);
		}, 0);

		return (totalProgress / objectives.size()) * 100;
	}

	/**
	 * Get visible objectives (not hidden)
	 */
	public getVisibleObjectives(): QuestObjective[] {
		return this.objectivesMapToArray(this.progress.objectives).filter(obj => !obj.isHidden);
	}

	/**
	 * Get completed objectives
	 */
	public getCompletedObjectives(): QuestObjective[] {
		return this.objectivesMapToArray(this.progress.objectives).filter(obj => obj.isCompleted);
	}

	/**
	 * Get remaining time in seconds
	 */
	public getRemainingTime(): number | undefined {
		if (!this.progress.expirationTime) return undefined;
		return math.max(0, this.progress.expirationTime - tick());
	}

	/**
	 * Add event listener
	 */
	public onEvent(callback: (event: QuestEvent) => void): () => void {
		this.eventCallbacks.push(callback);

		return () => {
			const index = this.eventCallbacks.indexOf(callback);
			if (index >= 0) {
				this.eventCallbacks.remove(index);
			}
		};
	}

	private areRequiredObjectivesCompleted(): boolean {
		const requiredObjectives = this.objectivesMapToArray(this.progress.objectives).filter(obj => !obj.isOptional);
		return requiredObjectives.every(obj => obj.isCompleted);
	}

	private fireEvent(event: QuestEvent): void {
		this.eventCallbacks.forEach(callback => {
			try {
				callback(event);
			} catch (error) {
				warn(`Quest event callback failed: ${error}`);
			}
		});
	}
}

/**
 * Quest Manager service for managing all quests
 */
export class QuestManager extends BaseService {
	private static instance: QuestManager;
	private questDefinitions: Map<string, QuestDefinition> = new Map();
	private activeQuests: Map<string, Quest> = new Map(); // questId -> Quest
	private completedQuests: Set<string> = new Set();
	private config: QuestConfiguration;
	private eventCallbacks: ((event: QuestEvent) => void)[] = [];

	constructor() {
		super("QuestManager");
		this.config = {
			enableAutoProgress: true,
			enableNotifications: true,
			maxActiveQuests: 20,
			enableQuestLog: true,
			enableQuestTracking: true,
			saveProgress: true,
		};
	}

	public static getInstance(): QuestManager {
		if (!QuestManager.instance) {
			QuestManager.instance = new QuestManager();
		}
		return QuestManager.instance;
	}

	protected async onInitialize(): Promise<Result<void, Error>> {
		try {
			this.loadDefaultQuests();
			print("📜 Quest Manager initialized");
			return Result.ok(undefined);
		} catch (error) {
			return Result.err(createError(`Failed to initialize QuestManager: ${error}`));
		}
	}

	protected async onShutdown(): Promise<Result<void, Error>> {
		this.questDefinitions.clear();
		this.activeQuests.clear();
		this.completedQuests.clear();
		this.eventCallbacks = [];
		print("📜 Quest Manager shutdown");
		return Result.ok(undefined);
	}

	/**
	 * Register a quest definition
	 */
	public registerQuest(definition: QuestDefinition): Result<void, string> {
		if (this.questDefinitions.has(definition.id)) {
			return Result.err(`Quest '${definition.id}' already registered`);
		}

		this.questDefinitions.set(definition.id, { ...definition });
		print(`📜 Registered quest: ${definition.name}`);
		return Result.ok(undefined);
	}

	/**
	 * Start a quest
	 */
	public startQuest(questId: string): Result<Quest, string> {
		const definition = this.questDefinitions.get(questId);
		if (!definition) {
			return Result.err(`Quest '${questId}' not found`);
		}

		if (this.activeQuests.has(questId)) {
			return Result.err("Quest is already active");
		}

		if (this.activeQuests.size() >= this.config.maxActiveQuests) {
			return Result.err("Maximum active quests reached");
		}

		// Check prerequisites
		if (definition.prerequisites.size() > 0) {
			const prerequisitesMet = definition.prerequisites.every(prereqId => 
				this.completedQuests.has(prereqId)
			);

			if (!prerequisitesMet) {
				return Result.err("Quest prerequisites not met");
			}
		}

		const quest = new Quest(definition);
		
		// Forward quest events
		quest.onEvent(event => {
			this.handleQuestEvent(event);
		});

		const startResult = quest.start();
		if (startResult.isError()) {
			return Result.err(startResult.getError());
		}

		this.activeQuests.set(questId, quest);
		return Result.ok(quest);
	}

	/**
	 * Complete a quest
	 */
	public completeQuest(questId: string): Result<boolean, string> {
		const quest = this.activeQuests.get(questId);
		if (!quest) {
			return Result.err("Quest is not active");
		}

		const result = quest.complete();
		if (result.isOk()) {
			this.completedQuests.add(questId);
			
			// Remove from active if not repeatable
			const definition = quest.getDefinition();
			if (!definition.isRepeatable) {
				this.activeQuests.delete(questId);
			}
		}

		return result;
	}

	/**
	 * Fail a quest
	 */
	public failQuest(questId: string, reason?: string): Result<boolean, string> {
		const quest = this.activeQuests.get(questId);
		if (!quest) {
			return Result.err("Quest is not active");
		}

		const result = quest.fail(reason);
		if (result.isOk()) {
			this.activeQuests.delete(questId);
		}

		return result;
	}

	/**
	 * Abandon a quest
	 */
	public abandonQuest(questId: string): Result<boolean, string> {
		const quest = this.activeQuests.get(questId);
		if (!quest) {
			return Result.err("Quest is not active");
		}

		const result = quest.abandon();
		if (result.isOk()) {
			this.activeQuests.delete(questId);
		}

		return result;
	}

	/**
	 * Update quest objective progress
	 */
	public updateQuestObjective(questId: string, objectiveId: string, increment: number = 1): Result<boolean, string> {
		const quest = this.activeQuests.get(questId);
		if (!quest) {
			return Result.err("Quest is not active");
		}

		return quest.updateObjective(objectiveId, increment);
	}

	/**
	 * Process a game event to update quest progress
	 */
	public processGameEvent(eventType: string, data: Record<string, unknown>): void {
		if (!this.config.enableAutoProgress) return;

		this.activeQuests.forEach(quest => {
			const definition = quest.getDefinition();
			const progress = quest.getProgress();

			progress.objectives.forEach(objective => {
				if (objective.isCompleted) return;

				// Check if this event matches the objective
				if (this.doesEventMatchObjective(eventType, data, objective)) {
					quest.updateObjective(objective.id, 1);
				}
			});
		});
	}

	/**
	 * Get an active quest
	 */
	public getQuest(questId: string): Quest | undefined {
		return this.activeQuests.get(questId);
	}

	private activeQuestsToArray(): Quest[] {
		const quests: Quest[] = [];
		this.activeQuests.forEach(quest => quests.push(quest));
		return quests;
	}

	/**
	 * Get all active quests
	 */
	public getActiveQuests(): Quest[] {
		return this.activeQuestsToArray();
	}

	/**
	 * Get quests by type
	 */
	public getQuestsByType(questType: QuestType): Quest[] {
		return this.activeQuestsToArray().filter(quest => 
			quest.getDefinition().type === questType
		);
	}

	/**
	 * Get available quests (not started but prerequisites met)
	 */
	public getAvailableQuests(): QuestDefinition[] {
		const available: QuestDefinition[] = [];

		this.questDefinitions.forEach(definition => {
			// Skip if already active or completed (and not repeatable)
			if (this.activeQuests.has(definition.id)) return;
			if (this.completedQuests.has(definition.id) && !definition.isRepeatable) return;

			// Check prerequisites
			const prerequisitesMet = definition.prerequisites.every(prereqId => 
				this.completedQuests.has(prereqId)
			);

			if (prerequisitesMet) {
				available.push(definition);
			}
		});

		return available;
	}

	/**
	 * Check for expired quests
	 */
	public checkExpiredQuests(): string[] {
		const expiredQuestIds: string[] = [];

		this.activeQuests.forEach((quest, questId) => {
			if (quest.checkExpiration()) {
				expiredQuestIds.push(questId);
				this.activeQuests.delete(questId);
			}
		});

		return expiredQuestIds;
	}

	/**
	 * Update configuration
	 */
	public updateConfig(newConfig: Partial<QuestConfiguration>): void {
		this.config = { ...this.config, ...newConfig };
	}

	/**
	 * Add global event listener
	 */
	public onEvent(callback: (event: QuestEvent) => void): () => void {
		this.eventCallbacks.push(callback);

		return () => {
			const index = this.eventCallbacks.indexOf(callback);
			if (index >= 0) {
				this.eventCallbacks.remove(index);
			}
		};
	}

	/**
	 * Generate a comprehensive report
	 */
	public getReport(): string {
		let report = "📜 Quest Manager Report\n";
		report += `Registered Quests: ${this.questDefinitions.size()}\n`;
		report += `Active Quests: ${this.activeQuests.size()}\n`;
		report += `Completed Quests: ${this.completedQuests.size()}\n`;
		report += `Available Quests: ${this.getAvailableQuests().size()}\n\n`;

		// Active quests breakdown
		if (this.activeQuests.size() > 0) {
			report += "📋 Active Quests:\n";
			this.activeQuests.forEach(quest => {
				const definition = quest.getDefinition();
				const completion = quest.getCompletionPercentage();
				report += `  ${definition.name}: ${string.format("%.1f", completion)}% complete\n`;
			});
			report += "\n";
		}

		// Quest types breakdown
		const typeCount: Record<string, number> = {};
		this.activeQuests.forEach(quest => {
			const questType = quest.getDefinition().type;
			typeCount[questType] = (typeCount[questType] || 0) + 1;
		});

		if (this.getObjectKeysCount(typeCount) > 0) {
			report += "📊 Active Quests by Type:\n";
			// Manually check common quest types
			if (typeCount[QuestType.Main]) {
				report += `  ${QuestType.Main}: ${typeCount[QuestType.Main]}\n`;
			}
			if (typeCount[QuestType.Side]) {
				report += `  ${QuestType.Side}: ${typeCount[QuestType.Side]}\n`;
			}
			if (typeCount[QuestType.Daily]) {
				report += `  ${QuestType.Daily}: ${typeCount[QuestType.Daily]}\n`;
			}
			if (typeCount[QuestType.Weekly]) {
				report += `  ${QuestType.Weekly}: ${typeCount[QuestType.Weekly]}\n`;
			}
			if (typeCount[QuestType.Achievement]) {
				report += `  ${QuestType.Achievement}: ${typeCount[QuestType.Achievement]}\n`;
			}
			if (typeCount[QuestType.Tutorial]) {
				report += `  ${QuestType.Tutorial}: ${typeCount[QuestType.Tutorial]}\n`;
			}
			if (typeCount[QuestType.Event]) {
				report += `  ${QuestType.Event}: ${typeCount[QuestType.Event]}\n`;
			}
		}

		return report;
	}

	private getObjectKeysCount(obj: Record<string, number>): number {
		// Simple count by checking known properties
		let count = 0;
		if (obj[QuestType.Main] !== undefined) count++;
		if (obj[QuestType.Side] !== undefined) count++;
		if (obj[QuestType.Daily] !== undefined) count++;
		if (obj[QuestType.Weekly] !== undefined) count++;
		if (obj[QuestType.Achievement] !== undefined) count++;
		if (obj[QuestType.Tutorial] !== undefined) count++;
		if (obj[QuestType.Event] !== undefined) count++;
		return count;
	}

	private handleQuestEvent(event: QuestEvent): void {
		// Notify global listeners
		this.eventCallbacks.forEach(callback => {
			try {
				callback(event);
			} catch (error) {
				warn(`Quest event callback failed: ${error}`);
			}
		});

		// Handle quest completion
		if (event.type === "completed") {
			this.completedQuests.add(event.questId);
			
			// Check for auto-start quests
			if (this.config.enableAutoProgress) {
				this.checkAutoStartQuests();
			}
		}
	}

	private checkAutoStartQuests(): void {
		const available = this.getAvailableQuests();
		
		available.forEach(definition => {
			if (definition.autoStart && this.activeQuests.size() < this.config.maxActiveQuests) {
				this.startQuest(definition.id);
			}
		});
	}

	private doesEventMatchObjective(eventType: string, data: Record<string, unknown>, objective: QuestObjective): boolean {
		switch (objective.type) {
			case ObjectiveType.KillTarget:
				return eventType === "entity_killed" && data.entityId === objective.targetId;
			
			case ObjectiveType.CollectItem:
				return eventType === "item_collected" && data.itemId === objective.targetId;
			
			case ObjectiveType.ReachLocation:
				return eventType === "location_reached" && data.locationId === objective.targetId;
			
			case ObjectiveType.TalkToNPC:
				return eventType === "npc_talked" && data.npcId === objective.targetId;
			
			case ObjectiveType.UseItem:
				return eventType === "item_used" && data.itemId === objective.targetId;
			
			case ObjectiveType.Craft:
				return eventType === "item_crafted" && data.itemId === objective.targetId;
			
			default:
				return false;
		}
	}

	private loadDefaultQuests(): void {
		// Load some default quest definitions for testing
		const defaultQuests: QuestDefinition[] = [
			{
				id: "tutorial_welcome",
				name: "Welcome to the World",
				description: "Complete your first tutorial objectives",
				type: QuestType.Tutorial,
				category: "Getting Started",
				level: 1,
				isRepeatable: false,
				prerequisites: [],
				objectives: [
					{
						id: "move_around",
						type: ObjectiveType.ReachLocation,
						description: "Move to the marked location",
						targetId: "tutorial_location_1",
						targetCount: 1,
						currentCount: 0,
						isOptional: false,
						isCompleted: false,
						isHidden: false,
					},
					{
						id: "collect_first_item",
						type: ObjectiveType.CollectItem,
						description: "Collect your first item",
						targetId: "tutorial_coin",
						targetCount: 1,
						currentCount: 0,
						isOptional: false,
						isCompleted: false,
						isHidden: false,
					},
				],
				rewards: [
					{
						type: "experience",
						experiencePoints: 100,
					},
					{
						type: "item",
						itemId: "starter_sword",
						quantity: 1,
					},
				],
				autoStart: true,
				autoComplete: true,
				tags: ["tutorial", "beginner"],
			},
		];

		defaultQuests.forEach(quest => {
			this.registerQuest(quest);
		});
	}
}

// Global quest management functions for easy access
export const QuestSystem = {
	getInstance: () => QuestManager.getInstance(),
	startQuest: (questId: string) => QuestManager.getInstance().startQuest(questId),
	getQuest: (questId: string) => QuestManager.getInstance().getQuest(questId),
	processGameEvent: (eventType: string, data: Record<string, unknown>) =>
		QuestManager.getInstance().processGameEvent(eventType, data),
	getReport: () => QuestManager.getInstance().getReport(),
};