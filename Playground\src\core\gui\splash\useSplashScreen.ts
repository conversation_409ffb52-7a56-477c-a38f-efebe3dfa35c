import * as React from "@rbxts/react";
import { SplashScreenManager, SplashScreenState } from "./SplashScreenManager";

export function useSplashScreen() {
	const [state, setState] = React.useState<SplashScreenState>({
		isVisible: true, // Start visible immediately
		loadingProgress: 0,
		loadingText: "Starting up...",
	});

	const manager = React.useMemo(() => SplashScreenManager.getInstance(), []);

	React.useEffect(() => {
		// Set up state change listener
		manager.onStateChanged((newState) => {
			setState(newState);
		});

		// Initialize the manager to be visible immediately
		manager.show(); // Make sure the manager starts visible
	}, [manager]);

	const startLoading = React.useCallback(async () => {
		await manager.startLoading();
	}, [manager]);

	const hide = React.useCallback(() => {
		manager.hide();
	}, [manager]);

	const setupDefaultTasks = React.useCallback(() => {
		manager.setupDefaultCoreTasks();
	}, [manager]);

	return {
		state,
		startLoading,
		hide,
		setupDefaultTasks,
		manager,
	};
}
