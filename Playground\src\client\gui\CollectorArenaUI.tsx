import * as React from "@rbxts/react";
import { 
	Modal, 
	Button, 
	Label, 
	VerticalFrame, 
	HorizontalFrame,
	COLORS,
	SIZES,
	GameModeManager,
	GameMode,
	CollectorArenaGame,
	PlaygroundController,
	ToastService,
} from "../../core";

interface CollectorArenaUIProps {
	isOpen: boolean;
	onClose: () => void;
}

export function CollectorArenaUI({ isOpen, onClose }: CollectorArenaUIProps): React.ReactElement {
	const [gameManager] = React.useState(() => GameModeManager.getInstance());
	const [arenaGame] = React.useState(() => new CollectorArenaGame());
	const [playgroundController] = React.useState(() => new PlaygroundController());
	const [gameState, setGameState] = React.useState(() => arenaGame.getGameState());
	const [currentMode, setCurrentMode] = React.useState(() => gameManager.getCurrentMode());
	const [isGameActive, setIsGameActive] = React.useState(() => gameManager.isGameActiveState());

	// Initialize game controllers
	React.useEffect(() => {
		gameManager.registerGameController(GameMode.CollectorArena, arenaGame);
		gameManager.registerGameController(GameMode.Playground, playgroundController);
		
		print("🎮 [CollectorArenaUI] Game controllers registered");
	}, [gameManager, arenaGame, playgroundController]);

	// Poll for state changes (simple approach using game.GetService)
	React.useEffect(() => {
		const heartbeat = game.GetService("RunService").Heartbeat.Connect(() => {
			setGameState(arenaGame.getGameState());
			setCurrentMode(gameManager.getCurrentMode());
			setIsGameActive(gameManager.isGameActiveState());
		});

		return () => heartbeat.Disconnect();
	}, [arenaGame, gameManager]);

	const handleStartGame = React.useCallback(async () => {
		try {
			// Switch to Collector Arena mode if not already
			if (currentMode !== GameMode.CollectorArena) {
				const switchResult = await gameManager.switchToMode(GameMode.CollectorArena);
				if (switchResult.isError()) {
					ToastService.showError("Game Error", `Failed to switch to game mode: ${switchResult.getError().message}`);
					return;
				}
			}

			// Start the game
			const startResult = await gameManager.startCurrentGame();
			if (startResult.isError()) {
				ToastService.showError("Game Error", `Failed to start game: ${startResult.getError().message}`);
				return;
			}

			ToastService.showSuccess("Game Started", "Collector Arena has begun! Collect coins and avoid enemies!");
			onClose(); // Close the UI when game starts
		} catch (error) {
			ToastService.showError("Game Error", `Unexpected error: ${error}`);
		}
	}, [gameManager, currentMode, onClose]);

	const handleStopGame = React.useCallback(async () => {
		try {
			const stopResult = await gameManager.stopCurrentGame();
			if (stopResult.isError()) {
				ToastService.showError("Game Error", `Failed to stop game: ${stopResult.getError().message}`);
				return;
			}

			ToastService.showInfo("Game Stopped", `Final Score: ${gameState.score} points!`);
		} catch (error) {
			ToastService.showError("Game Error", `Unexpected error: ${error}`);
		}
	}, [gameManager, gameState.score]);

	const handleSwitchToPlayground = React.useCallback(async () => {
		try {
			const switchResult = await gameManager.switchToMode(GameMode.Playground);
			if (switchResult.isError()) {
				ToastService.showError("Mode Error", `Failed to switch to playground: ${switchResult.getError().message}`);
				return;
			}

			ToastService.showInfo("Mode Changed", "Switched back to Playground mode");
			onClose();
		} catch (error) {
			ToastService.showError("Mode Error", `Unexpected error: ${error}`);
		}
	}, [gameManager, onClose]);

	const formatTime = (seconds: number): string => {
		const mins = math.floor(seconds / 60);
		const secs = math.floor(seconds % 60);
		return `${mins}:${secs < 10 ? "0" : ""}${secs}`;
	};

	return (
		<Modal
			isOpen={isOpen}
			onClose={onClose}
			title="🏟️ Collector Arena"
		>
			<VerticalFrame
				backgroundTransparency={1}
				size={new UDim2(1, 0, 1, 0)}
				spacing={SIZES.margin}
				padding={SIZES.padding}
			>
				{/* Current Mode Display */}
				<Label
					text={`Current Mode: ${currentMode}`}
					textColor={currentMode === GameMode.CollectorArena ? COLORS.success : COLORS.primary}
					layoutOrder={1}
				/>

				{/* Game Status */}
				{currentMode === GameMode.CollectorArena && (
					<VerticalFrame
						backgroundColor={COLORS.bg.surface}
						size={new UDim2(1, 0, 0, 150)}
						spacing={SIZES.margin}
						padding={SIZES.padding}
						layoutOrder={2}
					>
						<Label
							text="Game Statistics"
							textColor={COLORS.text.main}
						/>
						
						<Label
							text={`Score: ${gameState.score} | Time: ${formatTime(gameState.timeRemaining)}`}
							textColor={COLORS.text.main}
						/>
						
						<Label
							text={`Coins: ${gameState.coinsCollected} | Enemies: ${gameState.enemiesDefeated}`}
							textColor={COLORS.text.main}
						/>

						<Label
							text={`Difficulty Level: ${gameState.difficulty}`}
							textColor={COLORS.info}
						/>
					</VerticalFrame>
				)}

				{/* Game Instructions */}
				<VerticalFrame
					backgroundColor={COLORS.bg.surface}
					size={new UDim2(1, 0, 0, 120)}
					spacing={SIZES.margin}
					padding={SIZES.padding}
					layoutOrder={3}
				>
					<Label
						text="How to Play"
						textColor={COLORS.text.main}
					/>
					<Label
						text="• Move around to collect yellow coins (+10 points)"
						textColor={COLORS.text.main}
					/>
					<Label
						text="• Avoid red enemies (they get faster over time)"
						textColor={COLORS.text.main}
					/>
					<Label
						text="• Use abilities to defeat enemies (+25 points)"
						textColor={COLORS.text.main}
					/>
					<Label
						text="• Survive for 2 minutes to win!"
						textColor={COLORS.text.main}
					/>
				</VerticalFrame>

				{/* Game Controls */}
				<VerticalFrame
					size={new UDim2(1, 0, 0, 120)}
					spacing={SIZES.margin}
					layoutOrder={4}
				>
					{currentMode === GameMode.CollectorArena ? (
						gameState.isGameActive ? (
							<Button
								text="🛑 Stop Game"
								variant="danger"
								onClick={handleStopGame}
								size={new UDim2(1, 0, 0, 40)}
							/>
						) : (
							<Button
								text="🎮 Start Game"
								variant="primary"
								onClick={handleStartGame}
								size={new UDim2(1, 0, 0, 40)}
							/>
						)
					) : (
						<Button
							text="🏟️ Switch to Collector Arena"
							variant="primary"
							onClick={handleStartGame}
							size={new UDim2(1, 0, 0, 40)}
						/>
					)}

					{currentMode === GameMode.CollectorArena && !gameState.isGameActive && (
						<Button
							text="🛝 Back to Playground"
							variant="secondary"
							onClick={handleSwitchToPlayground}
							size={new UDim2(1, 0, 0, 40)}
							disabled={!gameManager.canSwitchModeState()}
						/>
					)}
				</VerticalFrame>
			</VerticalFrame>
		</Modal>
	);
}