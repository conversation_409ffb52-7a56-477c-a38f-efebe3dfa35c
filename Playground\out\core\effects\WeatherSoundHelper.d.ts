export interface WeatherSoundConfig {
    soundId: string;
    baseVolume: number;
    pitchRange: [number, number];
    looping: boolean;
    fadeInDuration: number;
    fadeOutDuration: number;
    is3D: boolean;
    rollOffMode: Enum.RollOffMode;
    maxDistance: number;
    minDistance: number;
}
export declare class WeatherSoundSystem {
    private static activeSounds;
    private static ambientSounds;
    private static fadeConnections;
    private static readonly WEATHER_SOUNDS;
    /**
     * Play weather ambient sound with proper configuration
     */
    static playWeatherAmbient(weatherType: string, intensity: number): void;
    /**
     * Fade sound volume over time with better performance
     */
    private static fadeSound;
    /**
     * Stop weather ambient sound with fade out
     */
    static stopWeatherAmbient(weatherType: string): void;
    /**
     * Get the appropriate sound key for weather type and intensity
     */
    private static getWeatherSoundKey;
    /**
     * Play layered weather sounds for more realistic audio experience
     */
    static playLayeredWeatherAmbient(weatherType: string, intensity: number): void;
    /**
     * Schedule random thunder sounds for thunderstorms
     */
    private static scheduleThunderSounds;
    /**
     * Play positional thunder sound with spatial effects
     */
    private static playPositionalThunder;
    /**
     * Stop all weather sounds
     */
    static stopAllWeatherSounds(): void;
}
export declare const WeatherSoundHelper: typeof WeatherSoundSystem;
