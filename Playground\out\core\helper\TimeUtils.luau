-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
--[[
	*
	 * Time and timing utilities for game development
	 
]]
local TimeUtils
do
	TimeUtils = setmetatable({}, {
		__tostring = function()
			return "TimeUtils"
		end,
	})
	TimeUtils.__index = TimeUtils
	function TimeUtils.new(...)
		local self = setmetatable({}, TimeUtils)
		return self:constructor(...) or self
	end
	function TimeUtils:constructor()
	end
	function TimeUtils:getGameTime()
		return tick() - self.gameStartTime
	end
	function TimeUtils:getUnixTime()
		return os.time()
	end
	function TimeUtils:getCurrentDateTime()
		return os.date("%Y-%m-%d %H:%M:%S")
	end
	function TimeUtils:getCurrentDate()
		return os.date("%Y-%m-%d")
	end
	function TimeUtils:getCurrentTime()
		return os.date("%H:%M:%S")
	end
	function TimeUtils:formatDuration(seconds)
		local days = math.floor(seconds / 86400)
		local hours = math.floor((seconds % 86400) / 3600)
		local minutes = math.floor((seconds % 3600) / 60)
		local secs = math.floor(seconds % 60)
		local parts = {}
		if days > 0 then
			local _arg0 = `{days}d`
			table.insert(parts, _arg0)
		end
		if hours > 0 then
			local _arg0 = `{hours}h`
			table.insert(parts, _arg0)
		end
		if minutes > 0 then
			local _arg0 = `{minutes}m`
			table.insert(parts, _arg0)
		end
		if secs > 0 or #parts == 0 then
			local _arg0 = `{secs}s`
			table.insert(parts, _arg0)
		end
		return table.concat(parts, " ")
	end
	function TimeUtils:formatDurationLong(seconds)
		local days = math.floor(seconds / 86400)
		local hours = math.floor((seconds % 86400) / 3600)
		local minutes = math.floor((seconds % 3600) / 60)
		local secs = math.floor(seconds % 60)
		local parts = {}
		if days > 0 then
			local _arg0 = `{days} {if days == 1 then "day" else "days"}`
			table.insert(parts, _arg0)
		end
		if hours > 0 then
			local _arg0 = `{hours} {if hours == 1 then "hour" else "hours"}`
			table.insert(parts, _arg0)
		end
		if minutes > 0 then
			local _arg0 = `{minutes} {if minutes == 1 then "minute" else "minutes"}`
			table.insert(parts, _arg0)
		end
		if secs > 0 and #parts == 0 then
			local _arg0 = `{secs} {if secs == 1 then "second" else "seconds"}`
			table.insert(parts, _arg0)
		end
		if #parts == 0 then
			return "0 seconds"
		end
		if #parts == 1 then
			return parts[1]
		end
		if #parts == 2 then
			return `{parts[1]} and {parts[2]}`
		end
		-- More than 2 parts: join with commas and "and"
		-- ▼ Array.pop ▼
		local _length = #parts
		local _result = parts[_length]
		parts[_length] = nil
		-- ▲ Array.pop ▲
		local lastPart = _result
		return `{table.concat(parts, ", ")} and {lastPart}`
	end
	function TimeUtils:hasElapsed(key, requiredInterval)
		local now = tick()
		local _timers = self.timers
		local _key = key
		local _result = _timers[_key]
		if _result ~= nil then
			_result = _result.lastRun
		end
		local _condition = _result
		if not (_condition ~= 0 and _condition == _condition and _condition) then
			_condition = 0
		end
		local lastTime = _condition
		if now - lastTime >= requiredInterval then
			local _timers_1 = self.timers
			local _key_1 = key
			local _arg1 = {
				callback = function() end,
				interval = requiredInterval,
				lastRun = now,
			}
			_timers_1[_key_1] = _arg1
			return true
		end
		return false
	end
	function TimeUtils:setInterval(key, callback, interval)
		local _timers = self.timers
		local _key = key
		local _arg1 = {
			callback = callback,
			interval = interval,
			lastRun = tick(),
		}
		_timers[_key] = _arg1
	end
	function TimeUtils:clearInterval(key)
		local _timers = self.timers
		local _key = key
		_timers[_key] = nil
	end
	function TimeUtils:updateTimers()
		local now = tick()
		for key, timer in self.timers do
			if now - timer.lastRun >= timer.interval then
				timer.callback()
				timer.lastRun = now
			end
		end
	end
	function TimeUtils:onFrame(callback)
		local _frameCallbacks = self.frameCallbacks
		local _callback = callback
		_frameCallbacks[_callback] = true
	end
	function TimeUtils:offFrame(callback)
		local _frameCallbacks = self.frameCallbacks
		local _callback = callback
		_frameCallbacks[_callback] = nil
	end
	function TimeUtils:updateFrame()
		for callback in self.frameCallbacks do
			TS.try(function()
				callback()
			end, function(error)
				warn(`Frame callback error: {error}`)
			end)
		end
	end
	function TimeUtils:createCountdown(duration, onTick, onComplete)
		local startTime = tick()
		local cancelled = false
		local countdown
		countdown = {
			getRemaining = function()
				if cancelled then
					return 0
				end
				local elapsed = tick() - startTime
				return math.max(0, duration - elapsed)
			end,
			isComplete = function()
				return countdown.getRemaining() <= 0 and not cancelled
			end,
			cancel = function()
				cancelled = true
			end,
		}
		-- Set up tick callback if provided
		if onTick then
			local tickKey = `countdown_{tostring(countdown)}`
			self:setInterval(tickKey, function()
				if cancelled then
					self:clearInterval(tickKey)
					return nil
				end
				local remaining = countdown.getRemaining()
				onTick(remaining)
				if remaining <= 0 then
					self:clearInterval(tickKey)
					if onComplete then
						onComplete()
					end
				end
			end, 0.1)
		end
		return countdown
	end
	function TimeUtils:createStopwatch()
		local startTime = 0
		local totalElapsed = 0
		local running = false
		return {
			start = function()
				if not running then
					startTime = tick()
					running = true
				end
			end,
			stop = function()
				if running then
					totalElapsed += tick() - startTime
					running = false
				end
			end,
			reset = function()
				totalElapsed = 0
				running = false
			end,
			getElapsed = function()
				if running then
					return totalElapsed + (tick() - startTime)
				end
				return totalElapsed
			end,
			isRunning = function()
				return running
			end,
		}
	end
	function TimeUtils:calculateFPS(deltaTime)
		return if deltaTime > 0 then math.floor(1 / deltaTime) else 0
	end
	function TimeUtils:createFPSCounter()
		local frameTimes = {}
		local maxSamples = 60
		local currentFPS = 0
		return {
			update = function(deltaTime)
				local _deltaTime = deltaTime
				table.insert(frameTimes, _deltaTime)
				if #frameTimes > maxSamples then
					table.remove(frameTimes, 1)
				end
				currentFPS = self:calculateFPS(deltaTime)
			end,
			getFPS = function()
				return currentFPS
			end,
			getAverageFPS = function()
				if #frameTimes == 0 then
					return 0
				end
				-- ▼ ReadonlyArray.reduce ▼
				local _result = 0
				local _callback = function(sum, time)
					return sum + time
				end
				for _i = 1, #frameTimes do
					_result = _callback(_result, frameTimes[_i], _i - 1, frameTimes)
				end
				-- ▲ ReadonlyArray.reduce ▲
				local avgDelta = _result / #frameTimes
				return self:calculateFPS(avgDelta)
			end,
		}
	end
	TimeUtils.waitFor = TS.async(function(self, condition, timeout, checkInterval)
		if timeout == nil then
			timeout = 10
		end
		if checkInterval == nil then
			checkInterval = 0.1
		end
		local startTime = tick()
		while tick() - startTime < timeout do
			if condition() then
				return true
			end
			task.wait(checkInterval)
		end
		return false
	end)
	function TimeUtils:throttle(key, func, limit)
		local lastCall = 0
		return function(...)
			local args = { ... }
			local now = tick()
			if now - lastCall >= limit then
				lastCall = now
				func(unpack(args))
			end
		end
	end
	function TimeUtils:debounce(key, func, delay)
		local timeoutThread
		return function(...)
			local args = { ... }
			if timeoutThread ~= nil then
				task.cancel(timeoutThread)
			end
			timeoutThread = task.delay(delay, function()
				func(unpack(args))
				timeoutThread = nil
			end)
		end
	end
	function TimeUtils:getTimeUntilNext(unit)
		local now = os.time()
		local date = os.date("*t", now)
		local nextTime
		repeat
			if unit == "minute" then
				nextTime = os.time({
					year = date.year,
					month = date.month,
					day = date.day,
					hour = date.hour,
					min = date.min + 1,
					sec = 0,
				})
				break
			end
			if unit == "hour" then
				nextTime = os.time({
					year = date.year,
					month = date.month,
					day = date.day,
					hour = date.hour + 1,
					min = 0,
					sec = 0,
				})
				break
			end
			if unit == "day" then
				nextTime = os.time({
					year = date.year,
					month = date.month,
					day = date.day + 1,
					hour = 0,
					min = 0,
					sec = 0,
				})
				break
			end
		until true
		return nextTime - now
	end
	function TimeUtils:cleanup()
		table.clear(self.timers)
		table.clear(self.frameCallbacks)
	end
	TimeUtils.timers = {}
	TimeUtils.frameCallbacks = {}
	TimeUtils.gameStartTime = tick()
end
return {
	TimeUtils = TimeUtils,
}
