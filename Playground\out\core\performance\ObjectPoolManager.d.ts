import { BaseService } from "../foundation/BaseService";
import { Result } from "../foundation/types/Result";
import { Error } from "../foundation/types/BrandedTypes";
interface PoolableObject {
    reset(): void;
    isActive(): boolean;
    setActive(active: boolean): void;
}
interface PoolConfiguration {
    initialSize: number;
    maxSize: number;
    expandSize: number;
    shrinkThreshold: number;
    shrinkSize: number;
    enableAutoShrink: boolean;
    autoShrinkInterval: number;
}
interface PoolStats {
    totalCreated: number;
    totalDestroyed: number;
    currentActive: number;
    currentPooled: number;
    peakActive: number;
    poolHits: number;
    poolMisses: number;
    expansions: number;
    shrinks: number;
}
/**
 * Generic object pool for optimizing creation and destruction of frequently used objects
 */
declare class ObjectPool<T extends PoolableObject> {
    private pool;
    private activeObjects;
    private factory;
    private config;
    private stats;
    private lastShrinkTime;
    constructor(factory: () => T, config?: Partial<PoolConfiguration>);
    /**
     * Get an object from the pool
     */
    acquire(): T;
    /**
     * Return an object to the pool
     */
    release(obj: T): void;
    /**
     * Release all active objects back to the pool
     */
    releaseAll(): void;
    /**
     * Clear the entire pool and destroy all objects
     */
    clear(): void;
    /**
     * Get pool statistics
     */
    getStats(): PoolStats;
    /**
     * Get pool configuration
     */
    getConfig(): PoolConfiguration;
    /**
     * Update pool configuration
     */
    updateConfig(newConfig: Partial<PoolConfiguration>): void;
    /**
     * Get efficiency metrics
     */
    getEfficiency(): {
        hitRate: number;
        utilizationRate: number;
        averagePoolSize: number;
    };
    private initializePool;
    private createNewObject;
    private destroyObject;
    private shouldExpand;
    private expandPool;
    private shouldShrink;
    private shrinkPool;
}
/**
 * Object Pool Manager service for managing multiple object pools
 */
export declare class ObjectPoolManager extends BaseService {
    private static instance;
    private pools;
    constructor();
    static getInstance(): ObjectPoolManager;
    protected onInitialize(): Promise<Result<void, Error>>;
    protected onShutdown(): Promise<Result<void, Error>>;
    /**
     * Register a new object pool
     */
    registerPool<T extends PoolableObject>(name: string, factory: () => T, config?: Partial<PoolConfiguration>): ObjectPool<T>;
    /**
     * Get an existing object pool
     */
    getPool<T extends PoolableObject>(name: string): ObjectPool<T> | undefined;
    /**
     * Remove and clear a pool
     */
    removePool(name: string): boolean;
    /**
     * Get all pool names
     */
    getPoolNames(): string[];
    /**
     * Get statistics for all pools
     */
    getAllStats(): Record<string, PoolStats>;
    /**
     * Get efficiency metrics for all pools
     */
    getAllEfficiency(): Record<string, ReturnType<ObjectPool<any>["getEfficiency"]>>;
    /**
     * Generate a comprehensive report of all pools
     */
    getReport(): string;
}
export declare abstract class PoolableBase implements PoolableObject {
    private active;
    isActive(): boolean;
    setActive(active: boolean): void;
    abstract reset(): void;
    protected onActivate(): void;
    protected onDeactivate(): void;
}
export declare const ObjectPooling: {
    getInstance: () => ObjectPoolManager;
    registerPool: <T extends PoolableObject>(name: string, factory: () => T, config?: Partial<PoolConfiguration>) => ObjectPool<T>;
    getPool: <T extends PoolableObject>(name: string) => ObjectPool<T> | undefined;
    getReport: () => string;
};
export type { PoolableObject, PoolConfiguration, PoolStats };
