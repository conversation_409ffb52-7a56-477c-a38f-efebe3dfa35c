-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitFor<PERSON>hild("RuntimeLib"))
local React = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "react")
local ErrorBoundary = TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "error-boundary", "ErrorBoundary").ErrorBoundary
--[[
	*
	 * Test component for verifying error boundary functionality
	 * This component can be used to test that error boundaries properly contain errors
	 
]]
local function ErrorBoundaryTest(props)
	local shouldError, setShouldError = React.useState(false)
	local errorType, setErrorType = React.useState("throw")
	if not props.isOpen then
		return React.createElement(React.Fragment)
	end
	-- Test component that throws errors on demand
	local function TestErrorComponent()
		if shouldError then
			repeat
				local _fallthrough = false
				if errorType == "throw" then
					error("Test error: Component deliberately throwing an error")
					_fallthrough = true
				end
				if _fallthrough or errorType == "nil" then
					-- Simulate calling a nil value
					local nilFunction = nil
					nilFunction()
					break
				end
				if errorType == "runtime" then
					-- Simulate a runtime error
					local obj = nil
					print(obj.nonExistentProperty)
					break
				end
				error("Unknown error type")
			until true
		end
		return React.createElement("frame", {
			Size = UDim2.new(1, 0, 1, 0),
			BackgroundTransparency = 1,
		}, React.createElement("uilistlayout", {
			SortOrder = Enum.SortOrder.LayoutOrder,
			FillDirection = Enum.FillDirection.Vertical,
			HorizontalAlignment = Enum.HorizontalAlignment.Center,
			Padding = UDim.new(0, 8),
		}), React.createElement("uipadding", {
			PaddingTop = UDim.new(0, 16),
			PaddingBottom = UDim.new(0, 16),
			PaddingLeft = UDim.new(0, 16),
			PaddingRight = UDim.new(0, 16),
		}), React.createElement("textlabel", {
			Text = "🧪 Error Test Component",
			Size = UDim2.new(1, 0, 0, 24),
			BackgroundTransparency = 1,
			TextColor3 = Color3.fromHex("#ffffff"),
			TextSize = 16,
			Font = Enum.Font.SourceSansBold,
			TextXAlignment = Enum.TextXAlignment.Center,
			LayoutOrder = 1,
		}), React.createElement("textlabel", {
			Text = "This component will throw an error when you click the trigger button. The error boundary should catch it and show a fallback UI.",
			Size = UDim2.new(1, 0, 0, 60),
			BackgroundTransparency = 1,
			TextColor3 = Color3.fromHex("#cccccc"),
			TextSize = 12,
			Font = Enum.Font.SourceSans,
			TextXAlignment = Enum.TextXAlignment.Center,
			TextWrapped = true,
			LayoutOrder = 2,
		}), React.createElement("textlabel", {
			Text = "Select error type:",
			Size = UDim2.new(1, 0, 0, 20),
			BackgroundTransparency = 1,
			TextColor3 = Color3.fromHex("#ffffff"),
			TextSize = 14,
			Font = Enum.Font.SourceSansBold,
			TextXAlignment = Enum.TextXAlignment.Center,
			LayoutOrder = 3,
		}), React.createElement("textbutton", {
			Text = if errorType == "throw" then "✓ Throw Error" else "Throw Error",
			Size = UDim2.new(1, -32, 0, 32),
			BackgroundColor3 = if errorType == "throw" then Color3.fromHex("#4a9eff") else Color3.fromHex("#666666"),
			BorderSizePixel = 0,
			TextColor3 = Color3.fromHex("#ffffff"),
			TextSize = 14,
			Font = Enum.Font.SourceSans,
			LayoutOrder = 4,
			Event = {
				Activated = function()
					return setErrorType("throw")
				end,
			},
		}, React.createElement("uicorner", {
			CornerRadius = UDim.new(0, 4),
		})), React.createElement("textbutton", {
			Text = if errorType == "nil" then "✓ Nil Call Error" else "Nil Call Error",
			Size = UDim2.new(1, -32, 0, 32),
			BackgroundColor3 = if errorType == "nil" then Color3.fromHex("#4a9eff") else Color3.fromHex("#666666"),
			BorderSizePixel = 0,
			TextColor3 = Color3.fromHex("#ffffff"),
			TextSize = 14,
			Font = Enum.Font.SourceSans,
			LayoutOrder = 5,
			Event = {
				Activated = function()
					return setErrorType("nil")
				end,
			},
		}, React.createElement("uicorner", {
			CornerRadius = UDim.new(0, 4),
		})), React.createElement("textbutton", {
			Text = if errorType == "runtime" then "✓ Runtime Error" else "Runtime Error",
			Size = UDim2.new(1, -32, 0, 32),
			BackgroundColor3 = if errorType == "runtime" then Color3.fromHex("#4a9eff") else Color3.fromHex("#666666"),
			BorderSizePixel = 0,
			TextColor3 = Color3.fromHex("#ffffff"),
			TextSize = 14,
			Font = Enum.Font.SourceSans,
			LayoutOrder = 6,
			Event = {
				Activated = function()
					return setErrorType("runtime")
				end,
			},
		}, React.createElement("uicorner", {
			CornerRadius = UDim.new(0, 4),
		})), React.createElement("textbutton", {
			Text = "🔥 TRIGGER ERROR",
			Size = UDim2.new(1, -32, 0, 40),
			BackgroundColor3 = Color3.fromHex("#ff4444"),
			BorderSizePixel = 0,
			TextColor3 = Color3.fromHex("#ffffff"),
			TextSize = 16,
			Font = Enum.Font.SourceSansBold,
			LayoutOrder = 7,
			Event = {
				Activated = function()
					print(`🧪 Triggering {errorType} error...`)
					setShouldError(true)
				end,
			},
		}, React.createElement("uicorner", {
			CornerRadius = UDim.new(0, 4),
		})))
	end
	return React.createElement("frame", {
		Size = UDim2.new(0, 400, 0, 500),
		Position = UDim2.new(0.5, 0, 0.5, 0),
		AnchorPoint = Vector2.new(0.5, 0.5),
		BackgroundColor3 = Color3.fromHex("#2a2a2a"),
		BorderSizePixel = 2,
		BorderColor3 = Color3.fromHex("#4a9eff"),
	}, React.createElement("uicorner", {
		CornerRadius = UDim.new(0, 8),
	}), React.createElement("uipadding", {
		PaddingTop = UDim.new(0, 16),
		PaddingBottom = UDim.new(0, 16),
		PaddingLeft = UDim.new(0, 16),
		PaddingRight = UDim.new(0, 16),
	}), React.createElement("uilistlayout", {
		SortOrder = Enum.SortOrder.LayoutOrder,
		FillDirection = Enum.FillDirection.Vertical,
		HorizontalAlignment = Enum.HorizontalAlignment.Center,
		Padding = UDim.new(0, 8),
	}), React.createElement("frame", {
		Size = UDim2.new(1, 0, 0, 40),
		BackgroundTransparency = 1,
		LayoutOrder = 1,
	}, React.createElement("uilistlayout", {
		SortOrder = Enum.SortOrder.LayoutOrder,
		FillDirection = Enum.FillDirection.Horizontal,
		VerticalAlignment = Enum.VerticalAlignment.Center,
	}), React.createElement("textlabel", {
		Text = "🧪 Error Boundary Test",
		Size = UDim2.new(1, -40, 1, 0),
		BackgroundTransparency = 1,
		TextColor3 = Color3.fromHex("#ffffff"),
		TextSize = 18,
		Font = Enum.Font.SourceSansBold,
		TextXAlignment = Enum.TextXAlignment.Left,
		LayoutOrder = 1,
	}), React.createElement("textbutton", {
		Text = "✕",
		Size = UDim2.new(0, 32, 0, 32),
		BackgroundColor3 = Color3.fromHex("#ff4444"),
		BorderSizePixel = 0,
		TextColor3 = Color3.fromHex("#ffffff"),
		TextSize = 16,
		Font = Enum.Font.SourceSansBold,
		LayoutOrder = 2,
		Event = {
			Activated = props.onClose,
		},
	}, React.createElement("uicorner", {
		CornerRadius = UDim.new(0, 4),
	}))), React.createElement("frame", {
		Size = UDim2.new(1, 0, 1, -50),
		BackgroundTransparency = 1,
		LayoutOrder = 2,
	}, React.createElement(ErrorBoundary, {
		onError = function(err, errorId)
			warn(`🧪 [ErrorBoundaryTest] Caught test error: {err} (ID: {errorId})`)
			print("✅ Error boundary is working correctly!")
		end,
		fallback = function(err, errorId, retry)
			return React.createElement("frame", {
				Size = UDim2.new(1, 0, 1, 0),
				BackgroundTransparency = 1,
			}, React.createElement("uilistlayout", {
				SortOrder = Enum.SortOrder.LayoutOrder,
				FillDirection = Enum.FillDirection.Vertical,
				HorizontalAlignment = Enum.HorizontalAlignment.Center,
				Padding = UDim.new(0, 8),
			}), React.createElement("uipadding", {
				PaddingTop = UDim.new(0, 16),
				PaddingBottom = UDim.new(0, 16),
				PaddingLeft = UDim.new(0, 16),
				PaddingRight = UDim.new(0, 16),
			}), React.createElement("textlabel", {
				Text = "✅ Error Boundary Works!",
				Size = UDim2.new(1, 0, 0, 24),
				BackgroundTransparency = 1,
				TextColor3 = Color3.fromHex("#44ff44"),
				TextSize = 16,
				Font = Enum.Font.SourceSansBold,
				TextXAlignment = Enum.TextXAlignment.Center,
				LayoutOrder = 1,
			}), React.createElement("textlabel", {
				Text = `Successfully caught error: {err}`,
				Size = UDim2.new(1, 0, 0, 60),
				BackgroundTransparency = 1,
				TextColor3 = Color3.fromHex("#cccccc"),
				TextSize = 12,
				Font = Enum.Font.SourceSans,
				TextXAlignment = Enum.TextXAlignment.Center,
				TextWrapped = true,
				LayoutOrder = 2,
			}), React.createElement("textlabel", {
				Text = `Error ID: {errorId}`,
				Size = UDim2.new(1, 0, 0, 20),
				BackgroundTransparency = 1,
				TextColor3 = Color3.fromHex("#888888"),
				TextSize = 12,
				Font = Enum.Font.SourceSans,
				TextXAlignment = Enum.TextXAlignment.Center,
				LayoutOrder = 3,
			}), React.createElement("textbutton", {
				Text = "🔄 Reset Test",
				Size = UDim2.new(1, -32, 0, 32),
				BackgroundColor3 = Color3.fromHex("#4a9eff"),
				BorderSizePixel = 0,
				TextColor3 = Color3.fromHex("#ffffff"),
				TextSize = 14,
				Font = Enum.Font.SourceSansBold,
				LayoutOrder = 4,
				Event = {
					Activated = retry,
				},
			}, React.createElement("uicorner", {
				CornerRadius = UDim.new(0, 4),
			})), React.createElement("textlabel", {
				Text = "This proves that the error boundary is properly isolating component failures and preventing UI crashes.",
				Size = UDim2.new(1, 0, 0, 40),
				BackgroundTransparency = 1,
				TextColor3 = Color3.fromHex("#aaaaaa"),
				TextSize = 12,
				Font = Enum.Font.SourceSans,
				TextXAlignment = Enum.TextXAlignment.Center,
				TextWrapped = true,
				LayoutOrder = 5,
			}))
		end,
	}, React.createElement(TestErrorComponent))), React.createElement("frame", {
		Size = UDim2.new(1, 0, 0, 80),
		BackgroundTransparency = 1,
		LayoutOrder = 3,
	}, React.createElement("textlabel", {
		Text = "Instructions: Select an error type and click 'TRIGGER ERROR'. The error boundary should catch it and show a success message instead of crashing the UI.",
		Size = UDim2.new(1, 0, 1, 0),
		BackgroundTransparency = 1,
		TextColor3 = Color3.fromHex("#aaaaaa"),
		TextSize = 12,
		Font = Enum.Font.SourceSans,
		TextWrapped = true,
		TextXAlignment = Enum.TextXAlignment.Center,
		TextYAlignment = Enum.TextYAlignment.Top,
	})))
end
return {
	ErrorBoundaryTest = ErrorBoundaryTest,
}
