import { RunService } from "@rbxts/services";

export interface LoadingTask {
	name: string;
	weight: number; // Relative weight for progress calculation
	task: () => Promise<void> | void;
}

export interface SplashScreenState {
	isVisible: boolean;
	loadingProgress: number;
	loadingText: string;
}

export class SplashScreenManager {
	private static instance?: SplashScreenManager;
	private loadingTasks: LoadingTask[] = [];
	private currentProgress = 0;
	private currentText = "Starting up...";
	private isVisible = true; // Start visible immediately
	private onStateChange?: (state: SplashScreenState) => void;

	private constructor() {}

	public static getInstance(): SplashScreenManager {
		if (!this.instance) {
			this.instance = new SplashScreenManager();
		}
		return this.instance;
	}

	/**
	 * Add a loading task to be executed during splash screen
	 */
	public addLoadingTask(task: LoadingTask): void {
		this.loadingTasks.push(task);
	}

	/**
	 * Add multiple loading tasks
	 */
	public addLoadingTasks(tasks: LoadingTask[]): void {
		for (const task of tasks) {
			this.loadingTasks.push(task);
		}
	}

	/**
	 * Set the state change callback
	 */
	public onStateChanged(callback: (state: SplashScreenState) => void): void {
		this.onStateChange = callback;
	}

	/**
	 * Start the loading process with enhanced error handling and validation
	 */
	public async startLoading(): Promise<void> {
		this.isVisible = true;
		this.currentProgress = 0;
		this.currentText = "Initializing Core Framework...";
		this.notifyStateChange();

		// Ensure we have tasks to run
		if (this.loadingTasks.size() === 0) {
			print("⚠️ No loading tasks found, setting up default tasks...");
			this.setupDefaultCoreTasks();
		}

		const totalWeight = this.loadingTasks.reduce((sum, task) => sum + task.weight, 0);
		if (totalWeight === 0) {
			warn("⚠️ Total weight is 0, this may cause division by zero issues");
			this.currentProgress = 1;
			this.currentText = "Loading Complete!";
			this.notifyStateChange();
			return;
		}

		let completedWeight = 0;
		let successfulTasks = 0;
		let failedTasks = 0;

		print(`🚀 Starting loading process with ${this.loadingTasks.size()} tasks (total weight: ${totalWeight})`);

		for (let i = 0; i < this.loadingTasks.size(); i++) {
			const task = this.loadingTasks[i];
			this.currentText = task.name;
			this.notifyStateChange();

			print(`📋 [${i + 1}/${this.loadingTasks.size()}] ${task.name} (weight: ${task.weight})`);

			try {
				// Execute the task with timeout protection
				const result = task.task();
				if (result !== undefined) {
					// Add a reasonable timeout for async tasks
					const timeoutPromise = new Promise<void>((_, reject) => {
						this.delay(10).then(() => reject("Task timeout after 10 seconds"));
					});
					
					await Promise.race([result, timeoutPromise]);
				}

				// Update progress
				completedWeight += task.weight;
				this.currentProgress = math.min(completedWeight / totalWeight, 1); // Ensure we don't exceed 1
				this.notifyStateChange();
				successfulTasks++;

				print(`✅ [${i + 1}/${this.loadingTasks.size()}] ${task.name} completed successfully`);

				// Small delay for visual feedback
				await this.delay(0.1);
			} catch (error) {
				failedTasks++;
				warn(`❌ [${i + 1}/${this.loadingTasks.size()}] Loading task failed: ${task.name} - ${error}`);
				
				// Show error message but continue loading
				this.currentText = `⚠️ ${task.name} (failed - continuing...)`;
				this.notifyStateChange();
				await this.delay(0.5); // Show error briefly
				
				// Still count partial progress for failed tasks to ensure we make progress
				completedWeight += task.weight * 0.5; // Give partial credit
				this.currentProgress = math.min(completedWeight / totalWeight, 1);
				this.notifyStateChange();
			}
		}

		// Ensure we reach 100% regardless of task results
		this.currentProgress = 1;
		
		// Show appropriate completion message
		if (failedTasks === 0) {
			this.currentText = "🎉 Loading Complete!";
			print(`✅ All ${successfulTasks} loading tasks completed successfully!`);
		} else {
			this.currentText = `⚠️ Loading Complete (${failedTasks} warnings)`;
			print(`⚠️ Loading completed with ${successfulTasks} successful and ${failedTasks} failed tasks`);
		}
		
		this.notifyStateChange();

		// Wait longer before hiding for better user experience and to show final status
		await this.delay(failedTasks > 0 ? 3.0 : 2.0); // Longer delay if there were failures
		
		print("🎯 Splash screen loading process completed!");
	}

	/**
	 * Show the splash screen
	 */
	public show(): void {
		this.isVisible = true;
		this.currentText = "Starting up...";
		this.currentProgress = 0;
		this.notifyStateChange();
	}

	/**
	 * Hide the splash screen
	 */
	public hide(): void {
		this.isVisible = false;
		this.notifyStateChange();
	}

	/**
	 * Get current state
	 */
	public getState(): SplashScreenState {
		return {
			isVisible: this.isVisible,
			loadingProgress: this.currentProgress,
			loadingText: this.currentText,
		};
	}

	/**
	 * Setup default core framework loading tasks with better error handling
	 */
	public setupDefaultCoreTasks(): void {
		print("🔧 Setting up default core framework loading tasks...");
		
		this.addLoadingTasks([
			{
				name: "Initializing Physics System...",
				weight: 1,
				task: async () => {
					try {
						// Import and initialize physics
						const { PhysicsImpactHelper } = await import("../../world/physics/PhysicsImpactHelper");
						PhysicsImpactHelper.initialize();
						print("✅ Physics system initialized");
					} catch (error) {
						warn(`⚠️ Physics system initialization failed: ${error}`);
						// Continue without physics - not critical for basic functionality
					}
				},
			},
			{
				name: "Loading Weather System...",
				weight: 1,
				task: async () => {
					try {
						// Import weather systems
						await import("../../world/state/WeatherController");
						await import("../../effects/WeatherParticleHelper");
						await import("../../effects/WeatherSoundHelper");
						print("✅ Weather system loaded");
					} catch (error) {
						warn(`⚠️ Weather system loading failed: ${error}`);
						// Continue without weather - not critical for basic functionality
					}
				},
			},
			{
				name: "Initializing UI Framework...",
				weight: 2, // Higher weight as this is more critical
				task: async () => {
					try {
						// Import UI components
						await import("../layout/ZIndexManager");
						const { ResponsiveManager } = await import("../layout/ResponsiveManager");
						ResponsiveManager.getInstance();
						print("✅ UI framework initialized");
					} catch (error) {
						warn(`⚠️ UI framework initialization failed: ${error}`);
						// UI is more critical, but still continue
					}
				},
			},
			{
				name: "Loading Sound System...",
				weight: 1,
				task: async () => {
					try {
						// Import sound helpers
						await import("../../effects/SoundHelper");
						print("✅ Sound system loaded");
					} catch (error) {
						warn(`⚠️ Sound system loading failed: ${error}`);
						// Continue without sound - not critical for basic functionality
					}
				},
			},
			{
				name: "Initializing Animation System...",
				weight: 1,
				task: async () => {
					try {
						// Import animation systems
						await import("../../animations/AnimationBuilder");
						await import("../../animations/LimbAnimator");
						print("✅ Animation system initialized");
					} catch (error) {
						warn(`⚠️ Animation system initialization failed: ${error}`);
						// Continue without animations - not critical for basic functionality
					}
				},
			},
			{
				name: "Loading Entity Management...",
				weight: 1,
				task: async () => {
					try {
						// Import entity systems
						await import("../../entities/EntityManager");
						print("✅ Entity management loaded");
					} catch (error) {
						warn(`⚠️ Entity management loading failed: ${error}`);
						// Continue without entity management - not critical for basic UI
					}
				},
			},
			{
				name: "Validating Core Systems...",
				weight: 1,
				task: async () => {
					try {
						// Perform validation checks
						const runService = game.GetService("RunService");
						const workspace = game.GetService("Workspace");
						const players = game.GetService("Players");
						
						// Basic service validation
						if (!runService || !workspace || !players) {
							throw "Critical Roblox services are not available";
						}
						
						// Check if we're in a valid game environment
						if (!players.LocalPlayer) {
							throw "LocalPlayer is not available";
						}
						
						print("✅ Core systems validation passed");
					} catch (error) {
						warn(`⚠️ Core systems validation failed: ${error}`);
						// Continue anyway - we'll handle these issues as they arise
					}
				},
			},
			{
				name: "Finalizing Core Framework...",
				weight: 1,
				task: async () => {
					// Final initialization and setup
					print("🎯 Core Framework loaded successfully!");
					print("📋 All systems ready for user interaction");
					
					// Small additional delay to ensure everything is settled
					await this.delay(0.5);
				},
			},
		]);
		
		print(`📋 Set up ${this.loadingTasks.size()} default loading tasks`);
	}

	private notifyStateChange(): void {
		if (this.onStateChange) {
			this.onStateChange(this.getState());
		}
	}

	private delay(seconds: number): Promise<void> {
		return new Promise((resolve) => {
			task.delay(seconds, resolve);
		});
	}
}
