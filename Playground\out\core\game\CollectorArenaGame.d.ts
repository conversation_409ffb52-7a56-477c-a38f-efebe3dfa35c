import { GameController } from "./GameModeManager";
import { Result } from "../foundation/types/Result";
import { Error } from "../foundation/types/RobloxError";
import { GameScore } from "./CollectorArenaScoreManager";
export interface CollectorArenaState {
    score: number;
    coinsCollected: number;
    enemiesDefeated: number;
    timeRemaining: number;
    isGameActive: boolean;
    gamePhase: "waiting" | "playing" | "ended";
    difficulty: number;
}
export interface CollectorArenaConfig {
    gameDuration: number;
    arenaSize: number;
    coinSpawnRate: number;
    enemySpawnRate: number;
    maxCoins: number;
    maxEnemies: number;
    coinValue: number;
    enemyDefeatValue: number;
    arenaCenter: Vector3;
}
export declare class CollectorArenaGame implements GameController {
    private entityManager;
    private aiController;
    private scoreManager;
    private gameLoop?;
    private coinSpawnLoop?;
    private enemySpawnLoop?;
    private config;
    private spawnedCoins;
    private spawnedEnemies;
    private player;
    private startTime;
    private gameState;
    constructor(config?: Partial<CollectorArenaConfig>);
    getName(): string;
    getGameState(): CollectorArenaState;
    start(): Promise<Result<void, Error>>;
    stop(): Promise<Result<void, Error>>;
    cleanup(): Promise<Result<void, Error>>;
    private createArena;
    private createWall;
    private startGameLoop;
    private startCoinSpawning;
    private startEnemySpawning;
    private spawnCoin;
    private spawnEnemy;
    private getRandomArenaPosition;
    private checkCoinCollection;
    private collectCoin;
    private saveFinalScore;
    getPersonalBest(): Promise<GameScore | undefined>;
    getRecentGames(): Promise<GameScore[]>;
}
