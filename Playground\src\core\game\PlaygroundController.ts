import { GameController } from "./GameModeManager";
import { Result } from "../foundation/types/Result";
import { Error } from "../foundation/types/RobloxError";

export class PlaygroundController implements GameController {
	public getName(): string {
		return "Playground Mode";
	}

	public async start(): Promise<Result<void, Error>> {
		print("🛝 [PlaygroundController] Playground mode activated");
		return Result.ok(undefined);
	}

	public async stop(): Promise<Result<void, Error>> {
		print("🛝 [PlaygroundController] Playground mode deactivated");
		return Result.ok(undefined);
	}

	public async cleanup(): Promise<Result<void, Error>> {
		print("🛝 [PlaygroundController] Playground cleanup completed");
		return Result.ok(undefined);
	}
}