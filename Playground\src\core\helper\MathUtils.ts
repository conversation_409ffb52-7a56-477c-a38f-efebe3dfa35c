/**
 * Advanced mathematical utilities for game development
 */
export class MathUtils {
	/**
	 * Linear interpolation between two values
	 */
	static lerp(a: number, b: number, t: number): number {
		return a + (b - a) * t;
	}

	/**
	 * Vector3 linear interpolation
	 */
	static lerpVector3(a: Vector3, b: Vector3, t: number): Vector3 {
		return new Vector3(
			this.lerp(a.X, b.X, t),
			this.lerp(a.Y, b.Y, t),
			this.lerp(a.Z, b.Z, t)
		);
	}

	/**
	 * Clamp a value between min and max
	 */
	static clamp(value: number, min: number, max: number): number {
		return math.max(min, math.min(max, value));
	}

	/**
	 * Smooth step interpolation (eased cubic curve)
	 */
	static smoothStep(t: number): number {
		return t * t * (3 - 2 * t);
	}

	/**
	 * Smoother step interpolation (eased quintic curve)
	 */
	static smootherStep(t: number): number {
		return t * t * t * (t * (t * 6 - 15) + 10);
	}

	/**
	 * Map a value from one range to another
	 */
	static map(value: number, inMin: number, inMax: number, outMin: number, outMax: number): number {
		return outMin + (outMax - outMin) * ((value - inMin) / (inMax - inMin));
	}

	/**
	 * Calculate distance between two Vector3 points
	 */
	static distance(a: Vector3, b: Vector3): number {
		return a.sub(b).Magnitude;
	}

	/**
	 * Calculate squared distance (faster for comparisons)
	 */
	static distanceSquared(a: Vector3, b: Vector3): number {
		const diff = a.sub(b);
		return diff.X * diff.X + diff.Y * diff.Y + diff.Z * diff.Z;
	}

	/**
	 * Check if a point is within a sphere
	 */
	static isInSphere(point: Vector3, center: Vector3, radius: number): boolean {
		return this.distanceSquared(point, center) <= radius * radius;
	}

	/**
	 * Check if a point is within a box
	 */
	static isInBox(point: Vector3, boxCenter: Vector3, boxSize: Vector3): boolean {
		const halfSize = boxSize.div(2);
		const diff = point.sub(boxCenter);
		return math.abs(diff.X) <= halfSize.X && 
			   math.abs(diff.Y) <= halfSize.Y && 
			   math.abs(diff.Z) <= halfSize.Z;
	}

	/**
	 * Generate random point within a sphere
	 */
	static randomPointInSphere(center: Vector3, radius: number): Vector3 {
		// Use rejection sampling for uniform distribution
		let point: Vector3;
		do {
			point = new Vector3(
				math.random(-radius, radius),
				math.random(-radius, radius),
				math.random(-radius, radius)
			);
		} while (point.Magnitude > radius);
		
		return center.add(point);
	}

	/**
	 * Generate random point on sphere surface
	 */
	static randomPointOnSphere(center: Vector3, radius: number): Vector3 {
		// Use spherical coordinates for uniform distribution
		const theta = math.random() * math.pi * 2;
		const phi = math.acos(2 * math.random() - 1);
		
		const x = radius * math.sin(phi) * math.cos(theta);
		const y = radius * math.sin(phi) * math.sin(theta);
		const z = radius * math.cos(phi);
		
		return center.add(new Vector3(x, y, z));
	}

	/**
	 * Convert degrees to radians
	 */
	static deg2rad(degrees: number): number {
		return degrees * (math.pi / 180);
	}

	/**
	 * Convert radians to degrees
	 */
	static rad2deg(radians: number): number {
		return radians * (180 / math.pi);
	}

	/**
	 * Calculate angle between two vectors (in radians)
	 */
	static angleBetween(a: Vector3, b: Vector3): number {
		const dot = a.Unit.Dot(b.Unit);
		return math.acos(math.clamp(dot, -1, 1));
	}

	/**
	 * Rotate a Vector3 around Y axis
	 */
	static rotateAroundY(vector: Vector3, angleRadians: number): Vector3 {
		const cos = math.cos(angleRadians);
		const sin = math.sin(angleRadians);
		
		return new Vector3(
			vector.X * cos - vector.Z * sin,
			vector.Y,
			vector.X * sin + vector.Z * cos
		);
	}

	/**
	 * Round number to specified decimal places
	 */
	static round(value: number, decimals = 0): number {
		const multiplier = math.pow(10, decimals);
		return math.floor(value * multiplier + 0.5) / multiplier;
	}

	/**
	 * Check if number is approximately equal (within epsilon)
	 */
	static approximately(a: number, b: number, epsilon = 0.001): boolean {
		return math.abs(a - b) < epsilon;
	}

	/**
	 * Wrap angle to -π to π range
	 */
	static wrapAngle(angle: number): number {
		while (angle > math.pi) {
			angle -= 2 * math.pi;
		}
		while (angle < -math.pi) {
			angle += 2 * math.pi;
		}
		return angle;
	}

	/**
	 * Calculate shortest angular distance between two angles
	 */
	static angularDistance(from: number, to: number): number {
		const diff = this.wrapAngle(to - from);
		return diff;
	}
}