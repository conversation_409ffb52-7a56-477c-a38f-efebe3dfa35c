-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local Result = TS.import(script, game:GetService("ReplicatedStorage"), "core", "foundation", "types", "Result").Result
local createError = TS.import(script, game:GetService("ReplicatedStorage"), "core", "foundation", "types", "RobloxError").createError
local EntityManager = TS.import(script, game:GetService("ReplicatedStorage"), "core", "entities", "EntityManager").EntityManager
local EntityType = TS.import(script, game:GetService("ReplicatedStorage"), "core", "entities", "enums", "EntityType").EntityType
local AIController = TS.import(script, game:GetService("ReplicatedStorage"), "core", "ai", "AIController").AIController
local _services = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "services")
local RunService = _services.RunService
local Players = _services.Players
local Workspace = _services.Workspace
local playSound = TS.import(script, game:GetService("ReplicatedStorage"), "core", "effects", "SoundHelper").playSound
local createParticleExplosion = TS.import(script, game:GetService("ReplicatedStorage"), "core", "effects", "ParticleHelper").createParticleExplosion
local createImpactFlash = TS.import(script, game:GetService("ReplicatedStorage"), "core", "effects", "VisualEffectUtils").createImpactFlash
local CollectorArenaScoreManager = TS.import(script, game:GetService("ReplicatedStorage"), "core", "game", "CollectorArenaScoreManager").CollectorArenaScoreManager
local CollectorArenaGame
do
	CollectorArenaGame = setmetatable({}, {
		__tostring = function()
			return "CollectorArenaGame"
		end,
	})
	CollectorArenaGame.__index = CollectorArenaGame
	function CollectorArenaGame.new(...)
		local self = setmetatable({}, CollectorArenaGame)
		return self:constructor(...) or self
	end
	function CollectorArenaGame:constructor(config)
		self.spawnedCoins = {}
		self.spawnedEnemies = {}
		self.startTime = 0
		self.player = Players.LocalPlayer
		self.entityManager = EntityManager:getInstance()
		self.aiController = AIController:getInstance()
		self.scoreManager = CollectorArenaScoreManager:getInstance()
		-- Default configuration
		local _object = {
			gameDuration = 120,
			arenaSize = 50,
			coinSpawnRate = 2,
			enemySpawnRate = 0.5,
			maxCoins = 10,
			maxEnemies = 5,
			coinValue = 10,
			enemyDefeatValue = 25,
			arenaCenter = Vector3.new(0, 5, 0),
		}
		if config then
			for _k, _v in config do
				_object[_k] = _v
			end
		end
		self.config = _object
		self.gameState = {
			score = 0,
			coinsCollected = 0,
			enemiesDefeated = 0,
			timeRemaining = self.config.gameDuration,
			isGameActive = false,
			gamePhase = "waiting",
			difficulty = 1,
		}
		print(`🏟️ [CollectorArena] Game initialized with config:`, self.config)
	end
	function CollectorArenaGame:getName()
		return "Collector Arena"
	end
	function CollectorArenaGame:getGameState()
		local _object = table.clone(self.gameState)
		setmetatable(_object, nil)
		return _object
	end
	CollectorArenaGame.start = TS.async(function(self)
		local _exitType, _returns = TS.try(function()
			print("🏟️ [CollectorArena] Starting game...")
			-- Reset game state
			self.startTime = tick()
			self.gameState = {
				score = 0,
				coinsCollected = 0,
				enemiesDefeated = 0,
				timeRemaining = self.config.gameDuration,
				isGameActive = true,
				gamePhase = "playing",
				difficulty = 1,
			}
			-- Clear any existing entities
			TS.await(self:cleanup())
			-- Create arena boundaries
			self:createArena()
			-- Start game loops
			self:startGameLoop()
			self:startCoinSpawning()
			self:startEnemySpawning()
			-- Move player to arena center
			local character = self.player.Character
			if character and character:FindFirstChild("HumanoidRootPart") then
				local rootPart = character:FindFirstChild("HumanoidRootPart")
				rootPart.CFrame = CFrame.new(self.config.arenaCenter)
			end
			-- Play start sound and effect
			playSound("rbxasset://sounds/electronicpingshort.wav", 0.5)
			createParticleExplosion(self.config.arenaCenter, 20, Color3.fromRGB(0, 255, 0), { 10, 30 }, { 0.5, 1.5 })
			print("🏟️ [CollectorArena] Game started successfully!")
			return TS.TRY_RETURN, { Result:ok(nil) }
		end, function(error)
			return TS.TRY_RETURN, { Result:err(createError(`Failed to start Collector Arena: {error}`)) }
		end)
		if _exitType then
			return unpack(_returns)
		end
	end)
	CollectorArenaGame.stop = TS.async(function(self)
		local _exitType, _returns = TS.try(function()
			print("🏟️ [CollectorArena] Stopping game...")
			self.gameState.isGameActive = false
			self.gameState.gamePhase = "ended"
			-- Stop all loops
			if self.gameLoop then
				self.gameLoop:Disconnect()
				self.gameLoop = nil
			end
			if self.coinSpawnLoop then
				self.coinSpawnLoop:Disconnect()
				self.coinSpawnLoop = nil
			end
			if self.enemySpawnLoop then
				self.enemySpawnLoop:Disconnect()
				self.enemySpawnLoop = nil
			end
			-- Play end sound and effect
			playSound("rbxasset://sounds/button_rollover.wav", 0.5)
			createImpactFlash(self.config.arenaCenter, 30, 1)
			-- Save the final score
			TS.await(self:saveFinalScore())
			print(`🏟️ [CollectorArena] Game ended! Final score: {self.gameState.score}`)
			return TS.TRY_RETURN, { Result:ok(nil) }
		end, function(error)
			return TS.TRY_RETURN, { Result:err(createError(`Failed to stop Collector Arena: {error}`)) }
		end)
		if _exitType then
			return unpack(_returns)
		end
	end)
	CollectorArenaGame.cleanup = TS.async(function(self)
		local _exitType, _returns = TS.try(function()
			print("🏟️ [CollectorArena] Cleaning up game...")
			-- Remove all spawned coins
			for coinId in self.spawnedCoins do
				self.entityManager:destroyEntity(coinId)
			end
			table.clear(self.spawnedCoins)
			-- Remove all spawned enemies
			for enemyId in self.spawnedEnemies do
				self.entityManager:destroyEntity(enemyId)
			end
			table.clear(self.spawnedEnemies)
			-- Remove arena boundaries
			local arena = Workspace:FindFirstChild("CollectorArena")
			if arena then
				arena:Destroy()
			end
			print("🏟️ [CollectorArena] Cleanup completed")
			return TS.TRY_RETURN, { Result:ok(nil) }
		end, function(error)
			return TS.TRY_RETURN, { Result:err(createError(`Failed to cleanup Collector Arena: {error}`)) }
		end)
		if _exitType then
			return unpack(_returns)
		end
	end)
	function CollectorArenaGame:createArena()
		-- Create arena container
		local arena = Instance.new("Model")
		arena.Name = "CollectorArena"
		arena.Parent = Workspace
		-- Create arena floor
		local floor = Instance.new("Part")
		floor.Name = "ArenaFloor"
		floor.Material = Enum.Material.Neon
		floor.BrickColor = BrickColor.new("Bright green")
		floor.Anchored = true
		floor.CanCollide = true
		floor.Size = Vector3.new(self.config.arenaSize * 2, 1, self.config.arenaSize * 2)
		local _arenaCenter = self.config.arenaCenter
		local _vector3 = Vector3.new(0, 2, 0)
		floor.Position = _arenaCenter - _vector3
		floor.Parent = arena
		-- Create arena walls (invisible barriers)
		local wallHeight = 10
		local wallThickness = 1
		-- North wall
		local _self = self
		local _exp = Vector3.new(self.config.arenaSize * 2, wallHeight, wallThickness)
		local _arenaCenter_1 = self.config.arenaCenter
		local _vector3_1 = Vector3.new(0, wallHeight / 2, self.config.arenaSize)
		local northWall = _self:createWall(_exp, _arenaCenter_1 + _vector3_1)
		northWall.Parent = arena
		-- South wall
		local _self_1 = self
		local _exp_1 = Vector3.new(self.config.arenaSize * 2, wallHeight, wallThickness)
		local _arenaCenter_2 = self.config.arenaCenter
		local _vector3_2 = Vector3.new(0, wallHeight / 2, -self.config.arenaSize)
		local southWall = _self_1:createWall(_exp_1, _arenaCenter_2 + _vector3_2)
		southWall.Parent = arena
		-- East wall
		local _self_2 = self
		local _exp_2 = Vector3.new(wallThickness, wallHeight, self.config.arenaSize * 2)
		local _arenaCenter_3 = self.config.arenaCenter
		local _vector3_3 = Vector3.new(self.config.arenaSize, wallHeight / 2, 0)
		local eastWall = _self_2:createWall(_exp_2, _arenaCenter_3 + _vector3_3)
		eastWall.Parent = arena
		-- West wall
		local _self_3 = self
		local _exp_3 = Vector3.new(wallThickness, wallHeight, self.config.arenaSize * 2)
		local _arenaCenter_4 = self.config.arenaCenter
		local _vector3_4 = Vector3.new(-self.config.arenaSize, wallHeight / 2, 0)
		local westWall = _self_3:createWall(_exp_3, _arenaCenter_4 + _vector3_4)
		westWall.Parent = arena
		print("🏟️ [CollectorArena] Arena created")
	end
	function CollectorArenaGame:createWall(size, position)
		local wall = Instance.new("Part")
		wall.Name = "ArenaWall"
		wall.Material = Enum.Material.ForceField
		wall.BrickColor = BrickColor.new("Bright blue")
		wall.Anchored = true
		wall.CanCollide = true
		wall.Transparency = 0.8
		wall.Size = size
		wall.Position = position
		return wall
	end
	function CollectorArenaGame:startGameLoop()
		self.gameLoop = RunService.Heartbeat:Connect(function()
			if not self.gameState.isGameActive then
				return nil
			end
			-- Update timer (using a fixed delta time)
			local deltaTime = 1 / 60
			local newTimeRemaining = self.gameState.timeRemaining - deltaTime
			if newTimeRemaining <= 0 then
				-- Game over
				self:stop()
				return nil
			end
			self.gameState.timeRemaining = newTimeRemaining
			self.gameState.difficulty = math.floor(1 + (self.config.gameDuration - newTimeRemaining) / 30)
			-- Check for coin collection
			self:checkCoinCollection()
		end)
	end
	function CollectorArenaGame:startCoinSpawning()
		local lastCoinSpawn = tick()
		self.coinSpawnLoop = RunService.Heartbeat:Connect(function()
			if not self.gameState.isGameActive then
				return nil
			end
			-- ▼ ReadonlySet.size ▼
			local _size = 0
			for _ in self.spawnedCoins do
				_size += 1
			end
			-- ▲ ReadonlySet.size ▲
			if _size >= self.config.maxCoins then
				return nil
			end
			local now = tick()
			local timeSinceLastSpawn = now - lastCoinSpawn
			local spawnInterval = 1 / self.config.coinSpawnRate
			if timeSinceLastSpawn >= spawnInterval then
				self:spawnCoin()
				lastCoinSpawn = now
			end
		end)
	end
	function CollectorArenaGame:startEnemySpawning()
		local lastEnemySpawn = tick()
		self.enemySpawnLoop = RunService.Heartbeat:Connect(function()
			if not self.gameState.isGameActive then
				return nil
			end
			-- ▼ ReadonlySet.size ▼
			local _size = 0
			for _ in self.spawnedEnemies do
				_size += 1
			end
			-- ▲ ReadonlySet.size ▲
			if _size >= self.config.maxEnemies then
				return nil
			end
			local now = tick()
			local timeSinceLastSpawn = now - lastEnemySpawn
			local spawnInterval = 1 / (self.config.enemySpawnRate * self.gameState.difficulty)
			if timeSinceLastSpawn >= spawnInterval then
				self:spawnEnemy()
				lastEnemySpawn = now
			end
		end)
	end
	function CollectorArenaGame:spawnCoin()
		local spawnPosition = self:getRandomArenaPosition()
		local coinEntity = self.entityManager:spawnEntity({
			type = EntityType.Pickup,
			position = spawnPosition,
			data = {
				name = "Coin",
				value = self.config.coinValue,
				collectionDistance = 5,
			},
		})
		-- Make coin visible
		if coinEntity.instance then
			local part = coinEntity.instance
			part.Material = Enum.Material.Neon
			part.BrickColor = BrickColor.new("Bright yellow")
			part.Shape = Enum.PartType.Ball
			part.Size = Vector3.new(2, 2, 2)
			part.CanCollide = false
			part.Anchored = true
			-- Add spinning animation
			local spinConnection
			spinConnection = RunService.Heartbeat:Connect(function()
				if part.Parent then
					local _cFrame = part.CFrame
					local _arg0 = CFrame.Angles(0, 0.1, 0)
					part.CFrame = _cFrame * _arg0
				else
					spinConnection:Disconnect()
				end
			end)
		end
		local _spawnedCoins = self.spawnedCoins
		local _id = coinEntity.id
		_spawnedCoins[_id] = true
		-- Play spawn sound
		playSound("rbxasset://sounds/pickup_01.wav", 0.3)
		print(`🪙 [CollectorArena] Spawned coin at {spawnPosition}`)
	end
	function CollectorArenaGame:spawnEnemy()
		local spawnPosition = self:getRandomArenaPosition()
		local enemyEntity = self.entityManager:spawnEntity({
			type = EntityType.NPC,
			position = spawnPosition,
			data = {
				name = "Enemy",
				health = 100,
				speed = 10,
				detectionRange = 20,
				isHostile = true,
			},
		})
		-- Make enemy visible and add AI
		if enemyEntity.instance then
			local npcModel = enemyEntity.instance
			local torso = npcModel:FindFirstChild("Torso")
			if torso then
				torso.Material = Enum.Material.Neon
				torso.BrickColor = BrickColor.new("Bright red")
				torso.Size = Vector3.new(4, 4, 4)
				torso.CanCollide = true
				torso.Anchored = false
			end
			-- Add AI behavior
			self.aiController:registerAI(enemyEntity.id, {
				detectionRange = 20,
				followRange = 25,
				attackRange = 5,
				moveSpeed = 10 + (self.gameState.difficulty * 2),
				patrolRadius = 10,
				reactionTime = 0.5,
				aggroTime = 5,
				memoryDuration = 3,
			})
		end
		local _spawnedEnemies = self.spawnedEnemies
		local _id = enemyEntity.id
		_spawnedEnemies[_id] = true
		-- Play spawn sound
		playSound("rbxasset://sounds/impact_03.wav", 0.4)
		print(`👹 [CollectorArena] Spawned enemy at {spawnPosition}`)
	end
	function CollectorArenaGame:getRandomArenaPosition()
		local angle = math.random() * math.pi * 2
		local distance = math.random() * (self.config.arenaSize - 5)
		local x = self.config.arenaCenter.X + math.cos(angle) * distance
		local z = self.config.arenaCenter.Z + math.sin(angle) * distance
		local y = self.config.arenaCenter.Y
		return Vector3.new(x, y, z)
	end
	function CollectorArenaGame:checkCoinCollection()
		local character = self.player.Character
		if not character or not character:FindFirstChild("HumanoidRootPart") then
			return nil
		end
		local playerPosition = (character:FindFirstChild("HumanoidRootPart")).Position
		for coinId in self.spawnedCoins do
			local coinEntity = self.entityManager:getEntity(coinId)
			if not coinEntity or not coinEntity.instance then
				continue
			end
			local coinPosition = (coinEntity.instance).Position
			local distance = (playerPosition - coinPosition).Magnitude
			local _result = coinEntity.data
			if _result ~= nil then
				_result = _result.collectionDistance
			end
			local _condition = _result
			if not (_condition ~= 0 and _condition == _condition and _condition) then
				_condition = 5
			end
			if distance <= _condition then
				local _self = self
				local _result_1 = coinEntity.data
				if _result_1 ~= nil then
					_result_1 = _result_1.value
				end
				local _condition_1 = _result_1
				if not (_condition_1 ~= 0 and _condition_1 == _condition_1 and _condition_1) then
					_condition_1 = self.config.coinValue
				end
				_self:collectCoin(coinId, _condition_1)
			end
		end
	end
	function CollectorArenaGame:collectCoin(coinId, value)
		-- Remove coin
		self.entityManager:destroyEntity(coinId)
		local _spawnedCoins = self.spawnedCoins
		local _coinId = coinId
		_spawnedCoins[_coinId] = nil
		-- Update score
		self.gameState.score += value
		self.gameState.coinsCollected += 1
		-- Play collection effects
		local character = self.player.Character
		if character and character:FindFirstChild("HumanoidRootPart") then
			local playerPosition = (character:FindFirstChild("HumanoidRootPart")).Position
			playSound("rbxasset://sounds/pickup_01.wav", 0.6)
			createParticleExplosion(playerPosition, 10, Color3.fromRGB(255, 255, 0), { 5, 15 }, { 0.2, 0.8 })
		end
		print(`🪙 [CollectorArena] Coin collected! Score: {self.gameState.score}`)
	end
	CollectorArenaGame.saveFinalScore = TS.async(function(self)
		TS.try(function()
			local gameScore = {
				playerName = self.player.Name,
				score = self.gameState.score,
				coinsCollected = self.gameState.coinsCollected,
				enemiesDefeated = self.gameState.enemiesDefeated,
				gameTime = tick() - self.startTime,
				timestamp = tick(),
			}
			local saved = TS.await(self.scoreManager:saveScore(gameScore))
			if saved then
				print(`💾 [CollectorArena] Score saved successfully!`)
			else
				warn(`⚠️ [CollectorArena] Failed to save score`)
			end
		end, function(error)
			warn(`❌ [CollectorArena] Error saving score: {error}`)
		end)
	end)
	CollectorArenaGame.getPersonalBest = TS.async(function(self)
		return TS.await(self.scoreManager:getPersonalBest())
	end)
	CollectorArenaGame.getRecentGames = TS.async(function(self)
		return TS.await(self.scoreManager:getRecentGames())
	end)
end
return {
	CollectorArenaGame = CollectorArenaGame,
}
