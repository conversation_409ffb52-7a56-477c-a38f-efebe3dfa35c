-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local React = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "react")
--[[
	*
	 * Centralized error handling utilities for the Playground application
	 * Provides consistent error logging, recovery strategies, and user feedback
	 
]]
local ErrorHandler
do
	ErrorHandler = setmetatable({}, {
		__tostring = function()
			return "ErrorHandler"
		end,
	})
	ErrorHandler.__index = ErrorHandler
	function ErrorHandler.new(...)
		local self = setmetatable({}, ErrorHandler)
		return self:constructor(...) or self
	end
	function ErrorHandler:constructor()
		self.errorCounts = {}
		self.maxErrorCount = 5
	end
	function ErrorHandler:getInstance()
		if not <PERSON>rror<PERSON>andler.instance then
			ErrorHandler.instance = ErrorHandler.new()
		end
		return ErrorHandler.instance
	end
	function ErrorHandler:handleError(err, context, options)
		if context == nil then
			context = {}
		end
		if options == nil then
			options = {}
		end
		local errorMessage = self:formatError(err)
		local _condition = context.component
		if not (_condition ~= "" and _condition) then
			_condition = "unknown"
		end
		local _condition_1 = context.operation
		if not (_condition_1 ~= "" and _condition_1) then
			_condition_1 = "unknown"
		end
		local errorKey = `{_condition}_{_condition_1}`
		-- Track error frequency
		local _condition_2 = self.errorCounts[errorKey]
		if not (_condition_2 ~= 0 and _condition_2 == _condition_2 and _condition_2) then
			_condition_2 = 0
		end
		local currentCount = _condition_2
		local _errorCounts = self.errorCounts
		local _arg1 = currentCount + 1
		_errorCounts[errorKey] = _arg1
		-- Create detailed error log
		local logMessage = self:createErrorLog(errorMessage, context)
		-- Log based on severity (frequency-based)
		if currentCount < self.maxErrorCount then
			warn(logMessage)
		elseif currentCount == self.maxErrorCount then
			warn(`{logMessage} (This error will be suppressed from now on)`)
		end
		-- Show user-friendly toast if requested
		if options.showToast and currentCount < 3 then
			local _self = self
			local _condition_3 = context.component
			if not (_condition_3 ~= "" and _condition_3) then
				_condition_3 = "Application"
			end
			_self:showErrorToast(_condition_3, errorMessage)
		end
		-- Return fallback value if provided
		if options.fallbackValue ~= nil then
			return options.fallbackValue
		end
		return nil
	end
	function ErrorHandler:safeExecute(fn, context, options)
		if context == nil then
			context = {}
		end
		if options == nil then
			options = {}
		end
		local _exitType, _returns = TS.try(function()
			return TS.TRY_RETURN, { fn() }
		end, function(err)
			return TS.TRY_RETURN, { self:handleError(err, context, options) }
		end)
		if _exitType then
			return unpack(_returns)
		end
	end
	ErrorHandler.safeExecuteAsync = TS.async(function(self, fn, context, options)
		if context == nil then
			context = {}
		end
		if options == nil then
			options = {}
		end
		local _exitType, _returns = TS.try(function()
			return TS.TRY_RETURN, { TS.await(fn()) }
		end, function(err)
			self:handleError(err, context, options)
			if options.fallbackValue ~= nil then
				return TS.TRY_RETURN, { options.fallbackValue }
			end
			return TS.TRY_RETURN, { nil }
		end)
		if _exitType then
			return unpack(_returns)
		end
	end)
	function ErrorHandler:wrapComponent(Component, componentName)
		return function(props)
			local _exitType, _returns = TS.try(function()
				return TS.TRY_RETURN, { Component(props) }
			end, function(err)
				self:handleError(err, {
					component = componentName,
					operation = "render",
				}, {
					showToast = true,
				})
				-- Return a simple error UI using React.createElement
				return TS.TRY_RETURN, { React.createElement("frame", {
					Size = UDim2.new(1, 0, 1, 0),
					BackgroundColor3 = Color3.fromRGB(40, 40, 40),
					BackgroundTransparency = 0.5,
				}, React.createElement("textlabel", {
					Text = `Error in {componentName}`,
					Size = UDim2.new(1, 0, 1, 0),
					BackgroundTransparency = 1,
					TextColor3 = Color3.fromRGB(255, 100, 100),
					TextSize = 14,
					Font = Enum.Font.SourceSans,
					TextXAlignment = Enum.TextXAlignment.Center,
				})) }
			end)
			if _exitType then
				return unpack(_returns)
			end
		end
	end
	function ErrorHandler:clearErrorCounts()
		table.clear(self.errorCounts)
	end
	function ErrorHandler:getErrorStats()
		local entries = {}
		local _exp = self.errorCounts
		-- ▼ ReadonlyMap.forEach ▼
		local _callback = function(count, key)
			local _arg0 = { key, count }
			table.insert(entries, _arg0)
		end
		for _k, _v in _exp do
			_callback(_v, _k, _exp)
		end
		-- ▲ ReadonlyMap.forEach ▲
		return entries
	end
	function ErrorHandler:formatError(err)
		local _err = err
		if type(_err) == "string" then
			return err
		else
			local _err_1 = err
			local _condition = type(_err_1) == "table"
			if _condition then
				_condition = err.message ~= nil
			end
			if _condition then
				return tostring(err.message)
			else
				return tostring(err)
			end
		end
	end
	function ErrorHandler:createErrorLog(errorMessage, context)
		local _condition = context.timestamp
		if not (_condition ~= 0 and _condition == _condition and _condition) then
			_condition = tick()
		end
		local timestamp = _condition
		local _condition_1 = context.component
		if not (_condition_1 ~= "" and _condition_1) then
			_condition_1 = "Unknown"
		end
		local component = _condition_1
		local _condition_2 = context.operation
		if not (_condition_2 ~= "" and _condition_2) then
			_condition_2 = "Unknown"
		end
		local operation = _condition_2
		local logMessage = `❌ [{component}] Error in {operation}: {errorMessage}`
		local _value = context.userId
		if _value ~= 0 and _value == _value and _value then
			logMessage ..= ` (User: {context.userId})`
		end
		logMessage ..= ` (Time: {math.floor(timestamp)})`
		if context.additionalData then
			local dataEntries = {}
			for key, value in pairs(context.additionalData) do
				local _arg0 = `{key}={tostring(value)}`
				table.insert(dataEntries, _arg0)
			end
			logMessage ..= ` (Data: {table.concat(dataEntries, ", ")})`
		end
		return logMessage
	end
	function ErrorHandler:showErrorToast(component, message)
		-- Disable toast for now to avoid require complications
		warn(`[ErrorHandler] Toast disabled: {component} - {message}`)
	end
end
-- Export singleton instance
local GlobalErrorHandler = ErrorHandler:getInstance()
-- Utility functions for common patterns
local function safeCall(fn, component, operation, fallback)
	return GlobalErrorHandler:safeExecute(fn, {
		component = component,
		operation = operation,
	}, {
		showToast = false,
		fallbackValue = fallback,
	})
end
local function safeCallAsync(fn, component, operation, fallback)
	return GlobalErrorHandler:safeExecuteAsync(fn, {
		component = component,
		operation = operation,
	}, {
		showToast = false,
		fallbackValue = fallback,
	})
end
local function safeRender(Component, componentName)
	return GlobalErrorHandler:wrapComponent(Component, componentName)
end
return {
	safeCall = safeCall,
	safeCallAsync = safeCallAsync,
	safeRender = safeRender,
	ErrorHandler = ErrorHandler,
	GlobalErrorHandler = GlobalErrorHandler,
}
