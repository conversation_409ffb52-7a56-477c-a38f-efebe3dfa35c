import { Lighting, Workspace, TweenService } from "@rbxts/services";
import { WeatherOptions, WeatherType } from "./interfaces/WeatherOptions";
import { AtmosphereOptions } from "./interfaces/AtmosphereOptions";
import { WeatherSystem } from "../../effects/WeatherParticleHelper";
import { WeatherSoundSystem } from "../../effects/WeatherSoundHelper";
import { WeatherTransitionHelper } from "../../effects/WeatherTransitionHelper";
import { PhysicsImpactHelper } from "../physics/PhysicsImpactHelper";

/**
 * WeatherController - Advanced weather system with realistic effects
 * Provides weather effects with proper particles, sounds, and transitions
 */
export class WeatherController {
	private static instance: WeatherController;
	private currentWeather: WeatherType = "clear";
	private currentIntensity = 0.5;
	private isTransitioning = false;

	private constructor() {
		print("🌦️ Weather Controller initialized");
	}

	public static getInstance(): WeatherController {
		if (!WeatherController.instance) {
			WeatherController.instance = new WeatherController();
		}
		return WeatherController.instance;
	}

	/**
	 * Set weather with optional smooth transition
	 */
	public setWeather(options: WeatherOptions): void {
		if (this.isTransitioning) {
			print("⚠️ Weather transition already in progress, skipping");
			return;
		}

		const previousWeather = this.currentWeather;
		const transitionDuration = options.transitionDuration || 0; // Default no transition for immediate effect

		print(`🌦️ Setting weather: ${previousWeather} -> ${options.type}`);

		// For now, always apply immediately for better testing
		this.applyWeatherImmediate(options);
	}

	/**
	 * Apply weather change immediately without transition
	 */
	private applyWeatherImmediate(options: WeatherOptions): void {
		print(`🌦️ Applying weather: ${options.type}`);

		this.currentWeather = options.type;

		// Clear existing weather effects
		this.clearWeatherEffects();

		switch (options.type) {
			case "clear":
				this.setClearWeather();
				break;
			case "rain":
				this.setRainWeather(options.intensity || 0.5);
				break;
			case "heavy_rain":
				this.setRainWeather(options.intensity || 0.8);
				break;
			case "snow":
				this.setSnowWeather(options.intensity || 0.5);
				break;
			case "blizzard":
				this.setSnowWeather(options.intensity || 0.9);
				break;
			case "storm":
				this.setStormWeather(options.intensity || 0.7);
				break;
			case "thunderstorm":
				this.setThunderstormWeather(options.intensity || 0.8);
				break;
			case "fog":
				this.setFogWeather(options.intensity || 0.6);
				break;
			case "sandstorm":
				this.setSandstormWeather(options.intensity || 0.7);
				break;
		}

		print(`✅ Weather changed to: ${options.type}`);
	}

	private setClearWeather(): void {
		print("☀️ Setting clear weather");

		// Clear, bright day lighting
		Lighting.FogEnd = 100000;
		Lighting.FogStart = 0;
		Lighting.FogColor = new Color3(0.76, 0.76, 0.76);
		Lighting.Brightness = 1;
		Lighting.Ambient = new Color3(0.5, 0.5, 0.5);

		// Stop all weather effects
		WeatherSystem.clearAllWeatherEffects();
		WeatherSoundSystem.stopAllWeatherSounds();

		this.currentIntensity = 0;
		print("✅ Clear weather set");
	}

	private setRainWeather(intensity: number): void {
		print(`🌧️ Setting realistic rain weather with physics - intensity ${intensity}`);

		// Overcast and rainy lighting with realistic atmospheric conditions
		Lighting.FogEnd = math.max(500, 2000 - intensity * 1500);
		Lighting.FogStart = 0;
		Lighting.FogColor = new Color3(0.6, 0.6, 0.6);
		Lighting.Brightness = math.max(0.3, 0.8 - intensity * 0.4);
		Lighting.Ambient = new Color3(0.3, 0.3, 0.4);

		// Apply realistic rain physics using existing physics system
		// Real raindrops: 2-9 m/s terminal velocity, affected by wind and air resistance
		const windSpeed = this.calculateRealisticWindSpeed(intensity);
		const gravityMultiplier = this.calculateRainGravityEffect(intensity);

		// Create physics zones for realistic rain behavior
		if (intensity > 0.3) {
			// Create subtle downward force zones to simulate realistic rain physics
			PhysicsImpactHelper.initialize();

			// Light gravity zones that affect loose objects (papers, leaves, etc.)
			// COMPLETELY DISABLED to prevent any avatar issues - weather effects are visual only
			// PhysicsImpactHelper.createGravityZone({
			// 	zoneType: "gravity",
			// 	center: new Vector3(0, 100, 0),
			// 	radius: 1000, // Large coverage area
			// 	intensity: gravityMultiplier,
			// 	gravityMultiplier: gravityMultiplier,
			// 	gravityDirection: new Vector3(windSpeed * 0.1, -1, 0), // Slight wind effect
			// 	duration: undefined, // Permanent until weather changes
			// 	visualEffect: false, // No visual effect for subtle rain physics
			// 	affectedObjects: ["parts", "debris"], // Only affect loose objects, NOT players
			// });
		}

		// Create realistic rain particles with proper terminal velocity physics
		WeatherSystem.createRealisticRain(intensity, windSpeed);

		// Play layered rain ambient sounds with atmospheric effects
		WeatherSoundSystem.playLayeredWeatherAmbient("rain", intensity);

		this.currentIntensity = intensity;
		print(`✅ Realistic rain weather set with intensity ${intensity}, wind: ${windSpeed} m/s`);
	}

	private setSnowWeather(intensity: number): void {
		print(`❄️ Setting realistic snow weather with physics - intensity ${intensity}`);

		// Snow lighting conditions - brighter and whiter than rain
		Lighting.FogEnd = math.max(300, 1500 - intensity * 1000); // Reduced visibility in heavy snow
		Lighting.FogStart = 0;
		Lighting.FogColor = new Color3(0.9, 0.9, 0.95); // Whiter fog for snow
		Lighting.Brightness = math.min(2.0, 1.2 + intensity * 0.3); // Brighter due to snow reflection
		Lighting.Ambient = new Color3(0.7, 0.7, 0.8); // Cool, bright ambient

		// Apply realistic snow physics
		// Real snowflakes: 0.5-2 m/s terminal velocity, much slower than rain
		const snowFallSpeed = this.calculateSnowFallSpeed(intensity);
		const windSpeed = this.calculateRealisticWindSpeed(intensity) * 0.6; // Snow is more affected by wind

		// Create physics zones for realistic snow behavior
		if (intensity > 0.2) {
			PhysicsImpactHelper.initialize();

			// COMPLETELY DISABLED to prevent any avatar issues - weather effects are visual only
			// PhysicsImpactHelper.createGravityZone({
			// 	zoneType: "gravity",
			// 	center: new Vector3(0, 120, 0),
			// 	radius: 1200, // Larger coverage for snow
			// 	intensity: 0.95, // Slightly reduced gravity for floating effect
			// 	gravityMultiplier: 0.95, // Lighter than rain
			// 	gravityDirection: new Vector3(windSpeed * 0.2, -1, 0), // More wind effect
			// 	duration: undefined,
			// 	visualEffect: false,
			// 	affectedObjects: ["parts", "debris"], // Only affect loose objects, NOT players
			// });
		}

		// Create realistic snow particles with proper fall speed
		WeatherSystem.createSnowEffect(intensity, windSpeed);

		// Play layered snow ambient sounds
		WeatherSoundSystem.playLayeredWeatherAmbient("snow", intensity);

		this.currentIntensity = intensity;
		print(`✅ Realistic snow weather set with intensity ${intensity}, fall speed: ${snowFallSpeed} m/s`);
	}

	private setStormWeather(intensity: number): void {
		print(`🌪️ Setting storm weather with intensity ${intensity}`);
		this.setRainWeather(intensity); // Use rain system for now
	}

	private setThunderstormWeather(intensity: number): void {
		print(`⛈️ Setting thunderstorm weather with intensity ${intensity}`);
		this.setRainWeather(intensity); // Use rain system for now
	}

	private setFogWeather(intensity: number): void {
		print(`🌫️ Setting fog weather with intensity ${intensity}`);
		this.setRainWeather(intensity); // Use rain system for now
	}

	private setSandstormWeather(intensity: number): void {
		print(`🏜️ Setting sandstorm weather with intensity ${intensity}`);
		this.setRainWeather(intensity); // Use rain system for now
	}

	/**
	 * Get current weather type
	 */
	public getCurrentWeather(): WeatherType {
		return this.currentWeather;
	}

	/**
	 * Get current weather intensity
	 */
	public getCurrentIntensity(): number {
		return this.currentIntensity;
	}

	/**
	 * Calculate realistic wind speed based on rain intensity
	 * Light rain: 0-2 m/s, Heavy rain: 5-15 m/s
	 */
	private calculateRealisticWindSpeed(intensity: number): number {
		// Real-world wind speeds during rain
		const baseWind = 1; // Light breeze
		const maxWind = 15; // Strong wind during heavy rain
		return baseWind + intensity * (maxWind - baseWind);
	}

	/**
	 * Calculate gravity effect for rain physics
	 * Subtle effect that doesn't interfere with gameplay
	 */
	private calculateRainGravityEffect(intensity: number): number {
		// Very subtle gravity increase (1.0 = normal, 1.1 = 10% stronger)
		return 1.0 + intensity * 0.1;
	}

	/**
	 * Calculate realistic snow fall speed
	 * Snowflakes: 0.5-2 m/s terminal velocity
	 */
	private calculateSnowFallSpeed(intensity: number): number {
		const baseSpeed = 0.5; // Light snow
		const maxSpeed = 2.0; // Heavy snow
		return baseSpeed + intensity * (maxSpeed - baseSpeed);
	}

	/**
	 * Clear weather effects
	 */
	private clearWeatherEffects(): void {
		print("🧹 Clearing weather effects");
		WeatherSystem.clearAllWeatherEffects();
		WeatherSoundSystem.stopAllWeatherSounds();
		// FIXED: Clear physics zones to prevent avatar issues
		PhysicsImpactHelper.shutdown();
		PhysicsImpactHelper.initialize(); // Reinitialize for future use
	}

	/**
	 * Cleanup all weather effects
	 */
	public cleanup(): void {
		print("🧹 Cleaning up weather controller");
		this.clearWeatherEffects();
		this.setClearWeather();

		// FIXED: Ensure physics system is properly shut down
		PhysicsImpactHelper.shutdown();
	}
}
