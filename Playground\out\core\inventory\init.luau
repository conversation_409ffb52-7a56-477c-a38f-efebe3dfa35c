-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local exports = {}
local _InventoryManager = TS.import(script, game:GetService("ReplicatedStorage"), "core", "inventory", "InventoryManager")
exports.Inventory = _InventoryManager.Inventory
exports.InventoryManager = _InventoryManager.InventoryManager
exports.InventorySystem = _InventoryManager.InventorySystem
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "inventory", "types") or {} do
	exports[_k] = _v
end
return exports
